{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue?vue&type=style&index=0&id=2a8a8cf4&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue", "mtime": 1753672674026}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 首页专用导航栏样式 - 透明悬浮设计 */\n.home-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 1000;\n  padding: 1.25rem 0;\n  transition: all 0.3s ease;\n  background: transparent;\n  backdrop-filter: none;\n  border-bottom: none;\n  box-shadow: none;\n}\n\n.home-navbar.scrolled {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  box-shadow: 0 4px 30px rgba(59, 130, 246, 0.12);\n  padding: 1rem 0;\n}\n\n.nav-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 品牌Logo区域 - 增加尺寸和阴影效果 */\n.nav-brand {\n  display: flex;\n  align-items: center;\n  gap: 0.875rem;\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #ffffff;\n  text-decoration: none;\n  transition: all 0.3s ease;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n.nav-brand:hover {\n  transform: translateY(-2px);\n}\n\n/* 滚动后的品牌样式 */\n.home-navbar.scrolled .nav-brand {\n  color: #1e293b;\n  text-shadow: none;\n}\n\n.brand-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.4rem;\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n}\n\n.brand-icon:hover {\n  transform: scale(1.05);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);\n}\n\n.brand-text {\n  color: #ffffff;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n  transition: all 0.3s ease;\n}\n\n/* 滚动后的品牌文字样式 */\n.home-navbar.scrolled .brand-text {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-shadow: none;\n}\n\n/* 导航菜单 - 确保单行显示 */\n.nav-menu {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: nowrap;\n  white-space: nowrap;\n}\n\n.nav-link {\n  color: rgba(255, 255, 255, 0.9);\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  padding: 0.875rem 1.125rem;\n  border-radius: 8px;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.nav-link:hover {\n  color: #ffffff;\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n  backdrop-filter: blur(10px);\n}\n\n/* 滚动后的导航链接样式 */\n.home-navbar.scrolled .nav-link {\n  color: rgba(30, 41, 59, 0.8);\n  text-shadow: none;\n}\n\n.home-navbar.scrolled .nav-link:hover {\n  color: #3b82f6;\n  background: rgba(59, 130, 246, 0.08);\n  backdrop-filter: none;\n}\n\n.nav-link::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  width: 0;\n  height: 2px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  transition: all 0.3s ease;\n  transform: translateX(-50%);\n}\n\n.nav-link:hover::after {\n  width: 80%;\n}\n\n/* 导航图标样式 */\n.nav-icon {\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.nav-text {\n  transition: all 0.3s ease;\n}\n\n.nav-link:hover .nav-icon {\n  color: #ffffff;\n  transform: scale(1.1);\n}\n\n.nav-link:hover .nav-text {\n  color: #ffffff;\n}\n\n/* 滚动后的导航图标和文字悬停样式 */\n.home-navbar.scrolled .nav-link:hover .nav-icon {\n  color: #3b82f6;\n}\n\n.home-navbar.scrolled .nav-link:hover .nav-text {\n  color: #3b82f6;\n}\n\n/* 右侧操作区 - 简化设计 */\n.nav-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.btn-secondary {\n  padding: 0.875rem 1.75rem;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: #ffffff;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.btn-secondary:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n/* 滚动后的按钮样式 */\n.home-navbar.scrolled .btn-secondary {\n  background: transparent;\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n  backdrop-filter: none;\n  text-shadow: none;\n}\n\n.home-navbar.scrolled .btn-secondary:hover {\n  background: rgba(59, 130, 246, 0.1);\n  border-color: rgba(59, 130, 246, 0.5);\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);\n}\n\n.mobile-menu-btn {\n  display: none;\n  background: rgba(255, 255, 255, 0.1);\n  border: none;\n  color: #ffffff;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.mobile-menu-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  color: #ffffff;\n}\n\n/* 滚动后的移动端按钮样式 */\n.home-navbar.scrolled .mobile-menu-btn {\n  background: none;\n  color: #1e293b;\n  backdrop-filter: none;\n  text-shadow: none;\n}\n\n.home-navbar.scrolled .mobile-menu-btn:hover {\n  background: rgba(59, 130, 246, 0.1);\n  color: #3b82f6;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .nav-container {\n    padding: 0 1.5rem;\n  }\n  \n  .nav-menu {\n    gap: 0.3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .nav-menu,\n  .nav-actions {\n    display: none;\n  }\n  \n  .mobile-menu-btn {\n    display: flex;\n  }\n  \n  .nav-container {\n    padding: 0 1rem;\n  }\n}\n", null]}