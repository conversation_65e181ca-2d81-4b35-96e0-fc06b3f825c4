{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue?vue&type=template&id=058e2d9c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue", "mtime": 1753756307330}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"payment-success-page\">\n  <div class=\"success-container\">\n    <!-- 成功图标 -->\n    <div class=\"success-icon\">\n      <a-icon type=\"check-circle\" theme=\"filled\" />\n    </div>\n    \n    <!-- 成功标题 -->\n    <h1 class=\"success-title\">支付成功！</h1>\n    \n    <!-- 订单信息 -->\n    <div class=\"order-info\" v-if=\"orderInfo\">\n      <div class=\"info-item\">\n        <span class=\"label\">订单号：</span>\n        <span class=\"value\">{{ orderInfo.orderId }}</span>\n      </div>\n      <div class=\"info-item\" v-if=\"orderInfo.amount\">\n        <span class=\"label\">支付金额：</span>\n        <span class=\"value amount\">¥{{ orderInfo.amount }}</span>\n      </div>\n      <div class=\"info-item\">\n        <span class=\"label\">支付时间：</span>\n        <span class=\"value\">{{ formatTime(new Date()) }}</span>\n      </div>\n    </div>\n    \n    <!-- 成功消息 -->\n    <div class=\"success-message\">\n      <p>您的充值已成功完成，余额将在几分钟内到账。</p>\n      <p>感谢您对智界AIGC的支持！</p>\n    </div>\n    \n    <!-- 操作按钮 -->\n    <div class=\"action-buttons\">\n      <a-button type=\"primary\" size=\"large\" @click=\"goToUserCenter\">\n        查看余额\n      </a-button>\n      <a-button size=\"large\" @click=\"goToMarket\" style=\"margin-left: 16px\">\n        去购买插件\n      </a-button>\n      <a-button size=\"large\" @click=\"goHome\" style=\"margin-left: 16px\">\n        返回首页\n      </a-button>\n    </div>\n    \n    <!-- 温馨提示 -->\n    <div class=\"tips\">\n      <a-alert\n        message=\"温馨提示\"\n        description=\"如果余额未及时到账，请联系客服或查看交易记录。\"\n        type=\"info\"\n        show-icon\n      />\n    </div>\n  </div>\n</div>\n", null]}