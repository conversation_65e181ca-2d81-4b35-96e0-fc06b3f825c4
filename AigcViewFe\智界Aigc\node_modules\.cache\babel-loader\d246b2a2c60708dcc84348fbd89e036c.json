{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\layout\\Header.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\layout\\Header.vue", "mtime": 1753672716010}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { gsap } from 'gsap';\nimport LogoImage from '@/components/common/LogoImage.vue';\nexport default {\n  name: 'Header',\n  components: {\n    LogoImage: LogoImage\n  },\n  data: function data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [{\n        name: '首页',\n        href: '#home',\n        icon: 'home'\n      }, {\n        name: '商城',\n        href: '#market',\n        icon: 'shop'\n      }, {\n        name: '客户案例',\n        href: '#cases',\n        icon: 'trophy'\n      }, {\n        name: '教程中心',\n        href: '#tutorials',\n        icon: 'book'\n      }, {\n        name: '签到奖励',\n        href: '#signin',\n        icon: 'gift'\n      }, {\n        name: '订阅会员',\n        href: '#membership',\n        icon: 'crown'\n      }, {\n        name: '邀请奖励',\n        href: '#affiliate',\n        icon: 'team'\n      }, {\n        name: '个人中心',\n        href: '#usercenter',\n        icon: 'user'\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.initScrollListener();\n    this.initNavbarAnimations();\n  },\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    initScrollListener: function initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll);\n    },\n    handleScroll: function handleScroll() {\n      this.isScrolled = window.scrollY > 50;\n    },\n    toggleMobileMenu: function toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n    },\n    initNavbarAnimations: function initNavbarAnimations() {\n      // 导航栏快速入场动画 - 同时出现\n      gsap.from([this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions], {\n        duration: 0.4,\n        y: -20,\n        opacity: 0,\n        ease: \"power2.out\",\n        stagger: 0.05 // 几乎同时出现\n\n      });\n    }\n  }\n};", null]}