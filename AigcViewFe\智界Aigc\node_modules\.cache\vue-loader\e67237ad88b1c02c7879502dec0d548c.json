{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=style&index=0&id=a1183866&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.affiliate-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0 0 1.5rem 0;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: white;\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.badge-level {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 分销内容区域 */\n.affiliate-section {\n  padding: 0 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 邀请工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 提现弹窗 */\n.withdraw-modal-content {\n  padding: 8px 0;\n}\n\n.withdraw-info {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 24px;\n  border: 1px solid #e2e8f0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.info-value {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.withdraw-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #f1f5f9;\n}\n\n.card-action {\n  margin-top: 8px;\n}\n\n/* 邀请链接区域 */\n.promotion-link-section {\n  background: white;\n  border-radius: 20px;\n  padding: 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 2px solid #e2e8f0;\n}\n\n.link-main-container {\n  margin-bottom: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.link-input-large {\n  flex: 1;\n}\n\n.link-input-large .ant-input {\n  font-size: 1.1rem;\n  padding: 16px 20px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  background: #f8fafc;\n  transition: all 0.3s ease;\n}\n\n.link-input-large .ant-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.link-actions {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.copy-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border: none;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.copy-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);\n  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n}\n\n.copy-btn:active {\n  transform: translateY(0);\n}\n\n.qr-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.qr-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n}\n\n.qr-btn:active {\n  transform: translateY(0);\n}\n\n/* 按钮禁用状态 */\n.copy-btn:disabled,\n.qr-btn:disabled {\n  background: #e5e7eb !important;\n  color: #9ca3af !important;\n  box-shadow: none !important;\n  transform: none !important;\n  cursor: not-allowed !important;\n}\n\n.copy-btn:disabled:hover,\n.qr-btn:disabled:hover {\n  background: #e5e7eb !important;\n  transform: none !important;\n  box-shadow: none !important;\n}\n\n.link-tips {\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: #0369a1;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.link-tips strong {\n  color: #1e40af;\n  font-weight: 700;\n}\n\n/* 分成规则表格 */\n.commission-rules {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.rules-table {\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.rule-row:last-child {\n  border-bottom: none;\n}\n\n.rule-row.header {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.rule-row.vip {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  color: #475569;\n  font-weight: 500;\n  border: 1px solid #94a3b8;\n  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.2);\n}\n\n.rule-row.svip {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);\n  color: #92400e;\n  font-weight: 600;\n  border: 1px solid #d97706;\n  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);\n}\n\n.rule-cell {\n  padding: 16px;\n  text-align: center;\n  border-right: 1px solid #e2e8f0;\n}\n\n.rule-cell:last-child {\n  border-right: none;\n}\n\n.rule-cell.highlight {\n  font-weight: 700;\n  color: #dc2626;\n  font-size: 1.1rem;\n}\n\n/* 表格容器 */\n.users-table-container,\n.records-table-container {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.referral-users,\n.withdraw-records {\n  margin-bottom: 3rem;\n}\n\n.reward-amount,\n.withdraw-amount {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n\n  .promotion-link-section {\n    padding: 1.5rem;\n    margin-bottom: 2rem;\n  }\n\n  .link-main-container {\n    gap: 1rem;\n  }\n\n  .link-input-large .ant-input {\n    font-size: 1rem;\n    padding: 14px 16px;\n  }\n\n  .link-actions {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .copy-btn,\n  .qr-btn {\n    width: 100%;\n    min-width: auto;\n    padding: 14px 24px;\n    font-size: 15px;\n  }\n\n  .rule-row {\n    grid-template-columns: 1fr;\n    text-align: left;\n  }\n\n  .rule-cell {\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    text-align: left;\n  }\n\n  .rule-cell:last-child {\n    border-bottom: none;\n  }\n\n  .promotion-link-section,\n  .commission-rules,\n  .users-table-container,\n  .records-table-container {\n    padding: 1.5rem;\n  }\n}\n\n/* 水平等级时间线样式 */\n.level-timeline-horizontal {\n  position: relative;\n  padding: 20px 0;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.level-step-horizontal {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  flex: 1;\n  text-align: center;\n  min-width: 0;\n  padding: 0 10px;\n}\n\n.step-circle-horizontal {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  z-index: 2;\n  border: 2px solid #e8e8e8;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.level-step-horizontal.completed .step-circle-horizontal {\n  background-color: #52c41a;\n  border-color: #52c41a;\n  color: white;\n}\n\n.level-step-horizontal.current .step-circle-horizontal {\n  background-color: #1890ff;\n  border-color: #1890ff;\n  color: white;\n}\n\n.level-step-horizontal.upcoming .step-circle-horizontal {\n  border-color: #d9d9d9;\n}\n\n.step-circle-horizontal .step-number {\n  font-size: 14px;\n  font-weight: 600;\n  color: #666;\n}\n\n.step-content-horizontal {\n  padding: 0 5px;\n  max-width: 120px;\n}\n\n.step-content-horizontal .step-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n  white-space: nowrap;\n}\n\n.step-content-horizontal .step-rate {\n  color: #1890ff;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.level-step-horizontal.completed .step-content-horizontal .step-rate {\n  color: #52c41a;\n}\n\n.step-content-horizontal .step-requirement {\n  color: #666;\n  margin-bottom: 4px;\n  font-size: 13px;\n}\n\n.step-content-horizontal .step-remaining {\n  color: #ff4d4f;\n  font-size: 12px;\n}\n\n.step-content-horizontal .step-completed {\n  color: #52c41a;\n  font-size: 12px;\n}\n\n.step-line-horizontal {\n  position: absolute;\n  left: calc(50% + 18px);\n  width: calc(100% - 36px);\n  top: 18px;\n  height: 2px;\n  background-color: #e8e8e8;\n  z-index: 1;\n}\n\n.level-step-horizontal.completed .step-line-horizontal {\n  background-color: #52c41a;\n}\n\n.level-step-horizontal:last-child .step-line-horizontal {\n  display: none;\n}\n", {"version": 3, "sources": ["Affiliate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsjDA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "Affiliate.vue", "sourceRoot": "src/views/website/affiliate", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"affiliate-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">邀请奖励</h1>\n        <p class=\"simple-subtitle\">邀请好友注册智界AIGC，获得丰厚奖励</p>\n        <div class=\"commission-badge\">\n          <span class=\"badge-text\">当前奖励比例：{{ currentCommissionRate }}%</span>\n          <span class=\"badge-level\">{{ commissionLevelText }}</span>\n        </div>\n      </div>\n\n      <!-- 分销内容区域 -->\n      <section class=\"affiliate-section\">\n        <div class=\"container\">\n          <!-- 邀请链接区域 - 最显眼位置 -->\n          <div class=\"promotion-link-section\">\n            <h2 class=\"section-title\">您的专属邀请链接</h2>\n            <div class=\"link-main-container\">\n              <div class=\"link-input-large\">\n                <a-input\n                  :value=\"affiliateLink || '正在生成邀请链接...'\"\n                  readonly\n                  :loading=\"loading\"\n                  size=\"large\"\n                  placeholder=\"邀请链接生成中...\"\n                />\n              </div>\n              <div class=\"link-actions\">\n                <a-button\n                  type=\"primary\"\n                  size=\"large\"\n                  :disabled=\"!affiliateLink || loading\"\n                  @click=\"copyLink\"\n                  class=\"copy-btn\"\n                >\n                  <a-icon type=\"copy\" />\n                  复制链接\n                </a-button>\n                <a-button\n                  size=\"large\"\n                  :loading=\"qrLoading\"\n                  @click=\"generateQRCode\"\n                  class=\"qr-btn\"\n                >\n                  <a-icon type=\"qrcode\" />\n                  邀请二维码\n                </a-button>\n              </div>\n            </div>\n            <div class=\"link-tips\">\n              <a-icon type=\"info-circle\" />\n              分享此链接，您将获得好友付费的 <strong>{{ currentCommissionRate }}%</strong> 奖励\n            </div>\n          </div>\n\n          <!-- 收益展示 -->\n          <div class=\"earnings-dashboard\">\n            <h2 class=\"section-title\">收益概览</h2>\n            <div class=\"earnings-grid\">\n              <div class=\"earning-card primary\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"dollar\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                    <div class=\"earning-label\">累计收益</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card success\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"wallet\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                    <div class=\"earning-label\">可提现金额</div>\n                  </a-spin>\n                  <div class=\"card-action\">\n                    <a-button\n                      type=\"primary\"\n                      size=\"small\"\n                      :disabled=\"availableEarnings <= 0 || loading\"\n                      @click=\"openWithdrawModal\"\n                    >\n                      立即提现\n                    </a-button>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"earning-card info\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"team\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ Math.floor(totalReferrals) }}</div>\n                    <div class=\"earning-label\">邀请注册人数</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card warning\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"crown\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ Math.floor(memberReferrals) }}</div>\n                    <div class=\"earning-label\">转化人数</div>\n                  </a-spin>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 奖励等级进度 -->\n          <div class=\"commission-progress\">\n            <h2 class=\"section-title\">奖励等级进度</h2>\n            <div class=\"progress-card\">\n              <!-- 一行显示所有等级 -->\n              <div class=\"level-timeline-horizontal\">\n                <div\n                  v-for=\"(level, index) in commissionLevels\"\n                  :key=\"index\"\n                  class=\"level-step-horizontal\"\n                  :class=\"{\n                    'current': level.isCurrent,\n                    'completed': level.isCompleted,\n                    'upcoming': level.isUpcoming\n                  }\"\n                >\n                  <div class=\"step-circle-horizontal\">\n                    <a-icon v-if=\"level.isCompleted\" type=\"check\" />\n                    <span v-else-if=\"level.isCurrent\" class=\"current-dot\"></span>\n                    <span v-else class=\"step-number\">{{ index + 1 }}</span>\n                  </div>\n                  <div class=\"step-content-horizontal\">\n                    <div class=\"step-title\">{{ level.name }}</div>\n                    <div class=\"step-rate\">{{ level.rate }}%</div>\n                    <div class=\"step-requirement\">{{ level.requirement }}人</div>\n                    <div v-if=\"level.remaining > 0\" class=\"step-remaining\">\n                      还需{{ level.remaining }}个\n                    </div>\n                    <div v-else-if=\"level.isCompleted\" class=\"step-completed\">\n                      已达成\n                    </div>\n                  </div>\n                  <!-- 连接线 -->\n                  <div v-if=\"index < commissionLevels.length - 1\" class=\"step-line-horizontal\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 奖励规则说明 -->\n          <div class=\"commission-rules\">\n            <h2 class=\"section-title\">奖励规则</h2>\n            <div class=\"rules-table\">\n              <div class=\"rule-row header\">\n                <div class=\"rule-cell\">用户等级</div>\n                <div class=\"rule-cell\">邀请人数要求</div>\n                <div class=\"rule-cell\">奖励比例</div>\n                <div class=\"rule-cell\">说明</div>\n              </div>\n              <a-spin :spinning=\"loading\" size=\"small\">\n                <div\n                  v-for=\"config in allLevelConfigs\"\n                  :key=\"config.id\"\n                  class=\"rule-row\"\n                  :class=\"{\n                    'vip': config.role_code === 'VIP',\n                    'svip': config.role_code === 'SVIP'\n                  }\"\n                >\n                  <div class=\"rule-cell\">{{ getRoleDisplayName(config.role_code) }}</div>\n                  <div class=\"rule-cell\">{{ getRequirementText(config) }}</div>\n                  <div class=\"rule-cell highlight\">{{ config.commission_rate }}%</div>\n                  <div class=\"rule-cell\">{{ config.level_name }}</div>\n                </div>\n              </a-spin>\n            </div>\n          </div>\n\n          <!-- 邀请用户列表 -->\n          <div class=\"referral-users\">\n            <h2 class=\"section-title\">我的邀请用户</h2>\n            <div class=\"users-table-container\">\n              <a-table\n                :columns=\"userColumns\"\n                :data-source=\"referralUsers\"\n                :loading=\"usersLoading\"\n                :pagination=\"usersPagination\"\n                size=\"middle\"\n                @change=\"handleUsersTableChange\"\n              >\n                <template slot=\"avatar\" slot-scope=\"text, record\">\n                  <a-avatar :src=\"getAvatarUrl(record.avatar)\" :style=\"{ backgroundColor: '#87d068' }\">\n                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                  </a-avatar>\n                </template>\n                <template slot=\"reward\" slot-scope=\"text\">\n                  <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n\n          <!-- 提现记录 -->\n          <div class=\"withdraw-records\">\n            <h2 class=\"section-title\">提现记录</h2>\n\n            <!-- 筛选区域 -->\n            <div class=\"filter-section\" style=\"margin-bottom: 16px; padding: 16px; background: #fafafa; border-radius: 6px;\">\n              <a-row :gutter=\"16\">\n                <a-col :span=\"5\">\n                  <a-form-item label=\"提现金额\">\n                    <a-input-group compact>\n                      <a-input-number\n                        v-model=\"withdrawFilter.minAmount\"\n                        placeholder=\"最小金额\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        style=\"width: 50%\"\n                      />\n                      <a-input-number\n                        v-model=\"withdrawFilter.maxAmount\"\n                        placeholder=\"最大金额\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        style=\"width: 50%\"\n                      />\n                    </a-input-group>\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\"申请时间\">\n                    <a-range-picker\n                      v-model=\"withdrawFilter.dateRange\"\n                      format=\"YYYY-MM-DD\"\n                      placeholder=\"['开始日期', '结束日期']\"\n                      style=\"width: 100%\"\n                    />\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"4\">\n                  <a-form-item label=\"状态\">\n                    <a-select\n                      v-model=\"withdrawFilter.status\"\n                      placeholder=\"选择状态\"\n                      style=\"width: 100%\"\n                    >\n                      <a-select-option :value=\"null\">全部</a-select-option>\n                      <a-select-option :value=\"1\">待审核</a-select-option>\n                      <a-select-option :value=\"2\">已发放</a-select-option>\n                      <a-select-option :value=\"3\">审核拒绝</a-select-option>\n                      <a-select-option :value=\"4\">已取消</a-select-option>\n                    </a-select>\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\"完成时间\">\n                    <a-range-picker\n                      v-model=\"withdrawFilter.completeDateRange\"\n                      format=\"YYYY-MM-DD\"\n                      placeholder=\"['开始日期', '结束日期']\"\n                      style=\"width: 100%\"\n                    />\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\" \">\n                    <a-button type=\"primary\" @click=\"handleWithdrawFilter\" :loading=\"recordsLoading\" style=\"margin-right: 8px;\">\n                      搜索\n                    </a-button>\n                    <a-button @click=\"handleWithdrawReset\">重置</a-button>\n                  </a-form-item>\n                </a-col>\n              </a-row>\n            </div>\n\n            <div class=\"records-table-container\">\n              <a-table\n                :columns=\"withdrawColumns\"\n                :data-source=\"withdrawRecords\"\n                :loading=\"recordsLoading\"\n                :pagination=\"withdrawPagination\"\n                size=\"middle\"\n                @change=\"handleWithdrawTableChange\"\n              >\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"getStatusColor(text)\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"amount\" slot-scope=\"text\">\n                  <span class=\"withdraw-amount\">¥{{ text }}</span>\n                </template>\n                <template slot=\"action\" slot-scope=\"text, record\">\n                  <a-button\n                    v-if=\"record.rawStatus === 1\"\n                    type=\"danger\"\n                    size=\"small\"\n                    :loading=\"cancelLoading\"\n                    @click=\"handleCancelWithdraw(record)\"\n                  >\n                    取消提现\n                  </a-button>\n                  <span v-else>-</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 二维码弹窗 -->\n      <a-modal\n        v-model=\"showQRModal\"\n        title=\"邀请二维码\"\n        :footer=\"null\"\n        width=\"400px\"\n        centered\n      >\n        <div class=\"qr-modal-content\">\n          <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n            <img :src=\"qrCodeUrl\" alt=\"邀请二维码\" class=\"qr-code-image\" />\n          </div>\n          <div class=\"qr-actions\">\n            <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n              <a-icon type=\"download\" />\n              下载二维码\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n\n      <!-- 提现弹窗 -->\n      <a-modal\n        v-model=\"showWithdrawModal\"\n        title=\"申请提现\"\n        :footer=\"null\"\n        width=\"500px\"\n        centered\n      >\n        <div class=\"withdraw-modal-content\">\n          <div class=\"withdraw-info\">\n            <div class=\"info-item\">\n              <span class=\"info-label\">可提现金额：</span>\n              <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">最低提现金额：</span>\n              <span class=\"info-value\">¥50.00</span>\n            </div>\n          </div>\n\n          <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n            <a-form-item label=\"提现金额\">\n              <a-input-number\n                v-decorator=\"['amount', {\n                  rules: [\n                    { required: true, message: '请输入提现金额' },\n                    { type: 'number', min: 50, message: '最低提现金额为50元' },\n                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                  ]\n                }]\"\n                :min=\"50\"\n                :max=\"availableEarnings\"\n                :precision=\"2\"\n                style=\"width: 100%\"\n                placeholder=\"请输入提现金额\"\n              >\n                <template slot=\"addonAfter\">元</template>\n              </a-input-number>\n            </a-form-item>\n\n            <a-form-item label=\"提现方式\">\n              <a-select\n                v-decorator=\"['method', {\n                  rules: [{ required: true, message: '请选择提现方式' }],\n                  initialValue: 'alipay'\n                }]\"\n                placeholder=\"请选择提现方式\"\n                disabled\n              >\n                <a-select-option value=\"alipay\">支付宝</a-select-option>\n              </a-select>\n            </a-form-item>\n\n            <a-form-item label=\"支付宝手机号\">\n              <a-input\n                v-decorator=\"['alipayAccount', {\n                  rules: [\n                    { required: true, message: '请输入支付宝手机号' },\n                    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                  ]\n                }]\"\n                placeholder=\"请输入支付宝手机号\"\n              />\n            </a-form-item>\n\n            <a-form-item label=\"收款人真实姓名\">\n              <a-input\n                v-decorator=\"['realName', {\n                  rules: [\n                    { required: true, message: '请输入收款人真实姓名' },\n                    { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                  ]\n                }]\"\n                placeholder=\"请输入收款人真实姓名\"\n              />\n            </a-form-item>\n          </a-form>\n\n          <div class=\"withdraw-actions\">\n            <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n              取消\n            </a-button>\n            <a-button\n              type=\"primary\"\n              :loading=\"withdrawLoading\"\n              @click=\"handleWithdraw\"\n            >\n              申请提现\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink, getUserRole, getLevelConfig } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n\n      // 邀请链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'user', // user, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手邀请员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级邀请员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 佣金等级配置\n      commissionLevels: [],\n      allLevelConfigs: [], // 从数据库获取的完整等级配置\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false, // 是否已预生成二维码\n\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n\n      // 邀请用户列表\n      referralUsers: [],\n      usersLoading: false,\n      usersPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n      },\n      // 邀请用户排序\n      usersSort: {\n        orderBy: 'total_reward',\n        order: 'desc'\n      },\n      defaultAvatar: '/default-avatar.png', // 本地降级头像\n\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      cancelLoading: false,\n      withdrawPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n      },\n      // 提现记录排序\n      withdrawSort: {\n        orderBy: 'apply_time',\n        order: 'desc'\n      },\n      // 提现记录筛选\n      withdrawFilter: {\n        minAmount: null,\n        maxAmount: null,\n        status: null, // null表示\"全部\"\n        dateRange: [],\n        completeDateRange: []\n      },\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  computed: {\n    // 动态计算表格列配置，确保排序状态响应式更新\n    userColumns() {\n      return [\n        {\n          title: '头像',\n          dataIndex: 'avatar',\n          key: 'avatar',\n          scopedSlots: { customRender: 'avatar' },\n          width: 80\n        },\n        {\n          title: '用户昵称',\n          dataIndex: 'nickname',\n          key: 'nickname',\n          sorter: true\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime',\n          sorter: true\n        },\n        {\n          title: '获得奖励',\n          dataIndex: 'reward',\n          key: 'reward',\n          scopedSlots: { customRender: 'reward' },\n          sorter: true\n        }\n      ]\n    },\n\n    // 动态计算提现记录表格列配置，确保排序状态响应式更新\n    withdrawColumns() {\n      return [\n        {\n          title: '提现金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          scopedSlots: { customRender: 'amount' },\n          sorter: true\n        },\n        {\n          title: '提现方式',\n          dataIndex: 'method',\n          key: 'method'\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'applyTime',\n          key: 'applyTime',\n          sorter: true\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' },\n          sorter: true\n        },\n        {\n          title: '完成时间',\n          dataIndex: 'completeTime',\n          key: 'completeTime',\n          sorter: true\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 100,\n          scopedSlots: { customRender: 'action' }\n        }\n      ]\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 获取头像URL（处理CDN路径和默认头像）\n    getAvatarUrl(avatar) {\n      if (!avatar) {\n        return this.defaultAvatar\n      }\n\n      // 如果是完整的URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是相对路径，使用getFileAccessHttpUrl转换\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar\n    },\n\n    // 处理文件访问URL（和其他组件保持一致）\n    getFileAccessHttpUrl(avatar) {\n      if (!avatar) return this.defaultAvatar\n\n      // 如果已经是完整URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是TOS文件，使用全局方法\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar\n      }\n\n      // 本地文件，使用静态域名\n      const staticDomain = this.$store.state.app.staticDomainURL\n      return staticDomain ? `${staticDomain}/${avatar}` : avatar\n    },\n\n    // 加载TOS默认头像URL\n    async loadDefaultAvatar() {\n      try {\n        const response = await this.$http.get('/sys/common/default-avatar-url')\n        if (response && response.success && response.result) {\n          this.defaultAvatar = response.result\n          console.log('🎯 Affiliate: 已加载TOS默认头像:', this.defaultAvatar)\n        }\n      } catch (error) {\n        console.warn('⚠️ Affiliate: 获取TOS默认头像失败，使用本地降级:', error)\n        // 保持本地默认头像作为降级方案\n      }\n    },\n\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole(),\n          this.loadLevelConfig(),\n          this.loadReferralUsers(),\n          this.loadWithdrawRecords(),\n          this.loadDefaultAvatar()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n\n        // 自动预生成邀请二维码\n        this.preGenerateQRCode()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.availableEarnings = data.available_rewards || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        const response = await getUserRole()\n        if (response.success) {\n          this.userRole = response.result.role_code || 'user'\n        }\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'user'\n      }\n    },\n\n    // 加载等级配置信息\n    async loadLevelConfig() {\n      try {\n        const response = await getLevelConfig()\n        if (response.success) {\n          this.allLevelConfigs = response.result || []\n        }\n      } catch (error) {\n        console.error('获取等级配置失败:', error)\n        this.allLevelConfigs = []\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      // 从数据库配置中获取当前用户角色的等级配置\n      const userLevelConfigs = this.allLevelConfigs.filter(config => config.role_code === this.userRole)\n\n      if (userLevelConfigs.length === 0) {\n        console.warn('未找到用户角色的等级配置:', this.userRole)\n        return\n      }\n\n      // 根据邀请人数确定当前等级\n      let currentLevel = null\n      for (let i = userLevelConfigs.length - 1; i >= 0; i--) {\n        if (memberCount >= userLevelConfigs[i].min_referrals) {\n          currentLevel = userLevelConfigs[i]\n          break\n        }\n      }\n\n      if (!currentLevel) {\n        currentLevel = userLevelConfigs[0] // 默认最低等级\n      }\n\n      // 设置当前等级信息\n      this.currentCommissionRate = parseFloat(currentLevel.commission_rate)\n      this.commissionLevelText = currentLevel.level_name\n\n      // 查找下一个等级\n      const nextLevel = userLevelConfigs.find(config => config.min_referrals > memberCount)\n\n      if (nextLevel) {\n        this.nextLevelRequirement = nextLevel.min_referrals\n        this.nextLevelText = nextLevel.level_name\n        this.nextLevelRate = parseFloat(nextLevel.commission_rate)\n        this.levelProgress = (memberCount / nextLevel.min_referrals) * 100\n        this.progressColor = '#1890ff'\n      } else {\n        // 已达最高等级\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = this.currentCommissionRate\n        this.levelProgress = 100\n        this.progressColor = '#722ed1'\n      }\n\n      // 生成等级进度显示数据\n      this.commissionLevels = userLevelConfigs.map((config, index) => {\n        const isCompleted = memberCount >= config.min_referrals\n\n        // 判断当前等级：如果不是已完成，且满足前一个等级的要求，则为当前等级\n        let isCurrent = false\n        if (!isCompleted) {\n          if (index === 0) {\n            // 第一个等级，如果没完成就是当前等级\n            isCurrent = true\n          } else {\n            // 其他等级，如果满足前一个等级要求但不满足当前等级要求，则为当前等级\n            const prevRequirement = userLevelConfigs[index - 1].min_referrals\n            isCurrent = memberCount >= prevRequirement\n          }\n        }\n\n        const isUpcoming = !isCompleted && !isCurrent\n\n        let remaining = 0\n        if (!isCompleted) {\n          remaining = config.min_referrals - memberCount\n        }\n\n        return {\n          name: config.level_name,\n          rate: parseFloat(config.commission_rate),\n          requirement: config.min_referrals,\n          isCompleted,\n          isCurrent,\n          isUpcoming,\n          remaining: remaining > 0 ? remaining : 0\n        }\n      })\n    },\n\n     // 复制邀请链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '邀请链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '邀请链接已复制',\n          description: '邀请链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制邀请链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 预生成邀请二维码（后台静默生成）\n    async preGenerateQRCode() {\n      if (!this.affiliateLink || this.qrPreGenerated) {\n        return\n      }\n\n      try {\n        console.log('开始预生成邀请二维码...')\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        if (response && response.success) {\n          // 静默保存二维码URL\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          console.log('邀请二维码预生成成功:', this.qrCodeUrl)\n        }\n      } catch (error) {\n        console.error('预生成二维码失败:', error)\n        // 预生成失败不显示错误提示，用户点击时再重试\n      }\n    },\n\n    // 生成邀请二维码（用户主动点击）\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '请等待邀请链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 如果已经预生成，直接显示\n      if (this.qrPreGenerated && this.qrCodeUrl) {\n        this.showQRModal = true\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        console.log('二维码生成响应:', response)\n\n        if (response && response.success) {\n          // 使用CDN地址\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          this.showQRModal = true\n\n          this.$notification.success({\n            message: '二维码生成成功',\n            description: '邀请二维码已生成并存储到CDN，可以下载保存',\n            placement: 'topRight'\n          })\n        } else {\n          const errorMsg = (response && response.message) || '生成失败'\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: error.message || '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      try {\n        // 从邀请链接中提取邀请码\n        const referralCode = this.extractReferralCode(this.affiliateLink)\n\n        // 通过后端代理下载，避免CORS问题\n        const backendUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/jeecg-boot'\n        const downloadUrl = `${backendUrl}/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}&code=${referralCode}&t=${Date.now()}`\n\n        console.log('下载URL:', downloadUrl)\n\n        // 使用隐藏iframe下载，避免页面跳动\n        const iframe = document.createElement('iframe')\n        iframe.style.display = 'none'\n        iframe.style.position = 'absolute'\n        iframe.style.left = '-9999px'\n        iframe.src = downloadUrl\n        document.body.appendChild(iframe)\n\n        // 3秒后移除iframe\n        setTimeout(() => {\n          if (iframe.parentNode) {\n            document.body.removeChild(iframe)\n          }\n        }, 3000)\n\n        this.$notification.success({\n          message: '下载开始',\n          description: `邀请二维码_${referralCode}.png 正在下载`,\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('下载二维码失败:', error)\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 从邀请链接中提取邀请码\n    extractReferralCode(url) {\n      if (!url) return 'UNKNOWN'\n\n      try {\n        const urlObj = new URL(url)\n        const refParam = urlObj.searchParams.get('ref')\n        return refParam || 'UNKNOWN'\n      } catch (error) {\n        console.error('提取邀请码失败:', error)\n        return 'UNKNOWN'\n      }\n    },\n\n    // 显示提现弹窗\n    openWithdrawModal() {\n      if (this.availableEarnings < 50) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为50元，请继续邀请获得更多收益',\n          placement: 'topRight'\n        })\n        return\n      }\n      this.showWithdrawModal = true\n    },\n\n    // 处理提现申请\n    async handleWithdraw() {\n      this.withdrawForm.validateFields(async (err, values) => {\n        if (err) return\n\n        // 二次确认弹窗\n        const h = this.$createElement\n        this.$confirm({\n          title: '确认提现申请',\n          content: h('div', { style: { margin: '16px 0' } }, [\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '提现金额：'),\n              `¥${values.amount}`\n            ]),\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '支付宝账号：'),\n              values.alipayAccount\n            ]),\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '收款人姓名：'),\n              values.realName\n            ]),\n            h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [\n              h('strong', '注意：'),\n              '请再次核实一遍提现信息，请确认信息无误！'\n            ])\n          ]),\n          okText: '确认提现',\n          cancelText: '取消',\n          centered: true,\n          width: 400,\n          onOk: async () => {\n            await this.submitWithdrawRequest(values)\n          }\n        })\n      })\n    },\n\n    // 提交提现申请\n    async submitWithdrawRequest(values) {\n      this.withdrawLoading = true\n\n      try {\n        const params = {\n          withdrawalAmount: values.amount,\n          realName: values.realName,\n          alipayAccount: values.alipayAccount\n        }\n\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n\n        if (response.success) {\n          this.withdrawLoading = false\n          this.showWithdrawModal = false\n          this.withdrawForm.resetFields()\n\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          })\n\n          // 刷新数据\n          await Promise.all([\n            this.loadReferralData(),\n            this.loadWithdrawRecords()\n          ])\n        } else {\n          this.withdrawLoading = false\n          this.$notification.error({\n            message: '提现申请失败',\n            description: response.message || '申请失败，请重试',\n            placement: 'topRight'\n          })\n        }\n      } catch (error) {\n        this.withdrawLoading = false\n        console.error('提现申请失败:', error)\n\n        // 检查是否是HTTP响应错误，如果是则显示后端返回的错误信息\n        if (error.response && error.response.data && error.response.data.message) {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: error.response.data.message,\n            placement: 'topRight'\n          })\n        } else if (error.message) {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: error.message,\n            placement: 'topRight'\n          })\n        } else {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: '网络错误，请稍后重试',\n            placement: 'topRight'\n          })\n        }\n      }\n    },\n\n    // 加载邀请用户列表\n    async loadReferralUsers() {\n      try {\n        this.usersLoading = true\n\n        const params = {\n          current: (this.usersPagination && this.usersPagination.current) || 1,\n          size: (this.usersPagination && this.usersPagination.pageSize) || 10,\n          orderBy: (this.usersSort && this.usersSort.orderBy) || 'total_reward',\n          order: (this.usersSort && this.usersSort.order) || 'desc'\n        }\n\n        console.log('加载邀请用户参数:', params)\n\n        const response = await this.$http.get('/api/usercenter/referralList', { params })\n\n        if (response && response.success) {\n          const result = response.result || {}\n          const records = result.records || []\n\n          // 更新分页信息\n          if (this.usersPagination) {\n            this.usersPagination.total = result.total || 0\n          }\n\n          // 转换数据格式\n          this.referralUsers = records.map((item, index) => ({\n            key: item.id || index,\n            nickname: item.referee_nickname || `用户***${index + 1}`,\n            avatar: item.referee_avatar || '',\n            registerTime: item.register_time || '',\n            reward: item.total_reward || '0.00'\n          }))\n        } else {\n          this.referralUsers = []\n          if (this.usersPagination) {\n            this.usersPagination.total = 0\n          }\n        }\n      } catch (error) {\n        console.error('获取邀请用户列表失败:', error)\n        this.referralUsers = []\n        // 如果是网络错误或其他错误，显示友好提示\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n        }\n      } finally {\n        this.usersLoading = false\n      }\n    },\n\n    // 加载提现记录\n    async loadWithdrawRecords() {\n      try {\n        this.recordsLoading = true\n\n        const params = {\n          current: (this.withdrawPagination && this.withdrawPagination.current) || 1,\n          size: (this.withdrawPagination && this.withdrawPagination.pageSize) || 10,\n          orderBy: (this.withdrawSort && this.withdrawSort.orderBy) || 'apply_time',\n          order: (this.withdrawSort && this.withdrawSort.order) || 'desc'\n        }\n\n        // 添加筛选参数\n        if (this.withdrawFilter) {\n          if (this.withdrawFilter.minAmount !== null && this.withdrawFilter.minAmount !== '') {\n            params.minAmount = this.withdrawFilter.minAmount\n          }\n          if (this.withdrawFilter.maxAmount !== null && this.withdrawFilter.maxAmount !== '') {\n            params.maxAmount = this.withdrawFilter.maxAmount\n          }\n          if (this.withdrawFilter.status !== null) {\n            params.status = this.withdrawFilter.status\n          }\n          if (this.withdrawFilter.dateRange && this.withdrawFilter.dateRange.length === 2) {\n            // 处理moment对象，转换为YYYY-MM-DD格式\n            params.startDate = this.withdrawFilter.dateRange[0].format\n              ? this.withdrawFilter.dateRange[0].format('YYYY-MM-DD')\n              : this.withdrawFilter.dateRange[0]\n            params.endDate = this.withdrawFilter.dateRange[1].format\n              ? this.withdrawFilter.dateRange[1].format('YYYY-MM-DD')\n              : this.withdrawFilter.dateRange[1]\n          }\n          if (this.withdrawFilter.completeDateRange && this.withdrawFilter.completeDateRange.length === 2) {\n            // 处理moment对象，转换为YYYY-MM-DD格式\n            params.completeStartDate = this.withdrawFilter.completeDateRange[0].format\n              ? this.withdrawFilter.completeDateRange[0].format('YYYY-MM-DD')\n              : this.withdrawFilter.completeDateRange[0]\n            params.completeEndDate = this.withdrawFilter.completeDateRange[1].format\n              ? this.withdrawFilter.completeDateRange[1].format('YYYY-MM-DD')\n              : this.withdrawFilter.completeDateRange[1]\n          }\n        }\n\n        console.log('加载提现记录参数:', params)\n        console.log('原始筛选条件:', {\n          dateRange: this.withdrawFilter.dateRange,\n          completeDateRange: this.withdrawFilter.completeDateRange\n        })\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n\n        if (response && response.success) {\n          const result = response.result || {}\n          const records = result.records || []\n\n          // 更新分页信息\n          if (this.withdrawPagination) {\n            this.withdrawPagination.total = result.total || 0\n          }\n\n          // 转换数据格式\n          this.withdrawRecords = records.map((item, index) => ({\n            key: item.id || index,\n            id: item.id,\n            amount: item.withdrawal_amount || '0.00',\n            method: item.withdrawalMethod || '支付宝',\n            applyTime: item.apply_time || '',\n            status: this.getWithdrawStatusText(item.status, item.review_remark),\n            rawStatus: item.status,\n            completeTime: item.review_time || '-'\n          }))\n        } else {\n          this.withdrawRecords = []\n          if (this.withdrawPagination) {\n            this.withdrawPagination.total = 0\n          }\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.withdrawRecords = []\n        // 如果是网络错误或其他错误，显示友好提示\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n        }\n      } finally {\n        this.recordsLoading = false\n      }\n    },\n\n    // 获取提现状态文本\n    getWithdrawStatusText(status, reviewRemark) {\n      const statusMap = {\n        0: '待审核',\n        1: '待审核',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已取消'\n      }\n      let statusText = statusMap[status] || '未知状态'\n\n      // 如果是已拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n\n    // 获取状态颜色\n    getStatusColor(statusText) {\n      // 处理带拒绝原因的状态文本\n      if (statusText.includes('已拒绝')) {\n        return 'red'\n      }\n\n      const colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '待审核': 'orange',\n        '已取消': 'gray'\n      }\n      return colorMap[statusText] || 'default'\n    },\n\n    // 获取排序状态（暂时保留，可能后续需要）\n    getSortOrder(field) {\n      if (this.usersSort && this.usersSort.orderBy === field) {\n        return this.usersSort.order === 'asc' ? 'ascend' : 'descend'\n      }\n      return null\n    },\n\n    // 处理邀请用户表格分页变化\n    handleUsersTableChange(pagination, _filters, sorter) {\n      console.log('表格变化:', { pagination, sorter })\n\n      if (this.usersPagination && pagination) {\n        this.usersPagination.current = pagination.current || 1\n        this.usersPagination.pageSize = pagination.pageSize || 10\n      }\n\n      // 处理排序\n      if (sorter && sorter.field && this.usersSort) {\n        const fieldMap = {\n          'nickname': 'nickname',\n          'registerTime': 'register_time',\n          'reward': 'total_reward'\n        }\n\n        const newOrderBy = fieldMap[sorter.field] || 'total_reward'\n\n        // 如果点击的是同一个字段，切换排序方向\n        if (this.usersSort.orderBy === newOrderBy) {\n          this.usersSort.order = this.usersSort.order === 'asc' ? 'desc' : 'asc'\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.usersSort.orderBy = newOrderBy\n          if (newOrderBy === 'total_reward') {\n            this.usersSort.order = 'desc' // 奖励金额默认降序\n          } else {\n            this.usersSort.order = 'asc' // 其他字段默认升序\n          }\n        }\n\n        console.log('排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.usersSort.orderBy,\n          finalOrder: this.usersSort.order,\n          clickedSameField: this.usersSort.orderBy === newOrderBy\n        })\n\n        // 排序时回到第一页\n        if (this.usersPagination) {\n          this.usersPagination.current = 1\n        }\n      }\n\n      this.loadReferralUsers()\n    },\n\n    // 处理提现记录表格分页变化\n    handleWithdrawTableChange(pagination, _filters, sorter) {\n      console.log('提现记录表格变化:', { pagination, sorter })\n\n      if (this.withdrawPagination && pagination) {\n        this.withdrawPagination.current = pagination.current || 1\n        this.withdrawPagination.pageSize = pagination.pageSize || 10\n      }\n\n      // 处理排序\n      if (sorter && sorter.field && this.withdrawSort) {\n        const fieldMap = {\n          'amount': 'withdrawal_amount',\n          'applyTime': 'apply_time',\n          'status': 'status',\n          'completeTime': 'review_time'\n        }\n\n        const newOrderBy = fieldMap[sorter.field] || 'apply_time'\n\n        // 如果点击的是同一个字段，切换排序方向\n        if (this.withdrawSort.orderBy === newOrderBy) {\n          this.withdrawSort.order = this.withdrawSort.order === 'asc' ? 'desc' : 'asc'\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.withdrawSort.orderBy = newOrderBy\n          if (newOrderBy === 'apply_time') {\n            this.withdrawSort.order = 'desc' // 申请时间默认降序\n          } else if (newOrderBy === 'withdrawal_amount') {\n            this.withdrawSort.order = 'desc' // 金额默认降序\n          } else {\n            this.withdrawSort.order = 'asc' // 其他字段默认升序\n          }\n        }\n\n        console.log('提现记录排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.withdrawSort.orderBy,\n          finalOrder: this.withdrawSort.order\n        })\n\n        // 排序时回到第一页\n        if (this.withdrawPagination) {\n          this.withdrawPagination.current = 1\n        }\n      }\n\n      this.loadWithdrawRecords()\n    },\n\n    // 处理提现记录筛选\n    handleWithdrawFilter() {\n      // 筛选时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1\n      }\n      this.loadWithdrawRecords()\n    },\n\n    // 重置提现记录筛选\n    handleWithdrawReset() {\n      this.withdrawFilter = {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        dateRange: [],\n        completeDateRange: []\n      }\n\n      // 重置时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1\n      }\n\n      this.loadWithdrawRecords()\n    },\n\n    // 处理取消提现\n    handleCancelWithdraw(record) {\n      const h = this.$createElement\n      this.$confirm({\n        title: '确认取消提现',\n        content: h('div', { style: { margin: '16px 0' } }, [\n          h('p', { style: { marginBottom: '8px' } }, [\n            h('strong', '提现金额：'),\n            `¥${record.amount}`\n          ]),\n          h('p', { style: { marginBottom: '8px' } }, [\n            h('strong', '申请时间：'),\n            record.applyTime\n          ]),\n          h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [\n            h('strong', '注意：'),\n            '取消后金额将返还到可提现余额，此操作不可撤销！'\n          ])\n        ]),\n        okText: '确认取消',\n        okType: 'danger',\n        cancelText: '返回',\n        centered: true,\n        width: 400,\n        onOk: async () => {\n          await this.confirmCancelWithdraw(record)\n        }\n      })\n    },\n\n    // 确认取消提现\n    async confirmCancelWithdraw(record) {\n      this.cancelLoading = true\n\n      try {\n        const params = {\n          withdrawalId: record.id\n        }\n\n        const response = await this.$http.post('/api/usercenter/cancelWithdrawal', params)\n\n        if (response.success) {\n          this.$notification.success({\n            message: '取消成功',\n            description: '提现申请已取消，金额已返还到可提现余额',\n            placement: 'topRight'\n          })\n\n          // 刷新数据\n          await Promise.all([\n            this.loadReferralData(),\n            this.loadWithdrawRecords()\n          ])\n        } else {\n          this.$notification.error({\n            message: '取消失败',\n            description: response.message || '取消提现失败，请重试',\n            placement: 'topRight'\n          })\n        }\n      } catch (error) {\n        console.error('取消提现失败:', error)\n\n        if (error.response && error.response.data && error.response.data.message) {\n          this.$notification.error({\n            message: '取消失败',\n            description: error.response.data.message,\n            placement: 'topRight'\n          })\n        } else if (error.message) {\n          this.$notification.error({\n            message: '取消失败',\n            description: error.message,\n            placement: 'topRight'\n          })\n        } else {\n          this.$notification.error({\n            message: '取消失败',\n            description: '网络错误，请稍后重试',\n            placement: 'topRight'\n          })\n        }\n      } finally {\n        this.cancelLoading = false\n      }\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n\n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n\n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     },\n\n     // 获取角色显示名称\n     getRoleDisplayName(roleCode) {\n       switch (roleCode) {\n         case 'VIP':\n           return 'VIP用户'\n         case 'SVIP':\n           return 'SVIP用户'\n         case 'user':\n         default:\n           return '普通用户'\n       }\n     },\n\n     // 获取邀请人数要求文本\n     getRequirementText(config) {\n       const minReferrals = config.min_referrals\n       const roleCode = config.role_code\n\n       // 查找同角色的下一个等级\n       const sameRoleConfigs = this.allLevelConfigs.filter(c => c.role_code === roleCode)\n       const currentIndex = sameRoleConfigs.findIndex(c => c.id === config.id)\n       const nextConfig = sameRoleConfigs[currentIndex + 1]\n\n       if (roleCode === 'SVIP') {\n         return '无要求'\n       }\n\n       if (nextConfig) {\n         if (minReferrals === 0) {\n           return `0-${nextConfig.min_referrals - 1}人`\n         } else {\n           return `${minReferrals}-${nextConfig.min_referrals - 1}人`\n         }\n       } else {\n         return `${minReferrals}人以上`\n       }\n     }\n   }\n }\n</script>\n\n<style scoped>\n.affiliate-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0 0 1.5rem 0;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: white;\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.badge-level {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 分销内容区域 */\n.affiliate-section {\n  padding: 0 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 邀请工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 提现弹窗 */\n.withdraw-modal-content {\n  padding: 8px 0;\n}\n\n.withdraw-info {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 24px;\n  border: 1px solid #e2e8f0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.info-value {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.withdraw-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #f1f5f9;\n}\n\n.card-action {\n  margin-top: 8px;\n}\n\n/* 邀请链接区域 */\n.promotion-link-section {\n  background: white;\n  border-radius: 20px;\n  padding: 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 2px solid #e2e8f0;\n}\n\n.link-main-container {\n  margin-bottom: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.link-input-large {\n  flex: 1;\n}\n\n.link-input-large .ant-input {\n  font-size: 1.1rem;\n  padding: 16px 20px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  background: #f8fafc;\n  transition: all 0.3s ease;\n}\n\n.link-input-large .ant-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.link-actions {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.copy-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border: none;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.copy-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);\n  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n}\n\n.copy-btn:active {\n  transform: translateY(0);\n}\n\n.qr-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.qr-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n}\n\n.qr-btn:active {\n  transform: translateY(0);\n}\n\n/* 按钮禁用状态 */\n.copy-btn:disabled,\n.qr-btn:disabled {\n  background: #e5e7eb !important;\n  color: #9ca3af !important;\n  box-shadow: none !important;\n  transform: none !important;\n  cursor: not-allowed !important;\n}\n\n.copy-btn:disabled:hover,\n.qr-btn:disabled:hover {\n  background: #e5e7eb !important;\n  transform: none !important;\n  box-shadow: none !important;\n}\n\n.link-tips {\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: #0369a1;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.link-tips strong {\n  color: #1e40af;\n  font-weight: 700;\n}\n\n/* 分成规则表格 */\n.commission-rules {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.rules-table {\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.rule-row:last-child {\n  border-bottom: none;\n}\n\n.rule-row.header {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.rule-row.vip {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  color: #475569;\n  font-weight: 500;\n  border: 1px solid #94a3b8;\n  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.2);\n}\n\n.rule-row.svip {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);\n  color: #92400e;\n  font-weight: 600;\n  border: 1px solid #d97706;\n  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);\n}\n\n.rule-cell {\n  padding: 16px;\n  text-align: center;\n  border-right: 1px solid #e2e8f0;\n}\n\n.rule-cell:last-child {\n  border-right: none;\n}\n\n.rule-cell.highlight {\n  font-weight: 700;\n  color: #dc2626;\n  font-size: 1.1rem;\n}\n\n/* 表格容器 */\n.users-table-container,\n.records-table-container {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.referral-users,\n.withdraw-records {\n  margin-bottom: 3rem;\n}\n\n.reward-amount,\n.withdraw-amount {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n\n  .promotion-link-section {\n    padding: 1.5rem;\n    margin-bottom: 2rem;\n  }\n\n  .link-main-container {\n    gap: 1rem;\n  }\n\n  .link-input-large .ant-input {\n    font-size: 1rem;\n    padding: 14px 16px;\n  }\n\n  .link-actions {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .copy-btn,\n  .qr-btn {\n    width: 100%;\n    min-width: auto;\n    padding: 14px 24px;\n    font-size: 15px;\n  }\n\n  .rule-row {\n    grid-template-columns: 1fr;\n    text-align: left;\n  }\n\n  .rule-cell {\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    text-align: left;\n  }\n\n  .rule-cell:last-child {\n    border-bottom: none;\n  }\n\n  .promotion-link-section,\n  .commission-rules,\n  .users-table-container,\n  .records-table-container {\n    padding: 1.5rem;\n  }\n}\n\n/* 水平等级时间线样式 */\n.level-timeline-horizontal {\n  position: relative;\n  padding: 20px 0;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.level-step-horizontal {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  flex: 1;\n  text-align: center;\n  min-width: 0;\n  padding: 0 10px;\n}\n\n.step-circle-horizontal {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  z-index: 2;\n  border: 2px solid #e8e8e8;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.level-step-horizontal.completed .step-circle-horizontal {\n  background-color: #52c41a;\n  border-color: #52c41a;\n  color: white;\n}\n\n.level-step-horizontal.current .step-circle-horizontal {\n  background-color: #1890ff;\n  border-color: #1890ff;\n  color: white;\n}\n\n.level-step-horizontal.upcoming .step-circle-horizontal {\n  border-color: #d9d9d9;\n}\n\n.step-circle-horizontal .step-number {\n  font-size: 14px;\n  font-weight: 600;\n  color: #666;\n}\n\n.step-content-horizontal {\n  padding: 0 5px;\n  max-width: 120px;\n}\n\n.step-content-horizontal .step-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n  white-space: nowrap;\n}\n\n.step-content-horizontal .step-rate {\n  color: #1890ff;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.level-step-horizontal.completed .step-content-horizontal .step-rate {\n  color: #52c41a;\n}\n\n.step-content-horizontal .step-requirement {\n  color: #666;\n  margin-bottom: 4px;\n  font-size: 13px;\n}\n\n.step-content-horizontal .step-remaining {\n  color: #ff4d4f;\n  font-size: 12px;\n}\n\n.step-content-horizontal .step-completed {\n  color: #52c41a;\n  font-size: 12px;\n}\n\n.step-line-horizontal {\n  position: absolute;\n  left: calc(50% + 18px);\n  width: calc(100% - 36px);\n  top: 18px;\n  height: 2px;\n  background-color: #e8e8e8;\n  z-index: 1;\n}\n\n.level-step-horizontal.completed .step-line-horizontal {\n  background-color: #52c41a;\n}\n\n.level-step-horizontal:last-child .step-line-horizontal {\n  display: none;\n}\n</style>\n"]}]}