{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue", "mtime": 1753672674026}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { gsap } from 'gsap';\nimport LogoImage from '@/components/common/LogoImage.vue';\nexport default {\n  name: 'HomeHeader',\n  components: {\n    LogoImage: LogoImage\n  },\n  data: function data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: []\n    };\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadMenuData();\n\n            case 2:\n              this.initScrollListener();\n              this.initNavbarAnimations();\n\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    loadMenuData: function () {\n      var _loadMenuData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                try {\n                  // TODO: 从API获取菜单数据，替换硬编码\n                  // const response = await this.$http.get('/api/website/header/menu')\n                  // this.menuItems = response.data\n                  // 临时硬编码数据，后续需要替换为API调用\n                  this.menuItems = [{\n                    name: '首页',\n                    href: '#home',\n                    icon: 'home'\n                  }, {\n                    name: '商城',\n                    href: '#market',\n                    icon: 'shop'\n                  }, {\n                    name: '客户案例',\n                    href: '#cases',\n                    icon: 'trophy'\n                  }, {\n                    name: '教程中心',\n                    href: '#tutorials',\n                    icon: 'book'\n                  }, {\n                    name: '签到奖励',\n                    href: '#signin',\n                    icon: 'gift'\n                  }, {\n                    name: '订阅会员',\n                    href: '#membership',\n                    icon: 'crown'\n                  }, {\n                    name: '邀请奖励',\n                    href: '#affiliate',\n                    icon: 'team'\n                  }, {\n                    name: '个人中心',\n                    href: '#usercenter',\n                    icon: 'user'\n                  }];\n                } catch (error) {\n                  console.error('加载菜单数据失败:', error); // 使用默认菜单数据作为降级方案\n\n                  this.menuItems = [{\n                    name: '首页',\n                    href: '#home',\n                    icon: 'home'\n                  }, {\n                    name: '商城',\n                    href: '#market',\n                    icon: 'shop'\n                  }, {\n                    name: '个人中心',\n                    href: '#usercenter',\n                    icon: 'user'\n                  }];\n                }\n\n              case 1:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function loadMenuData() {\n        return _loadMenuData.apply(this, arguments);\n      }\n\n      return loadMenuData;\n    }(),\n    initScrollListener: function initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll);\n    },\n    handleScroll: function handleScroll() {\n      this.isScrolled = window.scrollY > 50;\n    },\n    toggleMobileMenu: function toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n    },\n    initNavbarAnimations: function initNavbarAnimations() {\n      // 导航栏快速入场动画 - 同时出现\n      gsap.from([this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions], {\n        duration: 0.4,\n        y: -20,\n        opacity: 0,\n        ease: \"power2.out\",\n        stagger: 0.05 // 几乎同时出现\n\n      });\n    }\n  }\n};", null]}