{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'AigcWithdrawalList',\n  data: function data() {\n    return {\n      loading: false,\n      // 搜索表单\n      searchForm: {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      },\n      // 表格数据\n      dataSource: [],\n      // 分页\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total) {\n          return \"\\u5171 \".concat(total, \" \\u6761\\u8BB0\\u5F55\");\n        }\n      },\n      // 表格列定义\n      columns: [{\n        title: '用户信息',\n        key: 'userInfo',\n        width: 150,\n        scopedSlots: {\n          customRender: 'userInfo'\n        }\n      }, {\n        title: '提现金额',\n        key: 'amount',\n        width: 120,\n        align: 'right',\n        scopedSlots: {\n          customRender: 'amount'\n        }\n      }, {\n        title: '支付宝信息',\n        key: 'alipayInfo',\n        width: 180,\n        scopedSlots: {\n          customRender: 'alipayInfo'\n        }\n      }, {\n        title: '申请时间',\n        dataIndex: 'apply_time',\n        key: 'applyTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'applyTime'\n        }\n      }, {\n        title: '审核时间',\n        dataIndex: 'review_time',\n        key: 'reviewTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'reviewTime'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        width: 100,\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 280,\n        fixed: 'right',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 弹窗状态\n      showRejectModal: false,\n      showDetailModal: false,\n      currentRecord: null,\n      rejectReason: '',\n      rejecting: false\n    };\n  },\n  mounted: function mounted() {\n    this.loadData();\n  },\n  methods: {\n    // 加载数据\n    loadData: function () {\n      var _loadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var params, response, data, firstRecord, errorMsg;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.prev = 0;\n                this.loading = true;\n                params = _objectSpread({\n                  current: this.pagination.current,\n                  size: this.pagination.pageSize\n                }, this.getSearchParams());\n                _context.next = 5;\n                return this.$http.get('/api/usercenter/admin/withdrawalList', {\n                  params: params\n                });\n\n              case 5:\n                response = _context.sent;\n                console.log('提现列表完整响应:', response); // 根据实际返回的数据结构处理\n\n                data = response.data || response;\n                console.log('提现列表数据:', data);\n\n                if (data && data.success) {\n                  this.dataSource = data.result.records || [];\n                  this.pagination.total = data.result.total || 0;\n                  console.log('数据加载成功:', this.dataSource.length, '条记录');\n                  console.log('完整result结构:', data.result);\n                  console.log('records数组:', data.result.records);\n                  console.log('第一条数据结构:', this.dataSource[0]);\n                  console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {})); // 打印每个字段的值\n\n                  if (this.dataSource[0]) {\n                    firstRecord = this.dataSource[0];\n                    console.log('字段值详情:');\n                    Object.keys(firstRecord).forEach(function (key) {\n                      console.log(\"  \".concat(key, \":\"), firstRecord[key]);\n                    });\n                  }\n                } else {\n                  errorMsg = data && data.message || '获取数据失败';\n                  this.$message.error(errorMsg);\n                  this.dataSource = [];\n                  this.pagination.total = 0;\n                }\n\n                _context.next = 16;\n                break;\n\n              case 12:\n                _context.prev = 12;\n                _context.t0 = _context[\"catch\"](0);\n                console.error('加载提现数据失败:', _context.t0);\n                this.$message.error('加载数据失败');\n\n              case 16:\n                _context.prev = 16;\n                this.loading = false;\n                return _context.finish(16);\n\n              case 19:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[0, 12, 16, 19]]);\n      }));\n\n      function loadData() {\n        return _loadData.apply(this, arguments);\n      }\n\n      return loadData;\n    }(),\n    // 获取搜索参数\n    getSearchParams: function getSearchParams() {\n      var params = {};\n\n      if (this.searchForm.status !== undefined) {\n        params.status = this.searchForm.status;\n      }\n\n      if (this.searchForm.username) {\n        params.username = this.searchForm.username.trim();\n      }\n\n      if (this.searchForm.alipayInfo) {\n        params.alipayInfo = this.searchForm.alipayInfo.trim();\n      }\n\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD');\n        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD');\n      }\n\n      return params;\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.pagination.current = 1;\n      this.loadData();\n    },\n    // 重置搜索\n    handleReset: function handleReset() {\n      this.searchForm = {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      };\n      this.pagination.current = 1;\n      this.loadData();\n    },\n    // 表格变化\n    handleTableChange: function handleTableChange(pagination) {\n      this.pagination = _objectSpread(_objectSpread({}, this.pagination), pagination);\n      this.loadData();\n    },\n    // 审核通过\n    handleApprove: function () {\n      var _handleApprove = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(record) {\n        var _this = this;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.$confirm({\n                  title: '确认审核通过',\n                  content: \"\\u786E\\u5B9A\\u8981\\u5BA1\\u6838\\u901A\\u8FC7\\u7528\\u6237 \".concat(record.username, \" \\u7684\\u63D0\\u73B0\\u7533\\u8BF7\\u5417\\uFF1F\\n\\u63D0\\u73B0\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.formatNumber(record.withdrawal_amount)),\n                  onOk: function () {\n                    var _onOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n                      var response, data;\n                      return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                        while (1) {\n                          switch (_context2.prev = _context2.next) {\n                            case 0:\n                              _context2.prev = 0;\n\n                              _this.$set(record, 'approving', true);\n\n                              _context2.next = 4;\n                              return _this.$http.post('/api/usercenter/admin/approveWithdrawal', {\n                                id: record.id\n                              });\n\n                            case 4:\n                              response = _context2.sent;\n                              // 根据实际返回的数据结构处理\n                              data = response.data || response;\n\n                              if (data.success) {\n                                _this.$message.success('审核通过成功');\n\n                                _this.loadData();\n                              } else {\n                                _this.$message.error(data.message || '审核失败');\n                              }\n\n                              _context2.next = 13;\n                              break;\n\n                            case 9:\n                              _context2.prev = 9;\n                              _context2.t0 = _context2[\"catch\"](0);\n                              console.error('审核通过失败:', _context2.t0);\n\n                              _this.$message.error('审核失败，请重试');\n\n                            case 13:\n                              _context2.prev = 13;\n\n                              _this.$set(record, 'approving', false);\n\n                              return _context2.finish(13);\n\n                            case 16:\n                            case \"end\":\n                              return _context2.stop();\n                          }\n                        }\n                      }, _callee2, null, [[0, 9, 13, 16]]);\n                    }));\n\n                    function onOk() {\n                      return _onOk.apply(this, arguments);\n                    }\n\n                    return onOk;\n                  }()\n                });\n\n              case 1:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function handleApprove(_x) {\n        return _handleApprove.apply(this, arguments);\n      }\n\n      return handleApprove;\n    }(),\n    // 审核拒绝\n    handleReject: function handleReject(record) {\n      this.currentRecord = record;\n      this.rejectReason = '';\n      this.showRejectModal = true;\n    },\n    // 确认拒绝\n    confirmReject: function () {\n      var _confirmReject = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (this.rejectReason.trim()) {\n                  _context4.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请填写拒绝原因');\n                return _context4.abrupt(\"return\");\n\n              case 3:\n                _context4.prev = 3;\n                this.rejecting = true;\n                _context4.next = 7;\n                return this.$http.post('/api/usercenter/admin/rejectWithdrawal', {\n                  id: this.currentRecord.id,\n                  reason: this.rejectReason.trim()\n                });\n\n              case 7:\n                response = _context4.sent;\n                // 根据实际返回的数据结构处理\n                data = response.data || response;\n\n                if (data.success) {\n                  this.$message.success('审核拒绝成功');\n                  this.showRejectModal = false;\n                  this.loadData();\n                } else {\n                  this.$message.error(data.message || '审核失败');\n                }\n\n                _context4.next = 16;\n                break;\n\n              case 12:\n                _context4.prev = 12;\n                _context4.t0 = _context4[\"catch\"](3);\n                console.error('审核拒绝失败:', _context4.t0);\n                this.$message.error('审核失败，请重试');\n\n              case 16:\n                _context4.prev = 16;\n                this.rejecting = false;\n                return _context4.finish(16);\n\n              case 19:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[3, 12, 16, 19]]);\n      }));\n\n      function confirmReject() {\n        return _confirmReject.apply(this, arguments);\n      }\n\n      return confirmReject;\n    }(),\n    // 查看详情\n    handleViewDetail: function handleViewDetail(record) {\n      this.currentRecord = record;\n      this.showDetailModal = true;\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(status) {\n      var colorMap = {\n        1: 'orange',\n        // 待审核 - 橙色\n        2: 'green',\n        // 已发放 - 绿色\n        3: 'red',\n        // 审核拒绝 - 红色\n        4: 'gray' // 已取消 - 灰色\n\n      };\n      return colorMap[status] || 'volcano'; // 未知状态用火山红色\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status, reviewRemark) {\n      var textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝',\n        4: '已取消'\n      };\n      var statusText = textMap[status] || '未知状态'; // 如果是审核拒绝状态且有拒绝原因，则添加原因\n\n      if (status === 3 && reviewRemark) {\n        statusText += \"\\uFF08\".concat(reviewRemark, \"\\uFF09\");\n      }\n\n      return statusText;\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(number) {\n      if (!number) return '0.00';\n      return parseFloat(number).toFixed(2);\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(dateString) {\n      if (!dateString) return '-';\n\n      try {\n        var date = new Date(dateString);\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } catch (error) {\n        return '-';\n      }\n    }\n  }\n};", null]}