{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=style&index=0&id=7ef8aeb4&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 主容器 */\n.website-login {\n  min-height: 100vh;\n  position: relative;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n/* 动态背景 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n}\n\n.bg-animated-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);\n  background-size: 60px 60px;\n  animation: gridMove 30s linear infinite;\n}\n\n@keyframes gridMove {\n  0% { transform: translate(0, 0); }\n  100% { transform: translate(60px, 60px); }\n}\n\n.bg-floating-elements {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),\n    radial-gradient(circle at 45% 55%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);\n  animation: float 12s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(90deg); }\n}\n\n.bg-gradient-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(255, 255, 255, 0.3) 0%,\n    rgba(59, 130, 246, 0.05) 25%,\n    rgba(139, 92, 246, 0.05) 50%,\n    rgba(16, 185, 129, 0.05) 75%,\n    rgba(255, 255, 255, 0.2) 100%\n  );\n}\n\n\n\n/* 主要内容区域 */\n.login-main {\n  display: flex;\n  min-height: 100vh;\n  position: relative;\n  z-index: 1;\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 8rem 2rem 2rem; /* ✅ 增加顶部间距到8rem，给login-info更多距离页头的空间 */\n}\n\n/* 左侧信息展示 */\n.login-info {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem; /* ✅ 增加内边距，让内容与容器边缘有更多距离 */\n  background: rgba(255, 255, 255, 0.6);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  margin: 1rem 1rem 2rem; /* ✅ 增加顶部margin，与页头保持更好的距离 */\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(-50px);\n}\n\n.info-content {\n  max-width: 600px;\n  color: #374151;\n}\n\n.brand-showcase {\n  text-align: center;\n  margin-bottom: 4rem;\n}\n\n.brand-logo-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n/* Login页面Logo容器样式 */\n.login-logo-container {\n  width: 80px;\n  height: 80px;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);\n  animation: logoFloat 3s ease-in-out infinite;\n}\n\n.login-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 20px;\n}\n\n/* Login页面Fallback样式 */\n.login-logo-fallback {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 2.5rem;\n}\n\n@keyframes logoFloat {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.brand-title {\n  font-size: 3rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n\n.brand-slogan {\n  font-size: 1.2rem;\n  color: #6b7280;\n  margin: 0;\n}\n\n/* 特性展示 */\n.feature-highlights {\n  display: grid;\n  gap: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  padding: 1.5rem;\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateY(30px);\n}\n\n.feature-item:hover {\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(59, 130, 246, 0.2);\n  transform: translateX(10px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.feature-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.feature-text h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n\n.feature-text p {\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin: 0;\n  line-height: 1.5;\n}\n\n/* 右侧登录容器 */\n.login-container {\n  flex: 1;\n  max-width: 600px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  margin: 0 1rem 2rem; /* 移除顶部margin，只保留底部和左右 */\n  /* ✅ 初始状态设置为不可见，避免闪烁 */\n  opacity: 0;\n  transform: translateX(50px);\n}\n\n.login-card {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 3rem;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.1),\n    0 0 0 1px rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n/* 登录头部 */\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n}\n\n.login-title {\n  font-size: 2rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0 0 1rem 0;\n}\n\n.login-subtitle {\n  color: #64748b;\n  font-size: 1rem;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 登录方式切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 0.5rem;\n  background: #f8fafc;\n  padding: 0.25rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.tab-btn {\n  padding: 0.75rem 0.5rem;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.85rem;\n  font-weight: 500;\n  color: #64748b;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  min-width: 0;\n  flex: 1;\n}\n\n.tab-text {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-weight: 600;\n}\n\n.tab-btn:hover {\n  color: #3b82f6;\n}\n\n.tab-btn .anticon {\n  font-size: 1rem;\n}\n\n/* 登录表单 */\n.login-form {\n  margin-top: 0;\n}\n\n.login-content {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 快速登录顶部 */\n.quick-login-top {\n  margin-bottom: 2rem;\n}\n\n.quick-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 1rem;\n  text-align: center;\n}\n\n.social-buttons-horizontal {\n  display: flex;\n  gap: 1rem;\n}\n\n.social-btn-large {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 12px;\n  background: #ffffff;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n.social-btn-large:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n\n.social-btn-large.wechat:hover {\n  border-color: #07c160;\n  color: #07c160;\n  background: rgba(7, 193, 96, 0.05);\n}\n\n.social-btn-large.qq:hover {\n  border-color: #12b7f5;\n  color: #12b7f5;\n  background: rgba(18, 183, 245, 0.05);\n}\n\n.social-btn-large .anticon {\n  font-size: 1.2rem;\n}\n\n/* 分割线 */\n.divider-with-text {\n  text-align: center;\n  margin: 2rem 0;\n  position: relative;\n}\n\n.divider-with-text::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);\n}\n\n.divider-with-text span {\n  background: #ffffff;\n  padding: 0 1.5rem;\n  color: #9ca3af;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 输入组 */\n.input-group {\n  margin-bottom: 1.5rem;\n}\n\n.clean-input {\n  border-radius: 12px !important;\n  border: 2px solid #e5e7eb !important;\n  transition: all 0.3s ease !important;\n  background: #ffffff !important;\n}\n\n.clean-input:focus {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;\n}\n\n.clean-input:hover {\n  border-color: #9ca3af !important;\n}\n\n/* 验证码行 */\n.captcha-row {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.captcha-input {\n  flex: 1;\n}\n\n.captcha-image-container {\n  position: relative;\n  cursor: pointer;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.captcha-image-container:hover {\n  transform: scale(1.02);\n}\n\n.captcha-image {\n  width: 120px;\n  height: 48px;\n  border-radius: 12px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n  display: block;\n}\n\n.captcha-refresh-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(59, 130, 246, 0.8);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n}\n\n.captcha-image-container:hover .captcha-refresh-overlay {\n  opacity: 1;\n}\n\n/* 验证码行样式 */\n.verify-code-row {\n  display: flex;\n  gap: 0.75rem;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  border-radius: 8px;\n  white-space: nowrap;\n  min-width: 120px;\n}\n\n.send-code-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 微信登录样式 */\n.wechat-login-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n}\n\n.qr-loading {\n  padding: 3rem;\n  color: #64748b;\n}\n\n.qr-loading p {\n  margin-top: 1rem;\n  margin-bottom: 0;\n}\n\n.qr-instructions h4 {\n  margin: 0 0 1rem 0;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.qr-instructions p {\n  margin: 0.5rem 0;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.invite-tip {\n  color: #3b82f6 !important;\n  font-weight: 500;\n}\n\n/* 提示信息样式 */\n.phone-login-tip,\n.email-login-tip {\n  margin-top: 1rem;\n}\n\n.phone-login-tip .ant-alert,\n.email-login-tip .ant-alert {\n  border-radius: 8px;\n}\n\n/* 登录选项 */\n.login-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 1.5rem 0;\n}\n\n.remember-me {\n  color: #6b7280;\n  font-size: 0.9rem;\n}\n\n.forgot-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.forgot-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录按钮 */\n.login-button-item {\n  margin-bottom: 0;\n}\n\n.login-submit-button {\n  height: 52px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-submit-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s;\n}\n\n.login-submit-button:hover::before {\n  left: 100%;\n}\n\n.login-submit-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);\n}\n\n/* 注册部分 */\n.register-section {\n  text-align: center;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.register-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.register-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 登录/注册切换Tab */\n.auth-tabs {\n  margin-bottom: 2rem;\n}\n\n.tab-buttons {\n  display: flex;\n  background: rgba(243, 244, 246, 0.8);\n  border-radius: 12px;\n  padding: 4px;\n  gap: 4px;\n}\n\n.tab-btn {\n  flex: 1;\n  padding: 12px 24px;\n  border: none;\n  background: transparent;\n  border-radius: 8px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: white;\n  color: #3b82f6;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.tab-btn:hover:not(.active) {\n  color: #374151;\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* 注册表单 */\n.register-form {\n  margin-top: 0;\n}\n\n/* 注册方式切换 */\n.register-type-tabs {\n  margin-bottom: 2rem;\n}\n\n.type-tab-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.type-tab-btn {\n  flex: 1;\n  min-width: 120px;\n  padding: 12px 16px;\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 12px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #6b7280;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.type-tab-btn.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  border-color: transparent;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.type-tab-btn:hover:not(.active) {\n  border-color: rgba(59, 130, 246, 0.4);\n  background: rgba(255, 255, 255, 0.95);\n  color: #374151;\n}\n\n/* 注册内容区域 */\n.register-content {\n  margin-top: 1.5rem;\n}\n\n.register-form-content {\n  margin: 0;\n}\n\n/* 验证码输入行 */\n.verify-code-row {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.verify-code-input {\n  flex: 1;\n}\n\n.send-code-btn {\n  flex-shrink: 0;\n  min-width: 120px;\n  border-radius: 8px;\n  border: 2px solid #3b82f6;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.send-code-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  border-color: #2563eb;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.send-code-btn:disabled {\n  background: #e5e7eb;\n  border-color: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* 用户协议 */\n.agreement-section {\n  margin: 1.5rem 0;\n  padding: 1rem;\n  background: rgba(59, 130, 246, 0.05);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 12px;\n}\n\n.agreement-checkbox {\n  color: #374151;\n  font-size: 0.9rem;\n  line-height: 1.6;\n}\n\n.agreement-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  transition: color 0.3s ease;\n}\n\n.agreement-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 注册按钮 */\n.submit-section {\n  margin: 2rem 0 1.5rem;\n}\n\n.register-submit-btn {\n  width: 100%;\n  height: 48px;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  font-size: 1rem;\n  font-weight: 700;\n  letter-spacing: 0.5px;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\n}\n\n.register-submit-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);\n}\n\n.register-submit-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 登录提示 */\n.login-prompt {\n  text-align: center;\n  margin-top: 1.5rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(229, 231, 235, 0.8);\n}\n\n.login-text {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin-right: 0.5rem;\n}\n\n.login-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.login-link:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* 微信注册 */\n.wechat-register-container {\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.wechat-qr-section {\n  margin-bottom: 2rem;\n}\n\n.qr-code-container {\n  display: inline-block;\n  padding: 20px;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  margin-bottom: 1.5rem;\n}\n\n.qr-code-image {\n  width: 200px;\n  height: 200px;\n  border-radius: 8px;\n}\n\n.qr-loading {\n  width: 200px;\n  height: 200px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n}\n\n.qr-instructions h4 {\n  color: #374151;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin: 0 0 1rem 0;\n}\n\n.qr-instructions p {\n  color: #6b7280;\n  font-size: 0.9rem;\n  margin: 0.5rem 0;\n  line-height: 1.5;\n}\n\n.invite-info {\n  margin: 1.5rem 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .login-main {\n    max-width: 1200px;\n    padding: 0 1.5rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .login-main {\n    flex-direction: column;\n    max-width: 800px;\n    padding: 6rem 1rem 2rem; /* 平板端保持顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 减少margin */\n    padding: 2rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 减少margin */\n    max-width: none;\n  }\n\n  .brand-title {\n    font-size: 2.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n\n  .login-main {\n    padding: 5rem 0.5rem 2rem; /* 移动端减少顶部padding */\n  }\n\n  .login-info {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1.5rem;\n  }\n\n  .login-container {\n    margin: 0 0 1rem; /* 移动端减少margin */\n    padding: 1rem;\n  }\n\n  .login-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .brand-title {\n    font-size: 2rem;\n  }\n\n  .login-title {\n    font-size: 1.5rem;\n  }\n\n  .feature-highlights {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .feature-item {\n    padding: 1rem;\n  }\n\n  .feature-item:hover {\n    transform: translateY(-2px);\n  }\n\n  .social-buttons-horizontal {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-row {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .captcha-image-container {\n    align-self: stretch;\n  }\n\n  .captcha-image {\n    width: 100%;\n  }\n\n  .tab-buttons {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .tab-btn {\n    font-size: 0.8rem;\n    padding: 0.5rem;\n  }\n\n  .verify-code-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .send-code-btn {\n    width: 100%;\n  }\n}\n\n@media (max-width: 480px) {\n\n  .login-card {\n    padding: 1.5rem 1rem;\n    border-radius: 16px;\n  }\n\n  .brand-title {\n    font-size: 1.8rem;\n  }\n\n  .login-title {\n    font-size: 1.3rem;\n  }\n\n  .input-group {\n    margin-bottom: 1rem;\n  }\n\n  .login-submit-button {\n    height: 48px;\n    font-size: 1rem;\n  }\n\n  .social-btn-large {\n    padding: 0.75rem;\n    font-size: 0.8rem;\n  }\n\n  .social-btn-large .anticon {\n    font-size: 1rem;\n  }\n\n  .tab-buttons {\n    grid-template-columns: 1fr;\n    gap: 0.25rem;\n  }\n\n  .tab-btn {\n    flex-direction: row;\n    justify-content: center;\n    gap: 0.5rem;\n    font-size: 0.8rem;\n  }\n\n  .qr-code-image {\n    width: 150px;\n    height: 150px;\n  }\n}\n", null]}