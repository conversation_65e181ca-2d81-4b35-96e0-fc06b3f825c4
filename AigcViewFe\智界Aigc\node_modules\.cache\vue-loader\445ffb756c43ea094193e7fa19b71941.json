{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue", "mtime": 1753756307330}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Success.vue?vue&type=template&id=214e74b0&scoped=true&\"\nimport script from \"./Success.vue?vue&type=script&lang=js&\"\nexport * from \"./Success.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Success.vue?vue&type=style&index=0&id=214e74b0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"214e74b0\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\AigcView_zj\\\\AigcViewFe\\\\智界Aigc\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('214e74b0')) {\n      api.createRecord('214e74b0', component.options)\n    } else {\n      api.reload('214e74b0', component.options)\n    }\n    module.hot.accept(\"./Success.vue?vue&type=template&id=214e74b0&scoped=true&\", function () {\n      api.rerender('214e74b0', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/payment/Success.vue\"\nexport default component.exports"]}