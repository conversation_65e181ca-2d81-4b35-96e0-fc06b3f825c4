{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue?vue&type=template&id=033d3257&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue", "mtime": 1753756420429}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"payment-failure-page\"},[_c('div',{staticClass:\"failure-container\"},[_c('div',{staticClass:\"failure-icon\"},[_c('a-icon',{attrs:{\"type\":\"close-circle\",\"theme\":\"filled\"}})],1),_c('h1',{staticClass:\"failure-title\"},[_vm._v(\"支付失败\")]),_vm._m(0),(_vm.orderInfo)?_c('div',{staticClass:\"order-info\"},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"订单号：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.orderInfo.orderId))])]),(_vm.orderInfo.amount)?_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"订单金额：\")]),_c('span',{staticClass:\"value amount\"},[_vm._v(\"¥\"+_vm._s(_vm.orderInfo.amount))])]):_vm._e(),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"失败时间：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.formatTime(new Date())))])])]):_vm._e(),_c('div',{staticClass:\"action-buttons\"},[_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\"},on:{\"click\":_vm.retryPayment}},[_vm._v(\"\\n        重新支付\\n      \")]),_c('a-button',{staticStyle:{\"margin-left\":\"16px\"},attrs:{\"size\":\"large\"},on:{\"click\":_vm.goToUserCenter}},[_vm._v(\"\\n        查看订单\\n      \")]),_c('a-button',{staticStyle:{\"margin-left\":\"16px\"},attrs:{\"size\":\"large\"},on:{\"click\":_vm.goHome}},[_vm._v(\"\\n        返回首页\\n      \")])],1),_c('div',{staticClass:\"help-info\"},[_c('a-alert',{attrs:{\"message\":\"需要帮助？\",\"description\":\"如果问题持续存在，请联系客服：400-123-4567 或发送邮件至 <EMAIL>\",\"type\":\"warning\",\"show-icon\":\"\"}})],1)])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"failure-message\"},[_c('p',[_vm._v(\"很抱歉，您的支付未能成功完成。\")]),_c('p',[_vm._v(\"可能的原因：\")]),_c('ul',{staticClass:\"reason-list\"},[_c('li',[_vm._v(\"支付过程中网络连接中断\")]),_c('li',[_vm._v(\"支付信息验证失败\")]),_c('li',[_vm._v(\"支付宝账户余额不足\")]),_c('li',[_vm._v(\"银行卡限额或状态异常\")])])])}]\n\nexport { render, staticRenderFns }"]}