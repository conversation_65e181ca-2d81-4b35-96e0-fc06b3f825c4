{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue", "mtime": 1753687927441}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getFileAccessHttpUrl } from '@/api/manage'\nimport { getUnreadNotificationCount } from '@/api/notifications'\n\nexport default {\n  name: 'UserCenterSidebar',\n  props: {\n    currentPage: {\n      type: String,\n      default: 'overview'\n    },\n    userInfo: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      isOnline: true,\n      defaultAvatar: '/default-avatar.png', // 本地降级头像\n      unreadNotificationCount: 0,\n      sidebarTop: 140, // 默认顶部距离（增加20px）\n      footerSafeDistance: 150 // 页脚安全距离（增加50px）\n    }\n  },\n  computed: {\n    menuItems() {\n      return [\n        {\n          key: 'overview',\n          title: '概览',\n          icon: 'anticon anticon-dashboard',\n          description: '查看账户概况和统计数据'\n        },\n        {\n          key: 'profile',\n          title: '账户设置',\n          icon: 'anticon anticon-setting',\n          description: '管理个人信息和安全设置'\n        },\n        {\n          key: 'credits',\n          title: '账户管理',\n          icon: 'anticon anticon-wallet',\n          description: '查看余额和交易记录'\n        },\n        {\n          key: 'orders',\n          title: '订单记录',\n          icon: 'anticon anticon-shopping',\n          description: '查看购买历史和订单状态'\n        },\n        {\n          key: 'usage',\n          title: '使用记录',\n          icon: 'anticon anticon-bar-chart',\n          description: '查看API调用和插件使用'\n        },\n        {\n          key: 'notifications',\n          title: '系统通知',\n          icon: 'anticon anticon-bell',\n          description: '查看系统消息和通知',\n          badge: this.unreadNotificationCount > 0 ? (this.unreadNotificationCount > 99 ? '99+' : this.unreadNotificationCount.toString()) : null\n        }\n        // 🚫 临时注释掉会员服务和推荐奖励功能\n        // {\n        //   key: 'membership',\n        //   title: '会员服务',\n        //   icon: 'anticon anticon-crown',\n        //   description: '管理会员订阅和特权',\n        //   badge: (() => {\n        //     const role = this.userInfo && this.userInfo.currentRole\n        //     if (!role) return null\n\n        //     // VIP用户显示对应的徽章\n        //     if (role === 'VIP' || role === 'vip' || role === 'VIP会员') return 'VIP'\n        //     if (role === 'SVIP' || role === 'svip' || role === 'SVIP会员') return 'SVIP'\n        //     if (role === 'admin' || role === 'ADMIN' || role === '管理员') return 'ADMIN'\n\n        //     return null // 普通用户不显示徽章\n        //   })()\n        // },\n        // {\n        //   key: 'referral',\n        //   title: '推荐奖励',\n        //   icon: 'anticon anticon-team',\n        //   description: '推荐好友获得奖励'\n        // }\n      ]\n    },\n    memberLevelClass() {\n      const role = (this.userInfo && this.userInfo.currentRole) || 'user'\n      return {\n        'level-user': role === 'user' || role === 'USER' || role === '普通用户',\n        'level-vip': role === 'vip' || role === 'VIP' || role === 'VIP会员',\n        'level-svip': role === 'svip' || role === 'SVIP' || role === 'SVIP会员',\n        'level-admin': role === 'admin' || role === 'ADMIN' || role === '管理员'\n      }\n    },\n    memberLevelText() {\n      const role = (this.userInfo && this.userInfo.currentRole) || 'user'\n\n      const roleMap = {\n        // 小写映射\n        'user': '普通用户',\n        'vip': 'VIP会员',\n        'svip': 'SVIP会员',\n        'admin': '管理员',\n        // 大写映射（数据库实际存储）\n        'USER': '普通用户',\n        'VIP': 'VIP会员',\n        'SVIP': 'SVIP会员',\n        'ADMIN': '管理员',\n        // 中文显示名映射\n        '普通用户': '普通用户',\n        'VIP会员': 'VIP会员',\n        'SVIP会员': 'SVIP会员',\n        '管理员': '管理员'\n      }\n\n      return roleMap[role] || '普通用户'\n    },\n    avatarUrl() {\n      const avatar = this.userInfo && this.userInfo.avatar\n      if (!avatar) {\n        return this.defaultAvatar\n      }\n\n      // 如果是完整的URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是相对路径，使用getFileAccessHttpUrl转换\n      return getFileAccessHttpUrl(avatar) || this.defaultAvatar\n    }\n  },\n  async mounted() {\n    console.log('🔔 Sidebar: 组件已挂载，开始加载通知数量')\n    await this.loadUnreadNotificationCount()\n    this.loadDefaultAvatar()\n\n    // 监听通知更新事件\n    this.$bus.$on('notification-updated', this.handleNotificationUpdated)\n    console.log('🔔 Sidebar: 已设置通知更新事件监听')\n\n    // 添加滚动监听，处理页脚安全距离\n    this.initScrollListener()\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    this.$bus.$off('notification-updated', this.handleNotificationUpdated)\n    // 移除滚动监听\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  methods: {\n    // 加载TOS默认头像URL\n    async loadDefaultAvatar() {\n      try {\n        const response = await this.$http.get('/sys/common/default-avatar-url')\n        if (response && response.success && response.result) {\n          this.defaultAvatar = response.result\n          console.log('🎯 Sidebar: 已加载TOS默认头像:', this.defaultAvatar)\n        }\n      } catch (error) {\n        console.warn('⚠️ Sidebar: 获取TOS默认头像失败，使用本地降级:', error)\n        // 保持本地默认头像作为降级方案\n      }\n    },\n\n    handleMenuClick(item) {\n      this.$emit('menu-change', item.key)\n    },\n    \n    handleRecharge() {\n      this.$emit('action', 'recharge')\n    },\n    \n    handleUpgrade() {\n      this.$emit('action', 'upgrade')\n    },\n    \n    formatBalance(balance) {\n      console.log('🔍 Sidebar: formatBalance 接收到的余额:', balance)\n      console.log('🔍 Sidebar: userInfo:', this.userInfo)\n      console.log('🔍 Sidebar: userInfo.accountBalance:', this.userInfo && this.userInfo.accountBalance)\n      if (!balance) return '0.00'\n      return parseFloat(balance).toFixed(2)\n    },\n\n    // 获取未读通知数量\n    async loadUnreadNotificationCount() {\n      try {\n        console.log('🔔 Sidebar: 开始获取未读通知数量...')\n        const response = await getUnreadNotificationCount()\n\n        if (response.success && response.result) {\n          this.unreadNotificationCount = response.result.unreadCount || 0\n          console.log('🔔 Sidebar: 未读通知数量:', this.unreadNotificationCount)\n        } else {\n          console.error('🔔 Sidebar: 获取未读通知数量失败:', response.message)\n        }\n      } catch (error) {\n        console.error('🔔 Sidebar: 获取未读通知数量异常:', error)\n      }\n    },\n\n    // 处理通知更新事件\n    handleNotificationUpdated(eventData) {\n      console.log('🔔 Sidebar: 收到通知更新事件', eventData)\n      // 重新获取未读通知数量\n      this.loadUnreadNotificationCount()\n    },\n\n    // 初始化滚动监听\n    initScrollListener() {\n      this.handleScroll = this.throttle(this.calculateSidebarPosition, 16) // 60fps\n      window.addEventListener('scroll', this.handleScroll, { passive: true })\n      // 初始计算一次\n      this.calculateSidebarPosition()\n    },\n\n    // 计算侧边栏位置，避免压到页脚\n    calculateSidebarPosition() {\n      // 在小屏幕上不执行sticky逻辑\n      if (window.innerWidth <= 1200) {\n        return\n      }\n\n      const footer = document.querySelector('.website-footer')\n      if (!footer) return\n\n      const sidebar = this.$el\n      if (!sidebar) return\n\n      const footerRect = footer.getBoundingClientRect()\n      const sidebarRect = sidebar.getBoundingClientRect()\n      const windowHeight = window.innerHeight\n\n      // 计算页脚距离顶部的距离\n      const footerTop = footerRect.top\n      // 计算侧边栏需要的空间（高度 + 安全距离）\n      const sidebarNeededSpace = sidebarRect.height + this.footerSafeDistance\n\n      // 如果页脚即将进入视窗，调整侧边栏位置\n      if (footerTop < windowHeight && footerTop < sidebarNeededSpace + 140) {\n        // 计算新的top值，确保不压到页脚\n        const newTop = Math.max(20, footerTop - sidebarRect.height - this.footerSafeDistance)\n        this.sidebarTop = newTop\n        console.log('🔧 Sidebar: 调整位置避免压到页脚，新top值:', newTop)\n      } else {\n        // 恢复默认位置\n        if (this.sidebarTop !== 140) {\n          this.sidebarTop = 140\n          console.log('🔧 Sidebar: 恢复默认位置，top值:', 140)\n        }\n      }\n    },\n\n    // 节流函数，优化滚动性能\n    throttle(func, delay) {\n      let timeoutId\n      let lastExecTime = 0\n      return function (...args) {\n        const currentTime = Date.now()\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args)\n          lastExecTime = currentTime\n        } else {\n          clearTimeout(timeoutId)\n          timeoutId = setTimeout(() => {\n            func.apply(this, args)\n            lastExecTime = Date.now()\n          }, delay - (currentTime - lastExecTime))\n        }\n      }\n    }\n  }\n}\n", null]}