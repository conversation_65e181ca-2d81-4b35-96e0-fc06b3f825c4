import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts'

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [
  // ✅ 移除所有静态后台路由定义，完全由动态路由处理

      // // dashboard
      // {
      //   path: '/dashboard',
      //   name: 'dashboard',
      //   redirect: '/dashboard/workplace',
      //   component: RouteView,
      //   meta: { title: '仪表盘', icon: 'dashboard', permission: [ 'dashboard' ] },
      //   children: [
      //     {
      //       path: '/dashboard/analysis',
      //       name: 'Analysis',
      //       component: () => import('@/views/dashboard/Analysis'),
      //       meta: { title: '分析页', permission: [ 'dashboard' ] }
      //     },
      //     {
      //       path: '/dashboard/monitor',
      //       name: 'Monitor',
      //       hidden: true,
      //       component: () => import('@/views/dashboard/Monitor'),
      //       meta: { title: '监控页', permission: [ 'dashboard' ] }
      //     },
      //     {
      //       path: '/dashboard/workplace',
      //       name: 'Workplace',
      //       component: () => import('@/views/dashboard/Workplace'),
      //       meta: { title: '工作台', permission: [ 'dashboard' ] }
      //     }
      //   ]
      // },
      //
      // // forms
      // {
      //   path: '/form',
      //   redirect: '/form/basic-form',
      //   component: PageView,
      //   meta: { title: '表单页', icon: 'form', permission: [ 'form' ] },
      //   children: [
      //     {
      //       path: '/form/base-form',
      //       name: 'BaseForm',
      //       component: () => import('@/views/form/BasicForm'),
      //       meta: { title: '基础表单', permission: [ 'form' ] }
      //     },
      //     {
      //       path: '/form/step-form',
      //       name: 'StepForm',
      //       component: () => import('@/views/form/stepForm/StepForm'),
      //       meta: { title: '分步表单', permission: [ 'form' ] }
      //     },
      //     {
      //       path: '/form/advanced-form',
      //       name: 'AdvanceForm',
      //       component: () => import('@/views/form/advancedForm/AdvancedForm'),
      //       meta: { title: '高级表单', permission: [ 'form' ] }
      //     }
      //   ]
      // },
      //
      // // list
      // {
      //   path: '/list',
      //   name: 'list',
      //   component: PageView,
      //   redirect: '/list/query-list',
      //   meta: { title: '列表页', icon: 'table', permission: [ 'table' ] },
      //   children: [
      //     {
      //       path: '/list/query-list',
      //       name: 'QueryList',
      //       component: () => import('@/views/list/TableList'),
      //       meta: { title: '查询表格', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/edit-table',
      //       name: 'EditList',
      //       component: () => import('@/views/list/TableInnerEditList'),
      //       meta: { title: '内联编辑表格', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/user-list',
      //       name: 'UserList',
      //       component: () => import('@/views/list/UserList'),
      //       meta: { title: '用户列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/role-list',
      //       name: 'RoleList',
      //       component: () => import('@/views/list/RoleList'),
      //       meta: { title: '角色列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/permission-list',
      //       name: 'PermissionList',
      //       component: () => import('@/views/list/PermissionList'),
      //       meta: { title: '权限列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/basic-list',
      //       name: 'BasicList',
      //       component: () => import('@/views/list/StandardList'),
      //       meta: { title: '标准列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/card',
      //       name: 'CardList',
      //       component: () => import('@/views/list/CardList'),
      //       meta: { title: '卡片列表', permission: [ 'table' ] }
      //     },
      //     {
      //       path: '/list/search',
      //       name: 'SearchList',
      //       component: () => import('@/views/list/search/SearchLayout'),
      //       redirect: '/list/search/article',
      //       meta: { title: '搜索列表', permission: [ 'table' ] },
      //       children: [
      //         {
      //           path: '/list/search/article',
      //           name: 'SearchArticles',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（文章）', permission: [ 'table' ] }
      //         },
      //         {
      //           path: '/list/search/project',
      //           name: 'SearchProjects',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（项目）', permission: [ 'table' ] }
      //         },
      //         {
      //           path: '/list/search/application',
      //           name: 'SearchApplications',
      //           component: () => import('../views/list/TableList'),
      //           meta: { title: '搜索列表（应用）', permission: [ 'table' ] }
      //         },
      //       ]
      //     },
      //   ]
      // },
      //
      // // profile
      // {
      //   path: '/profile',
      //   name: 'profile',
      //   component: RouteView,
      //   redirect: '/profile/basic',
      //   meta: { title: '详情页', icon: 'profile', permission: [ 'profile' ] },
      //   children: [
      //     {
      //       path: '/profile/basic',
      //       name: 'ProfileBasic',
      //       component: () => import('@/views/profile/basic/Index'),
      //       meta: { title: '基础详情页', permission: [ 'profile' ] }
      //     },
      //     {
      //       path: '/profile/advanced',
      //       name: 'ProfileAdvanced',
      //       component: () => import('@/views/profile/advanced/Advanced'),
      //       meta: { title: '高级详情页', permission: [ 'profile' ] }
      //     }
      //   ]
      // },
      //
      // // result
      // {
      //   path: '/result',
      //   name: 'result',
      //   component: PageView,
      //   redirect: '/result/success',
      //   meta: { title: '结果页', icon: 'check-circle-o', permission: [ 'result' ] },
      //   children: [
      //     {
      //       path: '/result/success',
      //       name: 'ResultSuccess',
      //       component: () => import(/* webpackChunkName: "result" */ '@/views/result/Success'),
      //       meta: { title: '成功', hiddenHeaderContent: true, permission: [ 'result' ] }
      //     },
      //     {
      //       path: '/result/fail',
      //       name: 'ResultFail',
      //       component: () => import(/* webpackChunkName: "result" */ '@/views/result/Error'),
      //       meta: { title: '失败', hiddenHeaderContent: true, permission: [ 'result' ] }
      //     }
      //   ]
      // },
      //
      // // Exception
      // {
      //   path: '/exception',
      //   name: 'exception',
      //   component: RouteView,
      //   redirect: '/exception/403',
      //   meta: { title: '异常页', icon: 'warning', permission: [ 'exception' ] },
      //   children: [
      //     {
      //       path: '/exception/403',
      //       name: 'Exception403',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403'),
      //       meta: { title: '403', permission: [ 'exception' ] }
      //     },
      //     {
      //       path: '/exception/404',
      //       name: 'Exception404',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404'),
      //       meta: { title: '404', permission: [ 'exception' ] }
      //     },
      //     {
      //       path: '/exception/500',
      //       name: 'Exception500',
      //       component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500'),
      //       meta: { title: '500', permission: [ 'exception' ] }
      //     }
      //   ]
      // },
      //
      // // account
      // {
      //   path: '/account',
      //   component: RouteView,
      //   name: 'account',
      //   meta: { title: '个人页', icon: 'user', keepAlive: true, permission: [ 'user' ] },
      //   children: [
      //     {
      //       path: '/account/center',
      //       name: 'center',
      //       component: () => import('@/views/account/center/Index'),
      //       meta: { title: '个人中心', keepAlive: true, permission: [ 'user' ] }
      //     },
      //     {
      //       path: '/account/settings',
      //       name: 'settings',
      //       component: () => import('@/views/account/settings/Index'),
      //       meta: { title: '个人设置', hideHeader: true, keepAlive: true, permission: [ 'user' ]  },
      //       redirect: '/account/settings/base',
      //       alwaysShow: true,
      //       children: [
      //         {
      //           path: '/account/settings/base',
      //           name: 'BaseSettings',
      //           component: () => import('@/views/account/settings/BaseSetting'),
      //           meta: { title: '基本设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/security',
      //           name: 'SecuritySettings',
      //           component: () => import('@/views/account/settings/Security'),
      //           meta: { title: '安全设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/custom',
      //           name: 'CustomSettings',
      //           component: () => import('@/views/account/settings/Custom'),
      //           meta: { title: '个性化设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/binding',
      //           name: 'BindingSettings',
      //           component: () => import('@/views/account/settings/Binding'),
      //           meta: { title: '账户绑定', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //         {
      //           path: '/account/settings/notification',
      //           name: 'NotificationSettings',
      //           component: () => import('@/views/account/settings/Notification'),
      //           meta: { title: '新消息通知', hidden: true, keepAlive: true, permission: [ 'user' ]  }
      //         },
      //       ]
      //     },
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/RegisterResult')
      },
      {
        path: 'alteration',
        name: 'alteration',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
      },
    ]
  },

  // {
  //   path: '/',
  //   name: 'index',
  //   component: TabLayout,
  //   meta: {title: '首页'},
  //   redirect: '/dashboard/workplace',
  //   children: [
  //     {
  //       path: '/online',
  //       name: 'online',
  //       redirect: '/online',
  //       component: RouteView,
  //       meta: {title: '在线开发', icon: 'dashboard', permission: ['dashboard']},
  //       children: [
  //         {
  //           path: '/online/auto/:code',
  //           name: 'report',
  //           component: () => import('@/views/modules/online/cgreport/OnlCgreportAutoList')
  //         },
  //       ]
  //     },
  //   ]
  // },

  {
    // OAuth2 APP页面路由
    path: '/oauth2-app',
    component: BlankLayout,
    redirect: '/oauth2-app/login',
    children: [
      {
        // OAuth2 登录路由
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "oauth2-app.login" */ '@/views/user/oauth2/OAuth2Login')
      },
    ]
  },

  {
    path: '/test',
    component: BlankLayout,
    redirect: '/test/home',
    children: [
      {
        path: 'home',
        name: 'TestHome',
        component: () => import('@/views/Home')
      }
    ]
  },

  // ✅ 官网页面使用简洁的一级路径
  {
    path: '/home',
    name: 'WebsiteHome',
    component: () => import('@/views/website/home/<USER>'),
    meta: {
      title: '智界AIGC - AI内容生成平台',
      description: '基于前沿人工智能技术，为企业和个人提供智能内容生成解决方案'
    }
  },
  {
    path: '/market',
    name: 'WebsiteMarket',
    component: () => import('@/views/website/market/Market.vue'),
    meta: {
      title: '插件中心 - 智界AIGC',
      description: '发现和购买优质AI插件，提升您的创作效率',
      keepAlive: true, // 启用页面缓存
      componentName: 'Market' // 组件名称，用于缓存管理
    }
  },
  {
    path: '/market/plugin/:id',
    name: 'PluginDetail',
    component: () => import('@/views/website/market/PluginDetail.vue'),
    meta: {
      title: '插件详情 - 智界AIGC',
      description: '查看插件详细信息、使用教程和技术说明'
    }
  },
  {
    path: '/cases',
    name: 'WebsiteCases',
    component: () => import('@/views/website/cases/Cases.vue'),
    meta: {
      title: '客户案例 - 智界AIGC',
      description: '查看成功案例，了解智界AIGC如何帮助用户实现创作目标'
    }
  },
  {
    path: '/tutorials',
    name: 'WebsiteTutorials',
    component: () => import('@/views/website/tutorials/Tutorials.vue'),
    meta: {
      title: '教程中心 - 智界AIGC',
      description: '详细的使用教程和操作指南，快速上手智界AIGC'
    }
  },
  {
    path: '/signin',
    name: 'WebsiteSignIn',
    component: () => import('@/views/website/signin/SignIn.vue'),
    meta: {
      title: '签到奖励 - 智界AIGC',
      description: '每日签到获取积分奖励，兑换更多精彩内容'
    }
  },
  {
    path: '/membership',
    name: 'WebsiteMembership',
    component: () => import('@/views/website/membership/Membership.vue'),
    meta: {
      title: '订阅会员 - 智界AIGC',
      description: '成为会员享受更多特权，解锁高级功能'
    }
  },
  {
    path: '/affiliate',
    name: 'WebsiteAffiliate',
    component: () => import('@/views/website/affiliate/Affiliate.vue'),
    meta: {
      title: '邀请奖励 - 智界AIGC',
      description: '邀请好友注册智界AIGC，获得丰厚奖励'
    }
  },
  {
    path: '/usercenter',
    name: 'WebsiteUserCenter',
    component: () => import('@/views/website/usercenter/UserCenter.vue'),
    meta: {
      title: '个人中心 - 智界AIGC',
      description: '管理您的账户信息、订单记录和个人设置',
      requiresAuth: true // 需要登录验证
    }
  },
  {
    path: '/JianYingDraft',
    name: 'JianYingDraft',
    component: () => import('@/views/website/JianYingDraft.vue'),
    meta: {
      title: '剪映小助手 - 智界AIGC',
      description: '智能视频剪辑工具，支持剪映草稿导入，让创作更简单高效'
    }
  },
  {
    path: '/login',
    name: 'WebsiteLogin',
    component: () => import('@/views/website/auth/Login.vue'),
    meta: {
      title: '登录 - 智界AIGC',
      description: '登录您的智界AIGC账户，享受AI内容创作服务'
    }
  },
  {
    path: '/user-agreement',
    name: 'UserAgreement',
    component: () => import('@/views/website/legal/UserAgreement.vue'),
    meta: {
      title: '用户服务协议 - 智界AIGC',
      description: '智界AIGC用户服务协议，了解您的权利和义务'
    }
  },
  {
    path: '/privacy-policy',
    name: 'PrivacyPolicy',
    component: () => import('@/views/website/legal/PrivacyPolicy.vue'),
    meta: {
      title: '隐私政策 - 智界AIGC',
      description: '智界AIGC隐私政策，了解我们如何保护您的个人信息'
    }
  },
  {
    path: '/not-found',
    name: 'WebsiteNotFound',
    component: () => import('@/views/website/exception/NotFound.vue'), // ✅ 修正路径
    meta: {
      title: '页面未找到 - 智界AIGC',
      description: '抱歉，您访问的页面不存在'
    }
  },
  {
    path: '/carousel-test',
    name: 'CarouselTest',
    component: () => import('@/views/website/home/<USER>'),
    meta: {
      title: '轮播图功能测试',
      description: '轮播图组件功能测试页面'
    }
  },
  {
    path: '/route-test',
    name: 'RouteTest',
    component: () => import('@/views/website/test/RouteTest.vue'),
    meta: {
      title: '路由测试 - 智界AIGC',
      description: '路由功能测试页面'
    }
  },
  {
    path: '/error-test',
    name: 'ErrorTest',
    component: () => import('@/views/test/SimpleErrorTest.vue'),
    meta: {
      title: '错误处理测试 - 智界AIGC',
      description: '错误处理功能测试页面'
    }
  },

  // ✅ 根路径处理 - 让 permission.js 处理复杂逻辑
  {
    path: '/',
    beforeEnter: (to, from, next) => {
      // 让路由守卫处理根路径的复杂逻辑
      next()
    }
  },

  // 支付相关页面
  {
    path: '/payment/success',
    name: 'PaymentSuccess',
    component: () => import(/* webpackChunkName: "payment" */ '@/views/payment/Success'),
    meta: {
      title: '支付成功 - 智界AIGC',
      description: '支付成功，感谢您的支持'
    }
  },
  {
    path: '/payment/failure',
    name: 'PaymentFailure',
    component: () => import(/* webpackChunkName: "payment" */ '@/views/payment/Failure'),
    meta: {
      title: '支付失败 - 智界AIGC',
      description: '支付失败，请重试'
    }
  },

  // 异常页面
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },
  {
    path: '/server-error',
    name: 'ServerError',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/ServerError'),
    meta: {
      title: '服务器异常 - 智界AIGC',
      description: '服务器连接异常，请稍后重试'
    }
  },

  // 全局通配符路由 - 必须放在最后
  // 🔧 智能404处理：检查admin用户是否需要加载动态路由
  {
    path: '*',
    component: () => import('@/components/SmartNotFound.vue'),
    meta: {
      title: '页面检查中 - 智界AIGC',
      description: '正在检查页面权限和路由配置'
    }
  }

]
