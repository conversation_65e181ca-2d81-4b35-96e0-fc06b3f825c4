{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue?vue&type=template&id=1e174e22&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue", "mtime": 1753687927441}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"usercenter-sidebar\",style:({ top: _vm.sidebarTop + 'px' })},[_c('div',{staticClass:\"user-info-card\"},[_c('div',{staticClass:\"user-avatar\"},[_c('img',{attrs:{\"src\":_vm.avatarUrl,\"alt\":(_vm.userInfo && _vm.userInfo.nickname) || '用户头像'}}),_c('div',{staticClass:\"avatar-status\",class:{ online: _vm.isOnline }})]),_c('div',{staticClass:\"user-details\"},[_c('h3',{staticClass:\"user-name\"},[_vm._v(_vm._s((_vm.userInfo && _vm.userInfo.nickname) || '智界用户'))]),_c('div',{staticClass:\"user-level\"},[_c('span',{staticClass:\"level-badge\",class:_vm.memberLevelClass},[_vm._v(\"\\n          \"+_vm._s(_vm.memberLevelText)+\"\\n        \")])]),_c('div',{staticClass:\"user-balance\"},[_c('div',{staticClass:\"balance-label\"},[_vm._v(\"账户余额\")]),_c('div',{staticClass:\"balance-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatBalance((_vm.userInfo && _vm.userInfo.accountBalance) || 0)))]),_c('button',{staticClass:\"balance-btn-mini\",on:{\"click\":_vm.handleRecharge}},[_vm._v(\"充值\")])])])]),_c('nav',{staticClass:\"sidebar-nav\"},[_c('ul',{staticClass:\"nav-menu\"},_vm._l((_vm.menuItems),function(item){return _c('li',{key:item.key,staticClass:\"nav-item\"},[_c('a',{staticClass:\"nav-link sidebar-menu-item\",class:{ active: _vm.currentPage === item.key },attrs:{\"href\":\"#\"},on:{\"click\":function($event){$event.preventDefault();return _vm.handleMenuClick(item)}}},[_c('i',{staticClass:\"nav-icon\",class:item.icon}),_c('span',{staticClass:\"nav-text\"},[_vm._v(_vm._s(item.title))]),(item.badge)?_c('span',{staticClass:\"nav-badge\"},[_vm._v(_vm._s(item.badge))]):_vm._e()])])}),0)])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}