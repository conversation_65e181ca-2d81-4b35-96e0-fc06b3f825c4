{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753672621586}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { gsap } from 'gsap';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { isAdmin, getUserRole } from '@/utils/roleUtils';\nimport Vue from 'vue';\nimport LogoImage from '@/components/common/LogoImage.vue';\nexport default {\n  name: 'WebsiteHeader',\n  components: {\n    LogoImage: LogoImage\n  },\n  props: {\n    // 是否使用透明背景（首页专用）\n    transparent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [],\n      userInfo: {},\n      isLoggedIn: false,\n      isAdmin: false\n    };\n  },\n  computed: {\n    isTransparent: function isTransparent() {\n      return this.transparent && !this.isScrolled;\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadMenuData();\n\n            case 2:\n              _context.next = 4;\n              return this.checkUserStatus();\n\n            case 4:\n              this.initScrollListener();\n              this.initNavbarAnimations();\n\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    loadMenuData: function () {\n      var _loadMenuData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                try {\n                  // TODO: 从API获取菜单数据\n                  // const response = await this.$http.get('/api/website/header/menu')\n                  // this.menuItems = response.data\n                  // 临时数据，后续替换为API调用\n                  this.menuItems = [{\n                    name: '首页',\n                    path: '/home',\n                    icon: 'home'\n                  }, {\n                    name: '插件中心',\n                    path: '/market',\n                    icon: 'shop'\n                  }, // { name: '客户案例', path: '/cases', icon: 'trophy' },\n                  // { name: '教程中心', path: '/tutorials', icon: 'book' },\n                  // { name: '签到奖励', path: '/signin', icon: 'gift' },\n                  // { name: '订阅会员', path: '/membership', icon: 'crown' },\n                  {\n                    name: '客户案例',\n                    path: '',\n                    icon: 'trophy'\n                  }, {\n                    name: '教程中心',\n                    path: '',\n                    icon: 'book'\n                  }, {\n                    name: '签到奖励',\n                    path: '',\n                    icon: 'gift'\n                  }, {\n                    name: '订阅会员',\n                    path: '',\n                    icon: 'crown'\n                  }, {\n                    name: '邀请奖励',\n                    path: '/affiliate',\n                    icon: 'team'\n                  }, {\n                    name: '个人中心',\n                    path: '/usercenter',\n                    icon: 'user'\n                  }];\n                } catch (error) {\n                  console.error('加载菜单数据失败:', error); // 降级方案\n\n                  this.menuItems = [{\n                    name: '首页',\n                    path: '/',\n                    icon: 'home'\n                  }, {\n                    name: '插件中心',\n                    path: '/market',\n                    icon: 'shop'\n                  }, {\n                    name: '个人中心',\n                    path: '/usercenter',\n                    icon: 'user'\n                  }];\n                }\n\n              case 1:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function loadMenuData() {\n        return _loadMenuData.apply(this, arguments);\n      }\n\n      return loadMenuData;\n    }(),\n    initScrollListener: function initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll);\n    },\n    handleScroll: function handleScroll() {\n      this.isScrolled = window.scrollY > 50;\n    },\n    toggleMobileMenu: function toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n    },\n    closeMobileMenu: function closeMobileMenu() {\n      this.mobileMenuOpen = false;\n    },\n    // 🔥 处理桌面端开发中功能点击\n    handleDevelopingClick: function handleDevelopingClick(featureName) {\n      console.log('🎯 桌面端开发中功能点击:', featureName);\n      this.$message.info(\"\".concat(featureName, \"\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85\\uFF01\"), 3);\n    },\n    // 🔥 处理移动端菜单点击\n    handleMobileMenuClick: function handleMobileMenuClick(item) {\n      console.log('🎯 移动端菜单点击:', item.name, 'path:', item.path);\n\n      if (!item.path || item.path === '') {\n        console.log('🎯 移动端开发中功能点击:', item.name);\n        this.$message.info(\"\".concat(item.name, \"\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85\\uFF01\"), 3);\n      } else {\n        // 有效路径，进行跳转\n        this.$router.push(item.path);\n      }\n\n      this.closeMobileMenu();\n    },\n    goHome: function goHome() {\n      this.$router.push('/');\n    },\n    handleLogin: function handleLogin() {\n      this.$router.push('/login');\n    },\n    checkUserStatus: function () {\n      var _checkUserStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var token, userRole;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                // 检查是否有TOKEN\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context3.next = 6;\n                  break;\n                }\n\n                this.isLoggedIn = false;\n                this.isAdmin = false;\n                return _context3.abrupt(\"return\");\n\n              case 6:\n                this.isLoggedIn = true; // 检查用户角色\n\n                _context3.next = 9;\n                return getUserRole();\n\n              case 9:\n                userRole = _context3.sent;\n                _context3.next = 12;\n                return isAdmin();\n\n              case 12:\n                this.isAdmin = _context3.sent;\n                // 获取用户信息\n                this.userInfo = {\n                  username: this.$store.getters.username || '用户',\n                  role: userRole\n                };\n                console.log('🔍 WebsiteHeader用户状态:', {\n                  isLoggedIn: this.isLoggedIn,\n                  isAdmin: this.isAdmin,\n                  userInfo: this.userInfo\n                });\n                _context3.next = 22;\n                break;\n\n              case 17:\n                _context3.prev = 17;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('检查用户状态失败:', _context3.t0);\n                this.isLoggedIn = false;\n                this.isAdmin = false;\n\n              case 22:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 17]]);\n      }));\n\n      function checkUserStatus() {\n        return _checkUserStatus.apply(this, arguments);\n      }\n\n      return checkUserStatus;\n    }(),\n    goToAdmin: function goToAdmin() {\n      // 跳转到后台管理首页\n      this.$router.push('/dashboard/analysis');\n      this.closeMobileMenu();\n    },\n    initNavbarAnimations: function initNavbarAnimations() {\n      var _this = this;\n\n      // 导航栏入场动画 - 添加refs存在检查\n      this.$nextTick(function () {\n        var elements = [_this.$refs.navBrand, _this.$refs.navMenu, _this.$refs.navActions].filter(function (el) {\n          return el;\n        });\n\n        if (elements.length > 0) {\n          gsap.from(elements, {\n            duration: 0.4,\n            y: -20,\n            opacity: 0,\n            ease: \"power2.out\",\n            stagger: 0.05\n          });\n        }\n      });\n    }\n  }\n};", null]}