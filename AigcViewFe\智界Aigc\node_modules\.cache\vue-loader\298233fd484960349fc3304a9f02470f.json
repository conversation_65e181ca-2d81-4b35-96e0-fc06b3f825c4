{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue", "mtime": 1753702910988}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport StatsCard from '../components/StatsCard.vue'\nimport {\n  getReferralStats,\n  generateReferralLink,\n  getReferralList\n} from '@/api/usercenter'\n\nexport default {\n  name: 'UserCenterReferral',\n  components: {\n    StatsCard\n  },\n  data() {\n    return {\n      loading: true,\n      linkLoading: false,\n      recordLoading: false,\n      withdrawalLoading: false,\n      withdrawalHistoryLoading: false,\n      \n      defaultAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',\n      \n      // 推荐统计\n      referralStats: {\n        totalReferrals: 0,\n        totalRewards: 0,\n        availableRewards: 0,\n        monthlyReferrals: 0\n      },\n      \n      // 佣金比例配置\n      commissionConfig: {\n        userType: 'NORMAL', // 当前用户类型\n        commissionLevel: 1, // 当前佣金等级\n        inviteCount: 0 // 有效邀请数量\n      },\n      \n      // 佣金比例数据\n      normalRate: 30,\n      normalHighRate: 40,\n      normalTopRate: 50,\n      vipRate: 35,\n      vipHighRate: 45,\n      vipTopRate: 50,\n      svipRate: 50,\n      currentCommissionRate: 30,\n      \n      // 推荐链接\n      referralLink: '',\n      showQRModal: false,\n      \n      // 推荐记录\n      recordFilters: {\n        status: '',\n        dateRange: []\n      },\n      referralRecords: [],\n      recordPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      recordColumns: [\n        {\n          title: '好友信息',\n          key: 'friendInfo',\n          width: 200,\n          scopedSlots: { customRender: 'friendInfo' }\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime',\n          width: 150,\n          scopedSlots: { customRender: 'time' }\n        },\n        {\n          title: '奖励金额',\n          dataIndex: 'rewardAmount',\n          key: 'rewardAmount',\n          width: 120,\n          align: 'right',\n          scopedSlots: { customRender: 'rewardAmount' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          scopedSlots: { customRender: 'status' }\n        }\n      ],\n      \n      // 提现相关\n      withdrawalAmount: 50,\n      realName: '',\n      alipayAccount: '',\n      withdrawalInfo: {\n        availableAmount: 0,\n        frozenAmount: 0,\n        minWithdrawalAmount: 50,\n        hasPendingRequest: false,\n        pendingAmount: 0,\n        pendingTime: null,\n        canWithdraw: false,\n        message: ''\n      },\n      showConfirmWithdrawal: false,\n      confirmChecked: false,\n      withdrawalHistory: [],\n      withdrawalPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      withdrawalColumns: [\n        {\n          title: '提现金额',\n          dataIndex: 'withdrawal_amount',\n          key: 'amount',\n          width: 120,\n          align: 'right',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '真实姓名',\n          dataIndex: 'alipay_name',\n          key: 'realName',\n          width: 100\n        },\n        {\n          title: '支付宝账号',\n          dataIndex: 'alipay_account',\n          key: 'alipayAccount',\n          ellipsis: true\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'apply_time',\n          key: 'applyTime',\n          width: 150,\n          scopedSlots: { customRender: 'time' }\n        },\n        {\n          title: '审核时间',\n          dataIndex: 'review_time',\n          key: 'reviewTime',\n          width: 150,\n          scopedSlots: { customRender: 'time' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          scopedSlots: { customRender: 'status' }\n        }\n      ]\n    }\n  },\n  computed: {\n    monthlyTrend() {\n      return {\n        type: 'up',\n        value: 20.5,\n        text: '较上月增长20.5%'\n      }\n    },\n    \n    isFormValid() {\n      return this.withdrawalAmount >= 50 &&\n             this.withdrawalAmount <= this.withdrawalInfo.availableAmount &&\n             this.realName.trim() !== '' &&\n             this.alipayAccount.trim() !== ''\n    }\n  },\n  async mounted() {\n    await this.loadData()\n  },\n  methods: {\n    async loadData() {\n      try {\n        this.loading = true\n        \n        await Promise.all([\n          this.loadReferralStats(),\n          this.loadReferralLink(),\n          this.loadReferralRecords(),\n          this.loadWithdrawalInfo(),\n          this.loadWithdrawalHistory(),\n          this.loadCommissionConfig()\n        ])\n      } catch (error) {\n        console.error('加载推荐数据失败:', error)\n        this.$message.error('加载数据失败，请刷新重试')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async loadReferralStats() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          // 修复：使用 result 字段而不是 data 字段\n          this.referralStats = response.result || {}\n        }\n      } catch (error) {\n        console.error('加载推荐统计失败:', error)\n      }\n    },\n    \n    async loadReferralLink() {\n      try {\n        // 如果已有链接则不重新生成\n        if (!this.referralLink) {\n          await this.handleGenerateLink()\n        }\n      } catch (error) {\n        console.error('加载推荐链接失败:', error)\n      }\n    },\n    \n    async loadReferralRecords() {\n      try {\n        this.recordLoading = true\n        \n        const params = {\n          current: this.recordPagination.current,\n          size: this.recordPagination.pageSize,\n          ...this.recordFilters\n        }\n        \n        const response = await getReferralList(params)\n        if (response.success) {\n          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n          this.referralRecords = (response.result && response.result.records) || []\n          this.recordPagination.total = (response.result && response.result.total) || 0\n        }\n      } catch (error) {\n        console.error('加载推荐记录失败:', error)\n        this.referralRecords = []\n      } finally {\n        this.recordLoading = false\n      }\n    },\n    \n    async loadCommissionConfig() {\n      try {\n        // 获取用户佣金配置信息\n        const response = await this.$http.get('/api/user/commission-config')\n        if (response.data && response.data.success) {\n          this.commissionConfig = response.data.result || {}\n          this.calculateCurrentCommissionRate()\n        }\n      } catch (error) {\n        console.error('加载佣金配置失败:', error)\n        // 使用默认配置\n        this.commissionConfig = {\n          userType: 'NORMAL',\n          commissionLevel: 1,\n          inviteCount: 0\n        }\n        this.calculateCurrentCommissionRate()\n      }\n    },\n    \n    calculateCurrentCommissionRate() {\n      const { userType, inviteCount } = this.commissionConfig\n      \n      if (userType === 'SVIP') {\n        this.currentCommissionRate = this.svipRate\n      } else if (userType === 'VIP') {\n        if (inviteCount >= 30) {\n          this.currentCommissionRate = this.vipTopRate\n        } else if (inviteCount >= 10) {\n          this.currentCommissionRate = this.vipHighRate\n        } else {\n          this.currentCommissionRate = this.vipRate\n        }\n      } else {\n        // NORMAL用户\n        if (inviteCount >= 30) {\n          this.currentCommissionRate = this.normalTopRate\n        } else if (inviteCount >= 10) {\n          this.currentCommissionRate = this.normalHighRate\n        } else {\n          this.currentCommissionRate = this.normalRate\n        }\n      }\n    },\n\n    // 加载提现信息\n    async loadWithdrawalInfo() {\n      try {\n        const response = await this.$http.get('/api/usercenter/withdrawalInfo')\n        if (response.data.success) {\n          this.withdrawalInfo = response.data.result\n        }\n      } catch (error) {\n        console.error('获取提现信息失败:', error)\n      }\n    },\n\n    async loadWithdrawalHistory() {\n      try {\n        this.withdrawalHistoryLoading = true\n        \n        const params = {\n          current: this.withdrawalPagination.current,\n          size: this.withdrawalPagination.pageSize\n        }\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n        if (response && response.success) {\n          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n          this.withdrawalHistory = (response.result && response.result.records) || []\n          this.withdrawalPagination.total = (response.result && response.result.total) || 0\n        } else {\n          this.withdrawalHistory = []\n        }\n      } catch (error) {\n        console.error('加载提现记录失败:', error)\n      } finally {\n        this.withdrawalHistoryLoading = false\n      }\n    },\n    \n    // 推荐链接相关方法\n    async handleGenerateLink() {\n      try {\n        this.linkLoading = true\n        \n        const response = await generateReferralLink()\n        if (response.success) {\n          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n          this.referralLink = (response.result && response.result.link) || ''\n          this.$message.success('推荐链接生成成功')\n        }\n      } catch (error) {\n        console.error('生成推荐链接失败:', error)\n        this.$message.error('生成链接失败，请重试')\n      } finally {\n        this.linkLoading = false\n      }\n    },\n    \n    handleCopyLink() {\n      if (!this.referralLink) {\n        this.$message.warning('请先生成推荐链接')\n        return\n      }\n      \n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(this.referralLink).then(() => {\n          this.$message.success('推荐链接已复制到剪贴板')\n        }).catch(() => {\n          this.fallbackCopyTextToClipboard(this.referralLink)\n        })\n      } else {\n        this.fallbackCopyTextToClipboard(this.referralLink)\n      }\n    },\n    \n    fallbackCopyTextToClipboard(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.left = '-999999px'\n      textArea.style.top = '-999999px'\n      document.body.appendChild(textArea)\n      textArea.focus()\n      textArea.select()\n      \n      try {\n        document.execCommand('copy')\n        this.$message.success('推荐链接已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      }\n      \n      document.body.removeChild(textArea)\n    },\n    \n    handleGenerateQRCode() {\n      if (!this.referralLink) {\n        this.$message.warning('请先生成推荐链接')\n        return\n      }\n      \n      this.showQRModal = true\n      this.$nextTick(() => {\n        this.renderQRCode()\n      })\n    },\n    \n    renderQRCode() {\n      // TODO: 使用 QRCode.js 生成二维码\n      console.log('生成二维码:', this.referralLink)\n    },\n    \n    handleDownloadQR() {\n      this.$message.info('下载二维码功能开发中...')\n    },\n    \n    handleShareToSocial() {\n      this.$message.info('分享到社交媒体功能开发中...')\n    },\n    \n    // 提现相关方法\n    setMaxAmount() {\n      this.withdrawalAmount = this.withdrawalInfo.availableAmount\n    },\n\n    showConfirmModal() {\n      if (!this.isFormValid) {\n        this.$message.warning('请完善提现信息')\n        return\n      }\n\n      // 验证姓名格式\n      if (!/^[\\u4e00-\\u9fa5]{2,4}$/.test(this.realName.trim())) {\n        this.$message.error('请输入正确的中文姓名（2-4个汉字）')\n        return\n      }\n\n      // 验证支付宝账号格式（只允许手机号）\n      const phoneRegex = /^1[3-9]\\d{9}$/\n      if (!phoneRegex.test(this.alipayAccount)) {\n        this.$message.error('请输入正确的手机号')\n        return\n      }\n\n      this.showConfirmWithdrawal = true\n      this.confirmChecked = false\n    },\n\n    async handleConfirmWithdrawal() {\n      try {\n        this.withdrawalLoading = true\n\n        const params = {\n          withdrawalAmount: this.withdrawalAmount,\n          realName: this.realName.trim(),\n          alipayAccount: this.alipayAccount.trim()\n        }\n\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n        if (response.data.success) {\n          this.$message.success('提现申请提交成功，请等待审核')\n\n          // 关闭弹窗\n          this.showConfirmWithdrawal = false\n\n          // 重置表单\n          this.withdrawalAmount = 50\n          this.realName = ''\n          this.alipayAccount = ''\n\n          // 刷新数据\n          await Promise.all([\n            this.loadWithdrawalInfo(),\n            this.loadWithdrawalHistory()\n          ])\n        } else {\n          this.$message.error(response.data.message || '提现申请失败')\n        }\n      } catch (error) {\n        console.error('提现申请失败:', error)\n        this.$message.error('提现申请失败，请重试')\n      } finally {\n        this.withdrawalLoading = false\n      }\n    },\n    \n    // 表格相关方法\n    handleRecordFilterChange() {\n      this.recordPagination.current = 1\n      this.loadReferralRecords()\n    },\n    \n    handleRecordTableChange({ pagination }) {\n      this.recordPagination = { ...this.recordPagination, ...pagination }\n      this.loadReferralRecords()\n    },\n    \n    handleWithdrawalTableChange({ pagination }) {\n      this.withdrawalPagination = { ...this.withdrawalPagination, ...pagination }\n      this.loadWithdrawalHistory()\n    },\n    \n    // 工具方法\n    maskEmail(email) {\n      if (!email) return ''\n      \n      const [username, domain] = email.split('@')\n      if (username.length <= 3) {\n        return `${username[0]}***@${domain}`\n      }\n      \n      return `${username.substring(0, 3)}***@${domain}`\n    },\n    \n    getRecordStatusClass(status) {\n      const classMap = {\n        pending: 'status-pending',\n        confirmed: 'status-confirmed',\n        rewarded: 'status-rewarded'\n      }\n      return classMap[status] || ''\n    },\n    \n    getRecordStatusText(status) {\n      const textMap = {\n        pending: '待确认',\n        confirmed: '已确认',\n        rewarded: '已奖励'\n      }\n      return textMap[status] || '未知状态'\n    },\n    \n    getWithdrawalStatusColor(status) {\n      const colorMap = {\n        1: 'orange',      // 待审核 - 橙色\n        2: 'green',       // 已发放 - 绿色\n        3: 'red'          // 审核拒绝 - 红色\n      }\n      return colorMap[status] || 'default'\n    },\n\n    getWithdrawalStatusText(status, reviewRemark) {\n      const textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝'\n      }\n      let statusText = textMap[status] || '未知状态'\n\n      // 如果是审核拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n    \n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n    \n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n      \n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n", null]}