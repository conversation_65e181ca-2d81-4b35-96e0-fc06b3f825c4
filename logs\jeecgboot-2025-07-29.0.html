<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 29 00:04:56 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,757</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,876</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,881</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,881</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,881</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,882</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,897</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,901</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,905</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:04:56,906</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 29 00:05:00 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:00,775</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:00,802</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 32192 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:00,802</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:05:01,119</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,125</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,126</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,252</td>
<td class="Message">Finished Spring Data repository scanning in 117ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,373</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,373</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,374</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,438</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,439</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,439</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,439</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,439</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,440</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,440</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,440</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,440</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,440</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,593</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,596</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,598</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,598</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,599</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,599</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,600</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,601</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,601</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,602</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,603</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,604</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,605</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,605</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,606</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,606</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,607</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,607</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,608</td>
<td class="Message">Bean &#39;(inner bean)#50598a1a#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,608</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,627</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,630</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,682</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,730</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,732</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$f5d56bbd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:02,766</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,127</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,128</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">239</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,131</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,133</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,161</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,297</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,302</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$279495] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,309</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,318</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$13377bed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,345</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$a9b20174] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,348</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,547</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,553</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,554</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,554</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,661</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:03,661</td>
<td class="Message">Root WebApplicationContext: initialization completed in 2826 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:04,120</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:04,121</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:04,121</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:05,332</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,062</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,063</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,154</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,156</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,157</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,157</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,841</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,842</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,843</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,844</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,844</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:06,844</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:07,142</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:07,393</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:07,918</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:07,928</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:07,969</td>
<td class="Message">剪映蒙版搜索服务初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">JianyingMaskSearchService.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,016</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,024</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,648</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,650</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,661</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,661</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,666</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,668</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,669</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1753718708649&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,669</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,669</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:08,669</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@20706e70</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:11,640</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:12,810</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:13,071</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:13,107</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:13,223</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:13,224</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:13,226</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:14,649</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:14,684</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:14,686</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:14,690</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,051</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,308</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,320</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,330</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,353</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,358</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,360</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,362</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,364</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,370</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,385</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,402</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,406</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,408</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,410</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,412</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,425</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,433</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,458</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,463</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,465</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,468</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,472</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,480</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,495</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,497</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,499</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,501</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,503</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,510</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,520</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,526</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,528</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,532</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,540</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,546</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,558</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,564</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,566</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,568</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,570</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,575</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,579</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,594</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,611</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,613</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,615</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,618</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,620</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,625</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,637</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,639</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,641</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,644</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,647</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,653</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,673</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,681</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,682</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,683</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,691</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,697</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,715</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,722</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,724</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,726</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,737</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,743</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,752</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,754</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,757</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,759</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,761</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,766</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,777</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,778</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,780</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,781</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,784</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,790</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,801</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,803</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,805</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,807</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,808</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,817</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,826</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,828</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,830</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,832</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,834</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:15,839</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,013</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,021</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,026</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,031</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,034</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,048</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,129</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,132</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,136</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,141</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,143</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,148</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,155</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,157</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,159</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,161</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,162</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,167</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,172</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,175</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,178</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,181</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,184</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,191</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,199</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,201</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,202</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,205</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,207</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,212</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,224</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,226</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,228</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,230</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,233</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,241</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,258</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,260</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,268</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,271</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,273</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,280</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,299</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,304</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,306</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,308</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,310</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,336</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,400</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,447</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,449</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:16,454</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,324</td>
<td class="Message">Started JeecgSystemApplication in 17.983 seconds (JVM running for 18.878)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,330</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,417</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,417</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,434</td>
<td class="Message">Completed initialization in 17 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:05:18,603</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:20,958</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:20,959</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:20,983</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:20,985</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:20,985</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:20,992</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:20,992</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:20,999</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,003</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,006</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,007</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,008</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,008</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,596</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,596</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:21,628</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:21,629</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:06:21,764</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,765</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:21,765</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:29,571</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:29,571</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:31,503</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:31,503</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:32,609</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:32,609</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:33,288</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:33,289</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:34,465</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:34,465</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:35,791</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:35,791</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:36,814</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:36,815</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:37,327</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:06:37,327</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,801</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,802</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,809</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,810</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,831</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,832</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,831</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,833</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:09,836</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:09,837</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:09,837</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,838</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,838</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,842</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:09,842</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:09,971</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:09,972</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:10,136</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:10,138</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:10,138</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,172</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,172</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,182</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,184</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,185</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,185</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,185</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,248</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,249</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,250</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,250</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,250</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,250</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,250</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,248</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,369</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,370</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:08:15,427</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,427</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:15,428</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,147</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,148</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,573</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,574</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,950</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:30,951</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,278</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,279</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,615</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,615</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,993</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:31,994</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:32,305</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:32,305</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:32,849</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:32,850</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:33,323</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:08:33,323</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:11,137</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:11,137</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:11,617</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:11,618</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:12,045</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:12,045</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:12,629</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:12,629</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:13,113</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:13,114</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:13,560</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:13,560</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:18,197</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:18,197</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:18,666</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:18,667</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:24,807</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:24,807</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:25,412</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:25,413</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,219</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,219</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,226</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,226</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,227</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,228</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,231</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,232</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,232</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,241</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,241</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,244</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,244</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,244</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,245</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,328</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,329</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:09:26,412</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,412</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:09:26,413</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:49,337</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,419</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,419</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:51,427</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:51,428</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:51,428</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,429</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,432</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,433</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,437</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:51,438</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:51,594</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:51,595</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:55,690</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:55,691</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:55,691</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,278</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,278</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,278</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,282</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,282</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:26:59,282</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,385</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,385</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,385</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,386</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,386</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:26:59,388</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:27:02,998</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:27:03,150</td>
<td class="Message">管理员查询提现申请列表 - 页码: 1, 大小: 10, 状态: null, 用户名: null, 支付宝信息: null</td>
<td class="MethodOfCaller">getWithdrawalList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1194</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,396</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.hH4xmfKDAEW5ipQA-R5VtPcDrGDIxwT9TaM-kbF2PrQ</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,396</td>
<td class="Message">🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,396</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: ***********</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,404</td>
<td class="Message">🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,406</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=1943747721907171330, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,410</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,412</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,412</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,413</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,416</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,416</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,416</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,418</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,418</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,421</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,421</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,421</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.hH4xmfKDAEW5ipQA-R5VtPcDrGDIxwT9TaM-kbF2PrQ</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,422</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: ***********</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:08,430</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=1943747721907171330, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,433</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:08,433</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,082</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,083</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:09,084</td>
<td class="Message">用户 1943747721907171330 已有邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateOrGetInviteCode</td>
<td class="FileOfCaller">InviteCodeGeneratorServiceImpl.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:09,084</td>
<td class="Message">为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1496</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:09,084</td>
<td class="Message">生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralLink</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1518</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,085</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,086</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,086</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,086</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,087</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,087</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,088</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,088</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,094</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:09,094</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:09,213</td>
<td class="Message">开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3724</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:09,213</td>
<td class="Message">域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3743</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:12,075</td>
<td class="Message">二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png</td>
<td class="MethodOfCaller">generateReferralQRCode</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3755</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:12,075</td>
<td class="Message"> LogContent length : 7</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 00:30:12,075</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:24,969</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,732</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,735</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,736</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,736</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,736</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,743</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,744</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,746</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 00:30:32,747</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 29 14:36:33 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:33,194</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:33,220</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 5880 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:33,220</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:33,569</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:34,791</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:34,796</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:34,939</td>
<td class="Message">Finished Spring Data repository scanning in 136ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,091</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,091</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,096</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,172</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,177</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,338</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,343</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,344</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,348</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,348</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,350</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,350</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,352</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,355</td>
<td class="Message">Bean &#39;(inner bean)#3b0d3a63#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,355</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,373</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,374</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,431</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,479</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,485</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$8b0fb481] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,520</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,944</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,947</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">239</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,949</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,949</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:35,979</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,125</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,128</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9561dd59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,137</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,148</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$a871c4b1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,181</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3eec4a38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,184</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,415</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,422</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,425</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,425</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,576</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:36,577</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3322 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:37,177</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:37,180</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:37,180</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:38,457</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,226</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,226</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,335</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,339</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,340</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:39,340</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,422</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,422</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,425</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,428</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,428</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,428</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:40,684</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,039</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,703</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,714</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,754</td>
<td class="Message">剪映蒙版搜索服务初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">JianyingMaskSearchService.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,804</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:41,813</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,394</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,397</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,407</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,407</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,413</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,416</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,416</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1753771002395&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,418</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,418</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:42,418</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d2a4598</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:45,148</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,294</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,459</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,492</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,589</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,589</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:46,595</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:47,731</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:47,767</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:47,767</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:47,772</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,011</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,211</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,220</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,237</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,255</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,258</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,260</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,262</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,262</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,267</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,273</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,284</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,289</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,290</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,290</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,295</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,302</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,308</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,327</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,332</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,334</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,337</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,337</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,348</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,361</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,361</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,361</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,361</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,367</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,373</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,381</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,385</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,387</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,389</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,396</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,402</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,414</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,418</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,420</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,420</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,420</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,428</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,432</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,444</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,461</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,465</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,467</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,467</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,467</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,473</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,485</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,487</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,487</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,490</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,491</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,497</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,514</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,521</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,524</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,526</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,532</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,538</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,557</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,563</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,565</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,567</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,573</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,579</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,589</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,590</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,590</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,590</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,595</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,612</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,626</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,627</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,627</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,630</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,631</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,636</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,643</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,645</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,645</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,648</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,649</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,655</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,662</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,662</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,665</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,667</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,669</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,673</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,812</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,820</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,823</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,827</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,827</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,837</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,914</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,918</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,920</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,922</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,922</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,927</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,932</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,936</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,938</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,942</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,943</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,949</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,954</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,955</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,957</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,957</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,960</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,962</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,971</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,973</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,973</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,973</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,978</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,979</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,990</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,990</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,996</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,997</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:48,997</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,002</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,014</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,018</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,024</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,027</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,027</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,037</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,053</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,057</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,059</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,061</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,061</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,083</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,137</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,185</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,185</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:49,190</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:50,973</td>
<td class="Message">Started JeecgSystemApplication in 18.269 seconds (JVM running for 19.345)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:50,980</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,637</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,637</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,648</td>
<td class="Message">Completed initialization in 11 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,783</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,783</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,783</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,789</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,789</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:51,789</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,494</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,494</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,494</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,495</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,495</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:52,495</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,457</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,457</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">475</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,457</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">259</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,458</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">140</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,477</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">144</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,492</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=155, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,493</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,493</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,493</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">604</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,494</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">189</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,494</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,494</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,494</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,494</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,495</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,495</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,495</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,495</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:54,501</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,504</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:54,505</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:57,113</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:57,123</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3388</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:57,125</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:57,125</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:36:57,125</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:57,127</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:36:57,127</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:03,387</td>
<td class="Message">💰 创建充值订单 - 用户: admin, 金额: 1, 支付方式: alipay</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1775</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:03,400</td>
<td class="Message">💰 充值订单创建成功 - 订单号: RECHARGE_1753771023390_e9ca23d6</td>
<td class="MethodOfCaller">createRechargeOrder</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1832</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:03,406</td>
<td class="Message"> LogContent length : 6</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:03,406</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:03,420</td>
<td class="Message">💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753771023390_e9ca23d6, 金额: 1</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:03,420</td>
<td class="Message">💰 创建支付宝支付订单 - 订单号: RECHARGE_1753771023390_e9ca23d6, 金额: 1</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-29 14:37:03,431</td>
<td class="Message">💰 创建支付宝支付订单失败</td>
<td class="MethodOfCaller">createPayOrder</td>
<td class="FileOfCaller">AlipayController.java</td>
<td class="LineOfCaller">73</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&amp;biz_content={&quot;out_trade_no&quot;:&quot;RECHARGE_1753771023390_e9ca23d6&quot;,&quot;total_amount&quot;:&quot;1&quot;,&quot;subject&quot;:&quot;智界Aigc账户充值&quot;,&quot;body&quot;:&quot;充值金额：¥1&quot;,&quot;product_code&quot;:&quot;FAST_INSTANT_TRADE_PAY&quot;}&amp;charset=UTF-8&amp;format=json&amp;method=alipay.trade.page.pay&amp;sign_type=RSA2&amp;timestamp=2025-07-29 14:37:03&amp;version=1.0，charset=UTF-8，privateKeySize=0
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:25)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.AbstractAlipayClient.getRequestHolderWithSign(AbstractAlipayClient.java:836)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:634)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:629)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.service.AlipayService.createPayOrder(AlipayService.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.AlipayController.createPayOrder(AlipayController.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.AlipayController$$FastClassBySpringCGLIB$$d413598a.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:57)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.AlipayController$$EnhancerBySpringCGLIB$$b92fe164.createPayOrder(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&amp;biz_content={&quot;out_trade_no&quot;:&quot;RECHARGE_1753771023390_e9ca23d6&quot;,&quot;total_amount&quot;:&quot;1&quot;,&quot;subject&quot;:&quot;智界Aigc账户充值&quot;,&quot;body&quot;:&quot;充值金额：¥1&quot;,&quot;product_code&quot;:&quot;FAST_INSTANT_TRADE_PAY&quot;}&amp;charset=UTF-8&amp;format=json&amp;method=alipay.trade.page.pay&amp;sign_type=RSA2&amp;timestamp=2025-07-29 14:37:03&amp;version=1.0，charset=UTF-8，privateKeySize=0
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:86)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.internal.util.AlipaySignature.rsaSign(AlipaySignature.java:422)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:23)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 120 common frames omitted
<br />Caused by: com.alipay.api.AlipayApiException: 私钥[privateKey]不可为空
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 122 common frames omitted
</td></tr>
<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:03,432</td>
<td class="Message"> LogContent length : 9</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:03,432</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:59,226</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:59,235</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:59,238</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:59,238</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:37:59,240</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3388</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:59,244</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:37:59,244</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:41:19,434</td>
<td class="Message">🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1573</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:41:19,450</td>
<td class="Message">🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1</td>
<td class="MethodOfCaller">getTransactionList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">1657</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:41:19,452</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:41:19,453</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:41:19,454</td>
<td class="Message">💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00</td>
<td class="MethodOfCaller">calculateMonthlyConsumption</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">3388</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:41:19,457</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:41:19,457</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,500</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,503</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,503</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,503</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,504</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,509</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,511</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,514</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:48:57,514</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Jul 29 14:49:01 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:01,088</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:01,109</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 13836 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:01,110</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-29 14:49:01,415</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,543</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,545</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,679</td>
<td class="Message">Finished Spring Data repository scanning in 124ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,803</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,804</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,805</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,884</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,885</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,885</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,885</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,885</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,886</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,886</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,886</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,886</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:02,886</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,049</td>
<td class="Message">Bean &#39;(inner bean)#437ed416&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,052</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,053</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,053</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,054</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,054</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,055</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,055</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,056</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,057</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,058</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,058</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,059</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,060</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,060</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,061</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,061</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,062</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,062</td>
<td class="Message">Bean &#39;(inner bean)#437ed416#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,062</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,080</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,083</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,139</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,194</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,196</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$ba91bd19] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,235</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,647</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,649</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">239</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,651</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,654</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,685</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,824</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,829</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c4e3e5f1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,835</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,845</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$d7f3cd49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,874</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$6e6e52d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:03,877</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,106</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,114</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,114</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,114</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,258</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,258</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3120 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,765</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,766</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:04,766</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,036</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,757</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,757</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,850</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,853</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,853</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:06,853</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,726</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,726</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,729</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,730</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,731</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:07,731</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:08,258</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:08,618</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,095</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,105</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,142</td>
<td class="Message">剪映蒙版搜索服务初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">JianyingMaskSearchService.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,185</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,194</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,766</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,769</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,778</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,778</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,782</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,784</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,784</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1753771749768&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,785</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,785</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:09,785</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@628f0936</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:12,910</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:13,920</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:14,069</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:14,099</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:14,197</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:14,198</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:14,200</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,117</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,145</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,147</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,151</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,420</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,649</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,662</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,673</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,698</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,702</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,705</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,708</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,710</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,716</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,723</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,739</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,744</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,747</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,749</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,752</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,760</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,767</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,789</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,792</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,794</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,796</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,800</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,807</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,820</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,822</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,824</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,825</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,827</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,832</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,840</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,844</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,846</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,848</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,854</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,860</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,873</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,879</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,880</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,882</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,883</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,889</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,894</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,911</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,928</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,930</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,932</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,935</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,937</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,943</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,954</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,955</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,958</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,960</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,961</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,968</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,989</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,997</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:15,999</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,002</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,009</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,015</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,033</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,040</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,042</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,043</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,055</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,061</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,072</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,074</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,076</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,077</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,082</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,092</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,102</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,103</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,104</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,105</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,107</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,112</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,119</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,121</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,122</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,124</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,126</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,131</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,137</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,139</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,141</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,142</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,144</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,148</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,312</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,319</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,324</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,328</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,331</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,345</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,428</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,434</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,436</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,439</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,441</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,447</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,453</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,455</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,456</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,458</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,459</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,465</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,470</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,471</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,472</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,474</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,477</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,481</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,487</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,489</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,491</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,493</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,495</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,500</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,513</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,515</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,518</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,521</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,523</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,528</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,544</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,546</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,555</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,557</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,560</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,584</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,604</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,608</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,610</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,612</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,614</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,633</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,688</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,725</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,727</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:16,730</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:18,595</td>
<td class="Message">Started JeecgSystemApplication in 17.851 seconds (JVM running for 18.767)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:18,602</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:19,595</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:19,596</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-29 14:49:19,614</td>
<td class="Message">Completed initialization in 18 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>
