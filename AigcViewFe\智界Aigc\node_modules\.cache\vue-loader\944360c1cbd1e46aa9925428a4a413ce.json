{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=style&index=0&id=e299c5fe&lang=less&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749980456032}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.withdrawal-management {\n  padding: 24px;\n  background: #f0f2f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  margin-bottom: 24px;\n\n  h2 {\n    margin: 0 0 8px 0;\n    font-size: 24px;\n    font-weight: 600;\n    color: #262626;\n  }\n\n  p {\n    margin: 0;\n    color: #8c8c8c;\n    font-size: 14px;\n  }\n}\n\n.search-section {\n  margin-bottom: 24px;\n}\n\n// 表格样式\n.ant-table {\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    text-align: center !important;\n  }\n}\n\n// 表格内容样式\n.user-info {\n  .username {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .user-id {\n    font-size: 12px;\n    color: #8c8c8c;\n  }\n}\n\n.amount-info {\n  .amount {\n    font-weight: 600;\n    color: #262626;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n.alipay-info {\n  .name {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .account {\n    font-size: 12px;\n    color: #8c8c8c;\n    font-family: 'Courier New', monospace;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n// 弹窗样式\n.reject-modal {\n  .modal-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.detail-modal {\n  .amount-text {\n    font-weight: 600;\n    color: #52c41a;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .withdrawal-management {\n    padding: 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n\n    .ant-btn {\n      width: 100%;\n    }\n  }\n}\n", null]}