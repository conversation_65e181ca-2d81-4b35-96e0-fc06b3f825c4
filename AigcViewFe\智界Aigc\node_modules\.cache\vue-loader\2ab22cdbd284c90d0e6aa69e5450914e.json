{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=style&index=0&id=b236e548&lang=less&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749980456032}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.withdrawal-management {\n  padding: 24px;\n  background: #f0f2f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  margin-bottom: 24px;\n\n  h2 {\n    margin: 0 0 8px 0;\n    font-size: 24px;\n    font-weight: 600;\n    color: #262626;\n  }\n\n  p {\n    margin: 0;\n    color: #8c8c8c;\n    font-size: 14px;\n  }\n}\n\n.search-section {\n  margin-bottom: 24px;\n}\n\n// 表格样式\n.ant-table {\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    text-align: center !important;\n  }\n}\n\n// 表格内容样式\n.user-info {\n  .username {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .user-id {\n    font-size: 12px;\n    color: #8c8c8c;\n  }\n}\n\n.amount-info {\n  .amount {\n    font-weight: 600;\n    color: #262626;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n.alipay-info {\n  .name {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .account {\n    font-size: 12px;\n    color: #8c8c8c;\n    font-family: 'Courier New', monospace;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n// 弹窗样式\n.reject-modal {\n  .modal-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.detail-modal {\n  .amount-text {\n    font-weight: 600;\n    color: #52c41a;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .withdrawal-management {\n    padding: 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n\n    .ant-btn {\n      width: 100%;\n    }\n  }\n}\n", {"version": 3, "sources": ["AigcWithdrawalList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwiBA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AigcWithdrawalList.vue", "sourceRoot": "src/views/aigcview/vue", "sourcesContent": ["<template>\n  <div class=\"withdrawal-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>提现管理</h2>\n      <p>管理用户提现申请，审核通过或拒绝申请</p>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <a-card :bordered=\"false\">\n        <a-form layout=\"inline\" :model=\"searchForm\" @submit=\"handleSearch\">\n          <a-form-item label=\"申请状态\">\n            <a-select v-model=\"searchForm.status\" placeholder=\"请选择状态\" style=\"width: 120px\" allowClear>\n              <a-select-option :value=\"1\">待审核</a-select-option>\n              <a-select-option :value=\"2\">已发放</a-select-option>\n              <a-select-option :value=\"3\">审核拒绝</a-select-option>\n              <a-select-option :value=\"4\">已取消</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item label=\"申请时间\">\n            <a-range-picker\n              v-model=\"searchForm.dateRange\"\n              format=\"YYYY-MM-DD\"\n              :placeholder=\"['开始时间', '结束时间']\"\n            />\n          </a-form-item>\n          <a-form-item label=\"用户名\">\n            <a-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" style=\"width: 150px\" />\n          </a-form-item>\n\n          <a-form-item label=\"支付宝信息\">\n            <a-input v-model=\"searchForm.alipayInfo\" placeholder=\"支付宝账号或姓名\" style=\"width: 150px\" />\n          </a-form-item>\n\n          <a-form-item>\n            <a-button type=\"primary\" @click=\"handleSearch\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              搜索\n            </a-button>\n            <a-button @click=\"handleReset\" style=\"margin-left: 8px\">\n              <a-icon type=\"reload\" />\n              重置\n            </a-button>\n          </a-form-item>\n        </a-form>\n      </a-card>\n    </div>\n\n    <!-- 数据表格 -->\n    <div class=\"table-section\">\n      <a-card :bordered=\"false\">\n        <!-- 表格 -->\n        <a-table\n          :columns=\"columns\"\n          :data-source=\"dataSource\"\n          :loading=\"loading\"\n          :pagination=\"pagination\"\n          row-key=\"id\"\n          @change=\"handleTableChange\"\n          :scroll=\"{ x: 1200 }\"\n        >\n          <!-- 用户信息列 -->\n          <template slot=\"userInfo\" slot-scope=\"text, record\">\n            <div class=\"user-info\" v-if=\"record\">\n              <div class=\"username\">{{ record.username || '-' }}</div>\n              <div class=\"user-id\">ID: {{ record.user_id || '-' }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 提现金额列 -->\n          <template slot=\"amount\" slot-scope=\"text, record\">\n            <div class=\"amount-info\" v-if=\"record\">\n              <div class=\"amount\">¥{{ formatNumber(record.withdrawal_amount) }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 支付宝信息列 -->\n          <template slot=\"alipayInfo\" slot-scope=\"text, record\">\n            <div class=\"alipay-info\" v-if=\"record\">\n              <div class=\"name\">{{ record.alipay_name || '-' }}</div>\n              <div class=\"account\">{{ record.alipay_account || '-' }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 状态列 -->\n          <template slot=\"status\" slot-scope=\"text, record\">\n            <a-tag :color=\"getStatusColor(record && record.status)\" v-if=\"record\">\n              {{ getStatusText(record.status, record.review_remark) }}\n            </a-tag>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 申请时间列 -->\n          <template slot=\"applyTime\" slot-scope=\"text, record\">\n            <span>{{ record && record.apply_time ? formatDateTime(record.apply_time) : '-' }}</span>\n          </template>\n\n          <!-- 审核时间列 -->\n          <template slot=\"reviewTime\" slot-scope=\"text, record\">\n            <span>{{ record && record.review_time ? formatDateTime(record.review_time) : '-' }}</span>\n          </template>\n\n          <!-- 操作列 -->\n          <template slot=\"action\" slot-scope=\"text, record\">\n            <div class=\"action-buttons\" v-if=\"record\">\n              <a-button\n                v-if=\"record.status === 1\"\n                type=\"primary\"\n                size=\"small\"\n                @click=\"handleApprove(record)\"\n                :loading=\"record.approving\"\n              >\n                审核通过\n              </a-button>\n\n              <a-button\n                v-if=\"record.status === 1\"\n                type=\"danger\"\n                size=\"small\"\n                @click=\"handleReject(record)\"\n                :loading=\"record.rejecting\"\n                style=\"margin-left: 8px\"\n              >\n                审核拒绝\n              </a-button>\n\n              <a-button\n                size=\"small\"\n                @click=\"handleViewDetail(record)\"\n                style=\"margin-left: 8px\"\n              >\n                查看详情\n              </a-button>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </a-table>\n      </a-card>\n    </div>\n\n    <!-- 审核拒绝原因弹窗 -->\n    <a-modal\n      v-model=\"showRejectModal\"\n      title=\"审核拒绝\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"reject-modal\">\n        <a-alert \n          message=\"请填写拒绝原因\" \n          type=\"warning\" \n          show-icon \n          style=\"margin-bottom: 20px\"\n        />\n        \n        <a-form layout=\"vertical\">\n          <a-form-item label=\"拒绝原因\" required>\n            <a-textarea \n              v-model=\"rejectReason\" \n              placeholder=\"请输入拒绝原因\"\n              :rows=\"4\"\n              :maxLength=\"200\"\n            />\n          </a-form-item>\n        </a-form>\n        \n        <div class=\"modal-actions\">\n          <a-button @click=\"showRejectModal = false\">\n            取消\n          </a-button>\n          <a-button \n            type=\"danger\" \n            @click=\"confirmReject\"\n            :loading=\"rejecting\"\n            :disabled=\"!rejectReason.trim()\"\n            style=\"margin-left: 10px\"\n          >\n            确认拒绝\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 详情弹窗 -->\n    <a-modal\n      v-model=\"showDetailModal\"\n      title=\"提现申请详情\"\n      :footer=\"null\"\n      width=\"600px\"\n    >\n      <div class=\"detail-modal\" v-if=\"currentRecord\">\n        <a-descriptions :column=\"2\" bordered>\n          <a-descriptions-item label=\"申请ID\">\n            {{ currentRecord.id }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"用户名\">\n            {{ currentRecord.username }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"提现金额\">\n            <span class=\"amount-text\">¥{{ formatNumber(currentRecord.withdrawal_amount) }}</span>\n          </a-descriptions-item>\n          <a-descriptions-item label=\"申请状态\">\n            <a-tag :color=\"getStatusColor(currentRecord.status)\">\n              {{ getStatusText(currentRecord.status, currentRecord.review_remark) }}\n            </a-tag>\n          </a-descriptions-item>\n          <a-descriptions-item label=\"真实姓名\">\n            {{ currentRecord.alipay_name }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"支付宝账号\">\n            {{ currentRecord.alipay_account }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"申请时间\">\n            {{ currentRecord.apply_time ? formatDateTime(currentRecord.apply_time) : '-' }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核时间\">\n            {{ currentRecord.review_time ? formatDateTime(currentRecord.review_time) : '-' }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核人\" v-if=\"currentRecord.review_by\">\n            {{ currentRecord.review_by }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核备注\" v-if=\"currentRecord.review_remark\">\n            {{ currentRecord.review_remark }}\n          </a-descriptions-item>\n        </a-descriptions>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AigcWithdrawalList',\n  data() {\n    return {\n      loading: false,\n      // 搜索表单\n      searchForm: {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      },\n      // 表格数据\n      dataSource: [],\n      // 分页\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total) => `共 ${total} 条记录`\n      },\n      // 表格列定义\n      columns: [\n        {\n          title: '用户信息',\n          key: 'userInfo',\n          width: 150,\n          scopedSlots: { customRender: 'userInfo' }\n        },\n        {\n          title: '提现金额',\n          key: 'amount',\n          width: 120,\n          align: 'right',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '支付宝信息',\n          key: 'alipayInfo',\n          width: 180,\n          scopedSlots: { customRender: 'alipayInfo' }\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'apply_time',\n          key: 'applyTime',\n          width: 150,\n          scopedSlots: { customRender: 'applyTime' }\n        },\n        {\n          title: '审核时间',\n          dataIndex: 'review_time',\n          key: 'reviewTime',\n          width: 150,\n          scopedSlots: { customRender: 'reviewTime' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          fixed: 'right',\n          scopedSlots: { customRender: 'action' }\n        }\n      ],\n      // 弹窗状态\n      showRejectModal: false,\n      showDetailModal: false,\n      currentRecord: null,\n      rejectReason: '',\n      rejecting: false\n    }\n  },\n  mounted() {\n    this.loadData()\n  },\n  methods: {\n    // 加载数据\n    async loadData() {\n      try {\n        this.loading = true\n\n        const params = {\n          current: this.pagination.current,\n          size: this.pagination.pageSize,\n          ...this.getSearchParams()\n        }\n\n        const response = await this.$http.get('/api/usercenter/admin/withdrawalList', { params })\n        console.log('提现列表完整响应:', response)\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n        console.log('提现列表数据:', data)\n\n        if (data && data.success) {\n          this.dataSource = data.result.records || []\n          this.pagination.total = data.result.total || 0\n          console.log('数据加载成功:', this.dataSource.length, '条记录')\n          console.log('完整result结构:', data.result)\n          console.log('records数组:', data.result.records)\n          console.log('第一条数据结构:', this.dataSource[0])\n          console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {}))\n\n          // 打印每个字段的值\n          if (this.dataSource[0]) {\n            const firstRecord = this.dataSource[0]\n            console.log('字段值详情:')\n            Object.keys(firstRecord).forEach(key => {\n              console.log(`  ${key}:`, firstRecord[key])\n            })\n          }\n        } else {\n          const errorMsg = (data && data.message) || '获取数据失败'\n          this.$message.error(errorMsg)\n          this.dataSource = []\n          this.pagination.total = 0\n        }\n      } catch (error) {\n        console.error('加载提现数据失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取搜索参数\n    getSearchParams() {\n      const params = {}\n\n      if (this.searchForm.status !== undefined) {\n        params.status = this.searchForm.status\n      }\n\n      if (this.searchForm.username) {\n        params.username = this.searchForm.username.trim()\n      }\n\n      if (this.searchForm.alipayInfo) {\n        params.alipayInfo = this.searchForm.alipayInfo.trim()\n      }\n\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD')\n        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD')\n      }\n\n      return params\n    },\n\n    // 搜索\n    handleSearch() {\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      }\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 表格变化\n    handleTableChange(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n      this.loadData()\n    },\n\n    // 审核通过\n    async handleApprove(record) {\n      this.$confirm({\n        title: '确认审核通过',\n        content: `确定要审核通过用户 ${record.username} 的提现申请吗？\\n提现金额：¥${this.formatNumber(record.withdrawal_amount)}`,\n        onOk: async () => {\n          try {\n            this.$set(record, 'approving', true)\n\n            const response = await this.$http.post('/api/usercenter/admin/approveWithdrawal', {\n              id: record.id\n            })\n\n            // 根据实际返回的数据结构处理\n            const data = response.data || response\n\n            if (data.success) {\n              this.$message.success('审核通过成功')\n              this.loadData()\n            } else {\n              this.$message.error(data.message || '审核失败')\n            }\n          } catch (error) {\n            console.error('审核通过失败:', error)\n            this.$message.error('审核失败，请重试')\n          } finally {\n            this.$set(record, 'approving', false)\n          }\n        }\n      })\n    },\n\n    // 审核拒绝\n    handleReject(record) {\n      this.currentRecord = record\n      this.rejectReason = ''\n      this.showRejectModal = true\n    },\n\n    // 确认拒绝\n    async confirmReject() {\n      if (!this.rejectReason.trim()) {\n        this.$message.warning('请填写拒绝原因')\n        return\n      }\n\n      try {\n        this.rejecting = true\n\n        const response = await this.$http.post('/api/usercenter/admin/rejectWithdrawal', {\n          id: this.currentRecord.id,\n          reason: this.rejectReason.trim()\n        })\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n\n        if (data.success) {\n          this.$message.success('审核拒绝成功')\n          this.showRejectModal = false\n          this.loadData()\n        } else {\n          this.$message.error(data.message || '审核失败')\n        }\n      } catch (error) {\n        console.error('审核拒绝失败:', error)\n        this.$message.error('审核失败，请重试')\n      } finally {\n        this.rejecting = false\n      }\n    },\n\n    // 查看详情\n    handleViewDetail(record) {\n      this.currentRecord = record\n      this.showDetailModal = true\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        1: 'orange',      // 待审核 - 橙色\n        2: 'green',       // 已发放 - 绿色\n        3: 'red',         // 审核拒绝 - 红色\n        4: 'gray' // 已取消 - 灰色\n      }\n      return colorMap[status] || 'volcano' // 未知状态用火山红色\n    },\n\n    // 获取状态文本\n    getStatusText(status, reviewRemark) {\n      const textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝',\n        4: '已取消'\n      }\n      let statusText = textMap[status] || '未知状态'\n\n      // 如果是审核拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n\n    // 格式化数字\n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n\n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.withdrawal-management {\n  padding: 24px;\n  background: #f0f2f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  margin-bottom: 24px;\n\n  h2 {\n    margin: 0 0 8px 0;\n    font-size: 24px;\n    font-weight: 600;\n    color: #262626;\n  }\n\n  p {\n    margin: 0;\n    color: #8c8c8c;\n    font-size: 14px;\n  }\n}\n\n.search-section {\n  margin-bottom: 24px;\n}\n\n// 表格样式\n.ant-table {\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    text-align: center !important;\n  }\n}\n\n// 表格内容样式\n.user-info {\n  .username {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .user-id {\n    font-size: 12px;\n    color: #8c8c8c;\n  }\n}\n\n.amount-info {\n  .amount {\n    font-weight: 600;\n    color: #262626;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n.alipay-info {\n  .name {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .account {\n    font-size: 12px;\n    color: #8c8c8c;\n    font-family: 'Courier New', monospace;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n// 弹窗样式\n.reject-modal {\n  .modal-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.detail-modal {\n  .amount-text {\n    font-weight: 600;\n    color: #52c41a;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .withdrawal-management {\n    padding: 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n\n    .ant-btn {\n      width: 100%;\n    }\n  }\n}\n</style>\n"]}]}