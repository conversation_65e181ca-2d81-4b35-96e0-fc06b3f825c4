package org.jeecg.modules.system.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.system.service.AlipayService;
import org.jeecg.modules.system.config.AlipayConfig;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.reward.service.ReferralRewardTriggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付宝支付控制器
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Api(tags = "支付宝支付")
@RestController
@RequestMapping("/api/alipay")
@Slf4j
public class AlipayController {

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ReferralRewardTriggerService rewardTriggerService;

    @Autowired
    private AlipayConfig alipayConfig;

    /**
     * 创建支付订单
     */
    @AutoLog(value = "创建支付宝支付订单")
    @ApiOperation(value = "创建支付宝支付订单", notes = "创建支付宝支付订单")
    @PostMapping("/createOrder")
    public Result<?> createPayOrder(@RequestBody Map<String, Object> requestData) {
        try {
            String orderId = (String) requestData.get("orderId");
            BigDecimal amount = new BigDecimal(requestData.get("amount").toString());
            String subject = (String) requestData.getOrDefault("subject", "智界Aigc充值");
            String body = (String) requestData.getOrDefault("body", "智界Aigc账户充值");

            log.info("💰 创建支付宝支付订单请求 - 订单号: {}, 金额: {}", orderId, amount);

            String payForm = alipayService.createPayOrder(orderId, amount, subject, body);

            log.info("🔍 支付表单内容长度: {}", payForm != null ? payForm.length() : 0);
            log.info("🔍 支付表单前100字符: {}", payForm != null && payForm.length() > 100 ? payForm.substring(0, 100) : payForm);

            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("amount", amount);
            result.put("payForm", payForm);

            log.info("🔍 返回结果: orderId={}, amount={}, payForm长度={}", orderId, amount, payForm != null ? payForm.length() : 0);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 创建支付宝支付订单失败", e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建支付宝扫码支付订单
     */
    @AutoLog(value = "创建支付宝扫码支付订单")
    @ApiOperation(value = "创建支付宝扫码支付订单", notes = "创建支付宝扫码支付订单")
    @PostMapping("/createQrOrder")
    public Result<?> createQrPayOrder(@RequestBody Map<String, Object> params) {
        try {
            String orderId = (String) params.get("orderId");
            BigDecimal amount = new BigDecimal(params.get("amount").toString());
            String subject = (String) params.get("subject");
            String body = (String) params.get("body");

            log.info("💰 创建支付宝扫码支付订单请求 - 订单号: {}, 金额: {}", orderId, amount);

            String qrCode = alipayService.createQrPayOrder(orderId, amount, subject, body);

            log.info("🔍 扫码支付二维码URL: {}", qrCode);

            Map<String, Object> result = new HashMap<>();
            result.put("orderId", orderId);
            result.put("amount", amount);
            result.put("qrCode", qrCode);

            log.info("🔍 返回扫码支付结果: orderId={}, amount={}, qrCode={}", orderId, amount, qrCode != null ? "已生成" : "为空");
            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 创建支付宝扫码支付订单失败", e);
            return Result.error("创建扫码支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     */
    @AutoLog(value = "查询支付宝订单状态")
    @ApiOperation(value = "查询支付宝订单状态", notes = "查询支付宝订单状态")
    @GetMapping("/queryOrder")
    public Result<?> queryPayOrder(@RequestParam String orderId) {
        try {
            log.info("💰 查询支付宝订单状态请求 - 订单号: {}", orderId);

            Map<String, Object> orderInfo = alipayService.queryPayOrder(orderId);
            return Result.OK(orderInfo);

        } catch (Exception e) {
            log.error("💰 查询支付宝订单状态失败 - 订单号: {}", orderId, e);
            return Result.error("查询订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 支付宝异步通知处理
     */
    @AutoLog(value = "支付宝异步通知")
    @ApiOperation(value = "支付宝异步通知", notes = "处理支付宝异步通知")
    @PostMapping("/notify")
    public String handleNotify(HttpServletRequest request) {
        try {
            log.info("💰 收到支付宝异步通知");

            // 验证签名
            boolean signVerified = alipayService.verifyNotify(request);
            if (!signVerified) {
                log.error("💰 支付宝异步通知签名验证失败");
                return "failure";
            }
            log.info("💰 支付宝签名验证通过");

            // 获取通知参数
            String tradeStatus = request.getParameter("trade_status");
            String outTradeNo = request.getParameter("out_trade_no");
            String tradeNo = request.getParameter("trade_no");
            String totalAmount = request.getParameter("total_amount");
            String buyerEmail = request.getParameter("buyer_email");

            log.info("💰 支付宝异步通知参数 - 订单号: {}, 交易号: {}, 状态: {}, 金额: {}, 买家: {}",
                outTradeNo, tradeNo, tradeStatus, totalAmount, buyerEmail);

            // 验证订单是否存在且状态正确
            if (!isValidOrder(outTradeNo)) {
                log.error("💰 无效订单或订单已处理 - 订单号: {}", outTradeNo);
                return "failure";
            }

            // 处理支付成功通知
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                log.info("💰 支付成功 - 订单号: {}, 交易号: {}", outTradeNo, tradeNo);

                // 处理支付成功业务逻辑
                boolean processResult = processPaymentSuccess(outTradeNo, tradeNo, totalAmount);
                if (processResult) {
                    log.info("💰 支付成功处理完成 - 订单号: {}", outTradeNo);
                } else {
                    log.error("💰 支付成功处理失败 - 订单号: {}", outTradeNo);
                }

                // 返回success给支付宝，表示处理成功
                return "success";
            }

            log.warn("💰 支付状态异常 - 订单号: {}, 状态: {}", outTradeNo, tradeStatus);
            return "success"; // 即使状态异常也返回success，避免支付宝重复通知

        } catch (Exception e) {
            log.error("💰 处理支付宝异步通知异常", e);
            return "failure";
        }
    }

    /**
     * 支付宝同步返回处理
     */
    @AutoLog(value = "支付宝同步返回")
    @ApiOperation(value = "支付宝同步返回", notes = "处理支付宝同步返回")
    @GetMapping("/return")
    public void handleReturn(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("💰 收到支付宝同步返回");

            // 验证签名
            boolean signVerified = alipayService.verifyNotify(request);
            if (!signVerified) {
                log.error("💰 支付宝同步返回签名验证失败");
                response.sendRedirect("/payment/failure");
                return;
            }

            String outTradeNo = request.getParameter("out_trade_no");
            String tradeNo = request.getParameter("trade_no");
            String totalAmount = request.getParameter("total_amount");

            log.info("💰 支付宝同步返回参数 - 订单号: {}, 交易号: {}, 金额: {}",
                outTradeNo, tradeNo, totalAmount);

            // 动态获取当前请求的域名，确保重定向到正确的地址
            String scheme = request.getScheme(); // http 或 https
            String serverName = request.getServerName(); // 域名或IP
            int serverPort = request.getServerPort(); // 端口号

            String frontendUrl = scheme + "://" + serverName;
            // 只有非标准端口才需要添加端口号
            if ((scheme.equals("http") && serverPort != 80) ||
                (scheme.equals("https") && serverPort != 443)) {
                frontendUrl += ":" + serverPort;
            }

            log.info("💰 动态获取前端地址: {} (scheme: {}, serverName: {}, port: {})",
                frontendUrl, scheme, serverName, serverPort);

            String redirectUrl = frontendUrl + "/usercenter?page=credits&paymentSuccess=true&orderId=" + outTradeNo;
            log.info("💰 重定向到前端页面: {}", redirectUrl);
            response.sendRedirect(redirectUrl);

        } catch (IOException e) {
            log.error("💰 处理支付宝同步返回异常", e);
        }
    }

    /**
     * 支付宝授权回调处理
     */
    @AutoLog(value = "支付宝授权回调")
    @ApiOperation(value = "支付宝授权回调", notes = "处理支付宝授权回调")
    @GetMapping("/auth/callback")
    public Result<?> handleAuthCallback(@RequestParam(required = false) String code,
                                       @RequestParam(required = false) String state) {
        try {
            log.info("💰 收到支付宝授权回调 - code: {}, state: {}", code, state);

            if (code == null || code.isEmpty()) {
                return Result.error("授权失败：未获取到授权码");
            }

            // TODO: 这里可以实现支付宝登录功能
            // 使用授权码换取用户信息等

            Map<String, Object> result = new HashMap<>();
            result.put("code", code);
            result.put("state", state);
            result.put("message", "授权回调处理成功");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("💰 处理支付宝授权回调异常", e);
            return Result.error("授权回调处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证订单是否有效
     */
    private boolean isValidOrder(String outTradeNo) {
        try {
            String sql = "SELECT order_status, create_time FROM aicg_user_transaction WHERE related_order_id = ?";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, outTradeNo);

            if (result.isEmpty()) {
                log.warn("💰 订单不存在 - 订单号: {}", outTradeNo);
                return false;
            }

            Map<String, Object> order = result.get(0);
            Integer orderStatus = (Integer) order.get("order_status");

            // 只有待支付状态(1)的订单才能处理
            if (orderStatus != 1) {
                log.warn("💰 订单状态异常 - 订单号: {}, 状态: {}", outTradeNo, orderStatus);
                return false;
            }

            // 检查订单是否超时（15分钟）
            java.sql.Timestamp createTime = (java.sql.Timestamp) order.get("create_time");
            long diffMinutes = (System.currentTimeMillis() - createTime.getTime()) / (1000 * 60);
            if (diffMinutes > 15) {
                log.warn("💰 订单已超时 - 订单号: {}, 创建时间: {}, 超时: {}分钟", outTradeNo, createTime, diffMinutes);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("💰 验证订单异常 - 订单号: {}", outTradeNo, e);
            return false;
        }
    }

    /**
     * 处理支付成功业务逻辑
     */
    private boolean processPaymentSuccess(String outTradeNo, String tradeNo, String totalAmount) {
        try {
            log.info("💰 开始处理支付成功业务逻辑 - 订单号: {}, 交易号: {}, 金额: {}",
                outTradeNo, tradeNo, totalAmount);

            // 查询订单信息
            String querySql = "SELECT * FROM aicg_user_transaction WHERE related_order_id = ? AND order_status = 1";
            List<Map<String, Object>> orderResult = jdbcTemplate.queryForList(querySql, outTradeNo);

            if (orderResult.isEmpty()) {
                log.warn("💰 未找到待支付订单 - 订单号: {}", outTradeNo);
                return false;
            }

            Map<String, Object> order = orderResult.get(0);
            String userId = (String) order.get("user_id");
            BigDecimal orderAmount = (BigDecimal) order.get("amount");
            BigDecimal payAmount = new BigDecimal(totalAmount);

            // 验证金额是否一致
            if (orderAmount.compareTo(payAmount) != 0) {
                log.error("💰 订单金额不一致 - 订单号: {}, 订单金额: {}, 支付金额: {}",
                    outTradeNo, orderAmount, payAmount);
                return false;
            }

            // 获取用户信息
            String userSql = "SELECT * FROM aicg_user_profile WHERE user_id = ?";
            List<Map<String, Object>> userResult = jdbcTemplate.queryForList(userSql, userId);

            if (userResult.isEmpty()) {
                log.error("💰 用户不存在 - 用户ID: {}", userId);
                return false;
            }

            Map<String, Object> user = userResult.get(0);
            BigDecimal currentBalance = (BigDecimal) user.get("account_balance");
            BigDecimal newBalance = currentBalance.add(orderAmount);

            // 更新订单状态
            String updateOrderSql = "UPDATE aicg_user_transaction SET " +
                "order_status = 3, " +  // 已完成
                "balance_before = ?, " +
                "balance_after = ?, " +
                "transaction_time = ?, " +
                "update_time = ?, " +
                "description = ? " +
                "WHERE related_order_id = ? AND user_id = ?";

            Date now = new Date();
            jdbcTemplate.update(updateOrderSql,
                currentBalance,
                newBalance,
                now,
                now,
                "支付宝支付成功 - 交易号: " + tradeNo,
                outTradeNo,
                userId
            );

            // 更新用户余额
            String updateUserSql = "UPDATE aicg_user_profile SET " +
                "account_balance = ?, " +
                "total_recharge = total_recharge + ?, " +
                "update_time = ? " +
                "WHERE user_id = ?";

            jdbcTemplate.update(updateUserSql,
                newBalance,
                orderAmount,
                now,
                userId
            );

            // 检查是否为会员订阅订单，如果是则触发邀请奖励
            String orderType = (String) order.get("order_type");
            if ("membership".equals(orderType)) {
                try {
                    // 解析产品信息获取会员等级
                    String productInfoStr = (String) order.get("product_info");
                    Integer membershipLevel = 2; // 默认VIP等级

                    if (productInfoStr != null && !productInfoStr.isEmpty()) {
                        // 这里可以解析JSON获取具体的会员等级
                        // 暂时使用默认值
                    }

                    // 触发邀请奖励
                    boolean rewardSuccess = rewardTriggerService.triggerMembershipSubscriptionReward(
                        userId, orderAmount, membershipLevel, outTradeNo
                    );

                    if (rewardSuccess) {
                        log.info("🎁 邀请奖励触发成功 - 订单号: {}, 用户: {}, 金额: {}",
                                outTradeNo, userId, orderAmount);
                    }
                } catch (Exception e) {
                    log.error("🎁 邀请奖励触发失败 - 订单号: {}, 用户: {}", outTradeNo, userId, e);
                    // 奖励失败不影响主业务流程
                }
            }

            log.info("💰 支付成功处理完成 - 订单号: {}, 用户余额: {} -> {}",
                outTradeNo, currentBalance, newBalance);

            return true;

        } catch (Exception e) {
            log.error("💰 处理支付成功业务逻辑异常 - 订单号: {}", outTradeNo, e);
            return false;
        }
    }
}
