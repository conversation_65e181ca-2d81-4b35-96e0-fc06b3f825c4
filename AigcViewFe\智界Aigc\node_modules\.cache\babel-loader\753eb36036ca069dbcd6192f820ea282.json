{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue", "mtime": 1753687927441}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getFileAccessHttpUrl } from '@/api/manage';\nimport { getUnreadNotificationCount } from '@/api/notifications';\nexport default {\n  name: 'UserCenterSidebar',\n  props: {\n    currentPage: {\n      type: String,\n      default: 'overview'\n    },\n    userInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      isOnline: true,\n      defaultAvatar: '/default-avatar.png',\n      // 本地降级头像\n      unreadNotificationCount: 0,\n      sidebarTop: 140,\n      // 默认顶部距离（增加20px）\n      footerSafeDistance: 150 // 页脚安全距离（增加50px）\n\n    };\n  },\n  computed: {\n    menuItems: function menuItems() {\n      return [{\n        key: 'overview',\n        title: '概览',\n        icon: 'anticon anticon-dashboard',\n        description: '查看账户概况和统计数据'\n      }, {\n        key: 'profile',\n        title: '账户设置',\n        icon: 'anticon anticon-setting',\n        description: '管理个人信息和安全设置'\n      }, {\n        key: 'credits',\n        title: '账户管理',\n        icon: 'anticon anticon-wallet',\n        description: '查看余额和交易记录'\n      }, {\n        key: 'orders',\n        title: '订单记录',\n        icon: 'anticon anticon-shopping',\n        description: '查看购买历史和订单状态'\n      }, {\n        key: 'usage',\n        title: '使用记录',\n        icon: 'anticon anticon-bar-chart',\n        description: '查看API调用和插件使用'\n      }, {\n        key: 'notifications',\n        title: '系统通知',\n        icon: 'anticon anticon-bell',\n        description: '查看系统消息和通知',\n        badge: this.unreadNotificationCount > 0 ? this.unreadNotificationCount > 99 ? '99+' : this.unreadNotificationCount.toString() : null\n      } // 🚫 临时注释掉会员服务和推荐奖励功能\n      // {\n      //   key: 'membership',\n      //   title: '会员服务',\n      //   icon: 'anticon anticon-crown',\n      //   description: '管理会员订阅和特权',\n      //   badge: (() => {\n      //     const role = this.userInfo && this.userInfo.currentRole\n      //     if (!role) return null\n      //     // VIP用户显示对应的徽章\n      //     if (role === 'VIP' || role === 'vip' || role === 'VIP会员') return 'VIP'\n      //     if (role === 'SVIP' || role === 'svip' || role === 'SVIP会员') return 'SVIP'\n      //     if (role === 'admin' || role === 'ADMIN' || role === '管理员') return 'ADMIN'\n      //     return null // 普通用户不显示徽章\n      //   })()\n      // },\n      // {\n      //   key: 'referral',\n      //   title: '推荐奖励',\n      //   icon: 'anticon anticon-team',\n      //   description: '推荐好友获得奖励'\n      // }\n      ];\n    },\n    memberLevelClass: function memberLevelClass() {\n      var role = this.userInfo && this.userInfo.currentRole || 'user';\n      return {\n        'level-user': role === 'user' || role === 'USER' || role === '普通用户',\n        'level-vip': role === 'vip' || role === 'VIP' || role === 'VIP会员',\n        'level-svip': role === 'svip' || role === 'SVIP' || role === 'SVIP会员',\n        'level-admin': role === 'admin' || role === 'ADMIN' || role === '管理员'\n      };\n    },\n    memberLevelText: function memberLevelText() {\n      var role = this.userInfo && this.userInfo.currentRole || 'user';\n      var roleMap = {\n        // 小写映射\n        'user': '普通用户',\n        'vip': 'VIP会员',\n        'svip': 'SVIP会员',\n        'admin': '管理员',\n        // 大写映射（数据库实际存储）\n        'USER': '普通用户',\n        'VIP': 'VIP会员',\n        'SVIP': 'SVIP会员',\n        'ADMIN': '管理员',\n        // 中文显示名映射\n        '普通用户': '普通用户',\n        'VIP会员': 'VIP会员',\n        'SVIP会员': 'SVIP会员',\n        '管理员': '管理员'\n      };\n      return roleMap[role] || '普通用户';\n    },\n    avatarUrl: function avatarUrl() {\n      var avatar = this.userInfo && this.userInfo.avatar;\n\n      if (!avatar) {\n        return this.defaultAvatar;\n      } // 如果是完整的URL，直接返回\n\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是相对路径，使用getFileAccessHttpUrl转换\n\n\n      return getFileAccessHttpUrl(avatar) || this.defaultAvatar;\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              console.log('🔔 Sidebar: 组件已挂载，开始加载通知数量');\n              _context.next = 3;\n              return this.loadUnreadNotificationCount();\n\n            case 3:\n              this.loadDefaultAvatar(); // 监听通知更新事件\n\n              this.$bus.$on('notification-updated', this.handleNotificationUpdated);\n              console.log('🔔 Sidebar: 已设置通知更新事件监听'); // 添加滚动监听，处理页脚安全距离\n\n              this.initScrollListener();\n\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  beforeDestroy: function beforeDestroy() {\n    // 移除事件监听\n    this.$bus.$off('notification-updated', this.handleNotificationUpdated); // 移除滚动监听\n\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    // 加载TOS默认头像URL\n    loadDefaultAvatar: function () {\n      var _loadDefaultAvatar = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return this.$http.get('/sys/common/default-avatar-url');\n\n              case 3:\n                response = _context2.sent;\n\n                if (response && response.success && response.result) {\n                  this.defaultAvatar = response.result;\n                  console.log('🎯 Sidebar: 已加载TOS默认头像:', this.defaultAvatar);\n                }\n\n                _context2.next = 10;\n                break;\n\n              case 7:\n                _context2.prev = 7;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.warn('⚠️ Sidebar: 获取TOS默认头像失败，使用本地降级:', _context2.t0); // 保持本地默认头像作为降级方案\n\n              case 10:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 7]]);\n      }));\n\n      function loadDefaultAvatar() {\n        return _loadDefaultAvatar.apply(this, arguments);\n      }\n\n      return loadDefaultAvatar;\n    }(),\n    handleMenuClick: function handleMenuClick(item) {\n      this.$emit('menu-change', item.key);\n    },\n    handleRecharge: function handleRecharge() {\n      this.$emit('action', 'recharge');\n    },\n    handleUpgrade: function handleUpgrade() {\n      this.$emit('action', 'upgrade');\n    },\n    formatBalance: function formatBalance(balance) {\n      console.log('🔍 Sidebar: formatBalance 接收到的余额:', balance);\n      console.log('🔍 Sidebar: userInfo:', this.userInfo);\n      console.log('🔍 Sidebar: userInfo.accountBalance:', this.userInfo && this.userInfo.accountBalance);\n      if (!balance) return '0.00';\n      return parseFloat(balance).toFixed(2);\n    },\n    // 获取未读通知数量\n    loadUnreadNotificationCount: function () {\n      var _loadUnreadNotificationCount = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                console.log('🔔 Sidebar: 开始获取未读通知数量...');\n                _context3.next = 4;\n                return getUnreadNotificationCount();\n\n              case 4:\n                response = _context3.sent;\n\n                if (response.success && response.result) {\n                  this.unreadNotificationCount = response.result.unreadCount || 0;\n                  console.log('🔔 Sidebar: 未读通知数量:', this.unreadNotificationCount);\n                } else {\n                  console.error('🔔 Sidebar: 获取未读通知数量失败:', response.message);\n                }\n\n                _context3.next = 11;\n                break;\n\n              case 8:\n                _context3.prev = 8;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('🔔 Sidebar: 获取未读通知数量异常:', _context3.t0);\n\n              case 11:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 8]]);\n      }));\n\n      function loadUnreadNotificationCount() {\n        return _loadUnreadNotificationCount.apply(this, arguments);\n      }\n\n      return loadUnreadNotificationCount;\n    }(),\n    // 处理通知更新事件\n    handleNotificationUpdated: function handleNotificationUpdated(eventData) {\n      console.log('🔔 Sidebar: 收到通知更新事件', eventData); // 重新获取未读通知数量\n\n      this.loadUnreadNotificationCount();\n    },\n    // 初始化滚动监听\n    initScrollListener: function initScrollListener() {\n      this.handleScroll = this.throttle(this.calculateSidebarPosition, 16); // 60fps\n\n      window.addEventListener('scroll', this.handleScroll, {\n        passive: true\n      }); // 初始计算一次\n\n      this.calculateSidebarPosition();\n    },\n    // 计算侧边栏位置，避免压到页脚\n    calculateSidebarPosition: function calculateSidebarPosition() {\n      // 在小屏幕上不执行sticky逻辑\n      if (window.innerWidth <= 1200) {\n        return;\n      }\n\n      var footer = document.querySelector('.website-footer');\n      if (!footer) return;\n      var sidebar = this.$el;\n      if (!sidebar) return;\n      var footerRect = footer.getBoundingClientRect();\n      var sidebarRect = sidebar.getBoundingClientRect();\n      var windowHeight = window.innerHeight; // 计算页脚距离顶部的距离\n\n      var footerTop = footerRect.top; // 计算侧边栏需要的空间（高度 + 安全距离）\n\n      var sidebarNeededSpace = sidebarRect.height + this.footerSafeDistance; // 如果页脚即将进入视窗，调整侧边栏位置\n\n      if (footerTop < windowHeight && footerTop < sidebarNeededSpace + 140) {\n        // 计算新的top值，确保不压到页脚\n        var newTop = Math.max(20, footerTop - sidebarRect.height - this.footerSafeDistance);\n        this.sidebarTop = newTop;\n        console.log('🔧 Sidebar: 调整位置避免压到页脚，新top值:', newTop);\n      } else {\n        // 恢复默认位置\n        if (this.sidebarTop !== 140) {\n          this.sidebarTop = 140;\n          console.log('🔧 Sidebar: 恢复默认位置，top值:', 140);\n        }\n      }\n    },\n    // 节流函数，优化滚动性能\n    throttle: function throttle(func, delay) {\n      var timeoutId;\n      var lastExecTime = 0;\n      return function () {\n        var _this = this;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        var currentTime = Date.now();\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args);\n          lastExecTime = currentTime;\n        } else {\n          clearTimeout(timeoutId);\n          timeoutId = setTimeout(function () {\n            func.apply(_this, args);\n            lastExecTime = Date.now();\n          }, delay - (currentTime - lastExecTime));\n        }\n      };\n    }\n  }\n};", null]}