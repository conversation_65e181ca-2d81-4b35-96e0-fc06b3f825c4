{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue?vue&type=template&id=350f49e2&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue", "mtime": 1753702910988}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"referral-page\">\n  <div class=\"page-header\">\n    <h1 class=\"page-title\">推荐奖励</h1>\n    <p class=\"page-description\">邀请好友注册，获得丰厚奖励</p>\n  </div>\n\n  <div class=\"referral-content\">\n    <!-- 推荐统计 -->\n    <div class=\"referral-stats\">\n      <StatsCard\n        :value=\"referralStats.totalReferrals\"\n        label=\"推荐人数\"\n        icon=\"anticon anticon-team\"\n        icon-color=\"#7c8aed\"\n        :loading=\"loading\"\n      />\n      \n      <StatsCard\n        :value=\"referralStats.totalRewards\"\n        unit=\"元\"\n        label=\"累计奖励\"\n        icon=\"anticon anticon-gift\"\n        icon-color=\"#10b981\"\n        :loading=\"loading\"\n      />\n      \n      <StatsCard\n        :value=\"referralStats.availableRewards\"\n        unit=\"元\"\n        label=\"可提现金额\"\n        icon=\"anticon anticon-wallet\"\n        icon-color=\"#f59e0b\"\n        :loading=\"loading\"\n      />\n      \n      <StatsCard\n        :value=\"referralStats.monthlyReferrals\"\n        label=\"本月推荐\"\n        icon=\"anticon anticon-calendar\"\n        icon-color=\"#ef4444\"\n        :trend=\"monthlyTrend\"\n        :loading=\"loading\"\n      />\n    </div>\n\n    <!-- 推荐链接生成 -->\n    <div class=\"referral-link-section\">\n      <h3 class=\"section-title\">我的推荐链接</h3>\n      <div class=\"link-generator\">\n        <div class=\"link-display\">\n          <div class=\"link-input\">\n            <a-input\n              :value=\"referralLink\"\n              readonly\n              size=\"large\"\n              placeholder=\"点击生成推荐链接\"\n            />\n            <a-button \n              type=\"primary\" \n              size=\"large\"\n              :loading=\"linkLoading\"\n              @click=\"handleCopyLink\"\n            >\n              <i class=\"anticon anticon-copy\"></i>\n              复制链接\n            </a-button>\n          </div>\n          \n          <div class=\"link-actions\">\n            <a-button @click=\"handleGenerateLink\" :loading=\"linkLoading\">\n              <i class=\"anticon anticon-reload\"></i>\n              重新生成\n            </a-button>\n            <a-button @click=\"handleGenerateQRCode\">\n              <i class=\"anticon anticon-qrcode\"></i>\n              生成二维码\n            </a-button>\n            <a-button @click=\"handleShareToSocial\">\n              <i class=\"anticon anticon-share-alt\"></i>\n              分享到社交媒体\n            </a-button>\n          </div>\n        </div>\n        \n        <div class=\"referral-tips\">\n          <h4>推荐奖励规则</h4>\n          <ul>\n            <li>好友通过您的链接注册并订阅会员，您可获得其订阅金额的 <strong>{{ currentCommissionRate }}%</strong> 佣金</li>\n            <li>普通用户：{{ normalRate }}% 基础佣金，邀请10人升至{{ normalHighRate }}%，邀请30人升至{{ normalTopRate }}%</li>\n            <li>VIP会员：{{ vipRate }}% 基础佣金，邀请10人升至{{ vipHighRate }}%，邀请30人升至{{ vipTopRate }}%</li>\n            <li>SVIP会员：直接享受 {{ svipRate }}% 最高佣金</li>\n            <li>佣金将在好友完成订阅后24小时内到账</li>\n            <li>累计佣金满100元即可申请提现</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <!-- 推荐记录 -->\n    <div class=\"referral-records\">\n      <div class=\"records-header\">\n        <h3 class=\"section-title\">推荐记录</h3>\n        <div class=\"records-filters\">\n          <a-select \n            v-model=\"recordFilters.status\" \n            placeholder=\"状态筛选\"\n            style=\"width: 120px\"\n            @change=\"handleRecordFilterChange\"\n          >\n            <a-select-option value=\"\">全部状态</a-select-option>\n            <a-select-option value=\"pending\">待确认</a-select-option>\n            <a-select-option value=\"confirmed\">已确认</a-select-option>\n            <a-select-option value=\"rewarded\">已奖励</a-select-option>\n          </a-select>\n          \n          <a-range-picker \n            v-model=\"recordFilters.dateRange\"\n            style=\"width: 240px\"\n            @change=\"handleRecordFilterChange\"\n          />\n        </div>\n      </div>\n      \n      <a-table\n        :columns=\"recordColumns\"\n        :data-source=\"referralRecords\"\n        :loading=\"recordLoading\"\n        :pagination=\"recordPagination\"\n        row-key=\"id\"\n        @change=\"handleRecordTableChange\"\n      >\n        <!-- 好友信息列 -->\n        <template #friendInfo=\"{ record }\">\n          <div class=\"friend-info\">\n            <div class=\"friend-avatar\">\n              <img :src=\"record.friendAvatar || defaultAvatar\" :alt=\"record.friendNickname\" />\n            </div>\n            <div class=\"friend-details\">\n              <div class=\"friend-name\">{{ record.friendNickname || '新用户' }}</div>\n              <div class=\"friend-email\">{{ maskEmail(record.friendEmail) }}</div>\n            </div>\n          </div>\n        </template>\n        \n        <!-- 奖励金额列 -->\n        <template #rewardAmount=\"{ text }\">\n          <span class=\"reward-amount\">¥{{ formatNumber(text) }}</span>\n        </template>\n        \n        <!-- 状态列 -->\n        <template #status=\"{ text }\">\n          <span class=\"record-status\" :class=\"getRecordStatusClass(text)\">\n            {{ getRecordStatusText(text) }}\n          </span>\n        </template>\n        \n        <!-- 时间列 -->\n        <template #time=\"{ text }\">\n          <span class=\"record-time\">{{ formatDateTime(text) }}</span>\n        </template>\n      </a-table>\n    </div>\n\n    <!-- 奖励提现 -->\n    <div class=\"withdrawal-section\">\n      <h3 class=\"section-title\">奖励提现</h3>\n      <div class=\"withdrawal-card\">\n        <div class=\"withdrawal-info\">\n          <div class=\"balance-display\">\n            <div class=\"balance-item\">\n              <div class=\"balance-label\">可提现余额</div>\n              <div class=\"balance-amount\">¥{{ formatNumber(withdrawalInfo.availableAmount || 0) }}</div>\n            </div>\n            <div class=\"balance-item\">\n              <div class=\"balance-label\">冻结金额</div>\n              <div class=\"balance-amount frozen\">¥{{ formatNumber(withdrawalInfo.frozenAmount || 0) }}</div>\n            </div>\n          </div>\n\n          <!-- 状态提示 -->\n          <div class=\"withdrawal-status-tip\" v-if=\"withdrawalInfo.hasPendingRequest\">\n            <a-alert\n              message=\"您有提现申请正在审核中\"\n              :description=\"`申请金额：¥${formatNumber(withdrawalInfo.pendingAmount)}，申请时间：${formatDateTime(withdrawalInfo.pendingTime)}`\"\n              type=\"info\"\n              show-icon\n            />\n          </div>\n\n          <div class=\"withdrawal-rules\">\n            <h4>提现规则</h4>\n            <ul>\n              <li>最低提现金额：50元</li>\n              <li>支付宝账号：仅支持手机号</li>\n              <li>无手续费</li>\n              <li>到账时间：1-3个工作日</li>\n              <li>同一时间只能有一个待审核申请</li>\n            </ul>\n          </div>\n        </div>\n        \n        <div class=\"withdrawal-form\" v-if=\"withdrawalInfo.canWithdraw\">\n          <a-form layout=\"vertical\">\n            <a-form-item label=\"提现金额\">\n              <a-input-number\n                v-model=\"withdrawalAmount\"\n                :min=\"50\"\n                :max=\"withdrawalInfo.availableAmount\"\n                :step=\"10\"\n                placeholder=\"请输入提现金额\"\n                size=\"large\"\n                style=\"width: 100%\"\n              />\n              <div class=\"amount-tips\">\n                <span>最低50元</span>\n                <a @click=\"setMaxAmount\">全部提现</a>\n              </div>\n            </a-form-item>\n\n            <a-form-item label=\"真实姓名\">\n              <a-input\n                v-model=\"realName\"\n                placeholder=\"请输入真实姓名\"\n                size=\"large\"\n              />\n            </a-form-item>\n\n            <a-form-item label=\"支付宝账号\">\n              <a-input\n                v-model=\"alipayAccount\"\n                placeholder=\"请输入支付宝账号（手机号）\"\n                size=\"large\"\n              />\n            </a-form-item>\n\n            <div class=\"withdrawal-summary\">\n              <div class=\"summary-row\">\n                <span>提现金额：</span>\n                <span>¥{{ formatNumber(withdrawalAmount) }}</span>\n              </div>\n              <div class=\"summary-row total\">\n                <span>实际到账：</span>\n                <span>¥{{ formatNumber(withdrawalAmount) }}</span>\n              </div>\n            </div>\n\n            <a-button\n              type=\"primary\"\n              size=\"large\"\n              :disabled=\"!isFormValid\"\n              :loading=\"withdrawalLoading\"\n              @click=\"showConfirmModal\"\n              block\n            >\n              申请提现\n            </a-button>\n          </a-form>\n        </div>\n\n        <!-- 不能提现的提示 -->\n        <div v-else class=\"withdrawal-disabled\">\n          <a-alert\n            :message=\"withdrawalInfo.message\"\n            type=\"warning\"\n            show-icon\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 提现记录 -->\n    <div class=\"withdrawal-history\">\n      <h3 class=\"section-title\">提现记录</h3>\n      <a-table\n        :columns=\"withdrawalColumns\"\n        :data-source=\"withdrawalHistory\"\n        :loading=\"withdrawalHistoryLoading\"\n        :pagination=\"withdrawalPagination\"\n        row-key=\"id\"\n        @change=\"handleWithdrawalTableChange\"\n      >\n        <!-- 金额列 -->\n        <template slot=\"amount\" slot-scope=\"text, record\">\n          <span class=\"amount\">¥{{ formatNumber(record.withdrawal_amount) }}</span>\n        </template>\n\n        <!-- 状态列 -->\n        <template slot=\"status\" slot-scope=\"text, record\">\n          <a-tag :color=\"getWithdrawalStatusColor(record.status)\">\n            {{ record.statusText || getWithdrawalStatusText(record.status, record.review_remark) }}\n          </a-tag>\n        </template>\n\n        <!-- 时间列 -->\n        <template slot=\"time\" slot-scope=\"text\">\n          <span class=\"time-text\">{{ formatDateTime(text) }}</span>\n        </template>\n      </a-table>\n    </div>\n  </div>\n\n  <!-- 提现确认弹窗 -->\n  <a-modal\n    v-model=\"showConfirmWithdrawal\"\n    title=\"确认提现信息\"\n    :footer=\"null\"\n    width=\"500px\"\n  >\n    <div class=\"withdrawal-confirm\">\n      <a-alert\n        message=\"请仔细核对提现信息，提交后无法修改\"\n        type=\"warning\"\n        show-icon\n        style=\"margin-bottom: 20px\"\n      />\n\n      <div class=\"confirm-info\">\n        <div class=\"info-row\">\n          <span class=\"label\">提现金额：</span>\n          <span class=\"value\">¥{{ formatNumber(withdrawalAmount) }}</span>\n        </div>\n        <div class=\"info-row\">\n          <span class=\"label\">真实姓名：</span>\n          <span class=\"value\">{{ realName }}</span>\n        </div>\n        <div class=\"info-row\">\n          <span class=\"label\">支付宝账号：</span>\n          <span class=\"value\">{{ alipayAccount }}</span>\n        </div>\n        <div class=\"info-row\">\n          <span class=\"label\">实际到账：</span>\n          <span class=\"value highlight\">¥{{ formatNumber(withdrawalAmount) }}</span>\n        </div>\n      </div>\n\n      <div class=\"confirm-checkbox\">\n        <a-checkbox v-model=\"confirmChecked\">\n          我已仔细核对以上信息，确认无误\n        </a-checkbox>\n      </div>\n\n      <div class=\"confirm-actions\">\n        <a-button @click=\"showConfirmWithdrawal = false\" style=\"margin-right: 10px\">\n          取消\n        </a-button>\n        <a-button\n          type=\"primary\"\n          :disabled=\"!confirmChecked\"\n          :loading=\"withdrawalLoading\"\n          @click=\"handleConfirmWithdrawal\"\n        >\n          确认提交\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 二维码模态框 -->\n  <a-modal\n    v-model=\"showQRModal\"\n    title=\"推荐二维码\"\n    :footer=\"null\"\n    width=\"400px\"\n  >\n    <div class=\"qr-modal\">\n      <div class=\"qr-code\" ref=\"qrCodeContainer\">\n        <!-- 二维码将在这里生成 -->\n      </div>\n      <div class=\"qr-tips\">\n        <p>扫描二维码或分享链接给好友</p>\n        <p>好友注册并充值后您将获得奖励</p>\n      </div>\n      <div class=\"qr-actions\">\n        <a-button @click=\"handleDownloadQR\">下载二维码</a-button>\n        <a-button type=\"primary\" @click=\"handleCopyLink\">复制链接</a-button>\n      </div>\n    </div>\n  </a-modal>\n</div>\n", null]}