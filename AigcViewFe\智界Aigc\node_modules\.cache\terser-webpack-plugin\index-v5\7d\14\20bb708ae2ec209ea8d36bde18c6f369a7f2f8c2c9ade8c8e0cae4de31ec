
38d1428567ce6858f4bd377a1451ae80346eea60	{"key":"{\"terser\":\"4.8.0\",\"node_version\":\"v14.18.0\",\"terser-webpack-plugin\":\"1.4.4\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true,\"drop_console\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"1ce08bea2cd62ad32c27e48c4fa42e9c\"}","integrity":"sha512-LvvPoPbUaqp7qPP/wQ/gzj9dI2t2KXevk+JVvCawjcU6HBP58T1GOyauUxxWmsqmW8ly6ohHfFuF0mFzFy69ng==","time":1753786236741,"size":188444}