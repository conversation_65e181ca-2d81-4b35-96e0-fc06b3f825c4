{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue?vue&type=template&id=2a8a8cf4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue", "mtime": 1753672674026}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<nav class=\"home-navbar\" :class=\"{ 'scrolled': isScrolled }\" ref=\"navbar\">\n  <div class=\"nav-container\">\n    <div class=\"nav-brand\" ref=\"navBrand\">\n      <LogoImage\n        size=\"medium\"\n        :hover=\"true\"\n        container-class=\"brand-logo-container\"\n        image-class=\"brand-logo-image\"\n        fallback-class=\"brand-logo-fallback\"\n      />\n      <span class=\"brand-text\">智界AIGC</span>\n    </div>\n\n    <div class=\"nav-menu\" ref=\"navMenu\">\n      <a v-for=\"item in menuItems\" :key=\"item.name\" :href=\"item.href\" class=\"nav-link\">\n        <a-icon :type=\"item.icon\" class=\"nav-icon\" />\n        <span class=\"nav-text\">{{ item.name }}</span>\n      </a>\n    </div>\n\n    <div class=\"nav-actions\" ref=\"navActions\">\n      <button class=\"btn-secondary\">登录</button>\n    </div>\n\n    <button class=\"mobile-menu-btn\" @click=\"toggleMobileMenu\" ref=\"mobileMenuBtn\">\n      <a-icon :type=\"mobileMenuOpen ? 'close' : 'menu'\" />\n    </button>\n  </div>\n</nav>\n", null]}