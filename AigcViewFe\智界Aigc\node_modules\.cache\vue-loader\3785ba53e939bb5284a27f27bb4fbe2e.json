{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=a1183866&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\n      \"div\",\n      { staticClass: \"affiliate-container\" },\n      [\n        _c(\"div\", { staticClass: \"simple-header\" }, [\n          _c(\"h1\", { staticClass: \"simple-title\" }, [_vm._v(\"邀请奖励\")]),\n          _c(\"p\", { staticClass: \"simple-subtitle\" }, [\n            _vm._v(\"邀请好友注册智界AIGC，获得丰厚奖励\")\n          ]),\n          _c(\"div\", { staticClass: \"commission-badge\" }, [\n            _c(\"span\", { staticClass: \"badge-text\" }, [\n              _vm._v(\"当前奖励比例：\" + _vm._s(_vm.currentCommissionRate) + \"%\")\n            ]),\n            _c(\"span\", { staticClass: \"badge-level\" }, [\n              _vm._v(_vm._s(_vm.commissionLevelText))\n            ])\n          ])\n        ]),\n        _c(\"section\", { staticClass: \"affiliate-section\" }, [\n          _c(\"div\", { staticClass: \"container\" }, [\n            _c(\"div\", { staticClass: \"promotion-link-section\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [\n                _vm._v(\"您的专属邀请链接\")\n              ]),\n              _c(\"div\", { staticClass: \"link-main-container\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"link-input-large\" },\n                  [\n                    _c(\"a-input\", {\n                      attrs: {\n                        value: _vm.affiliateLink || \"正在生成邀请链接...\",\n                        readonly: \"\",\n                        loading: _vm.loading,\n                        size: \"large\",\n                        placeholder: \"邀请链接生成中...\"\n                      }\n                    })\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"link-actions\" },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"copy-btn\",\n                        attrs: {\n                          type: \"primary\",\n                          size: \"large\",\n                          disabled: !_vm.affiliateLink || _vm.loading\n                        },\n                        on: { click: _vm.copyLink }\n                      },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"copy\" } }),\n                        _vm._v(\"\\n                复制链接\\n              \")\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"qr-btn\",\n                        attrs: { size: \"large\", loading: _vm.qrLoading },\n                        on: { click: _vm.generateQRCode }\n                      },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"qrcode\" } }),\n                        _vm._v(\"\\n                邀请二维码\\n              \")\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                )\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"link-tips\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"info-circle\" } }),\n                  _vm._v(\"\\n            分享此链接，您将获得好友付费的 \"),\n                  _c(\"strong\", [\n                    _vm._v(_vm._s(_vm.currentCommissionRate) + \"%\")\n                  ]),\n                  _vm._v(\" 奖励\\n          \")\n                ],\n                1\n              )\n            ]),\n            _c(\"div\", { staticClass: \"earnings-dashboard\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"收益概览\")]),\n              _c(\"div\", { staticClass: \"earnings-grid\" }, [\n                _c(\"div\", { staticClass: \"earning-card primary\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"dollar\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(\n                              \"¥\" + _vm._s(_vm.formatNumber(_vm.totalEarnings))\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"累计收益\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card success\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"wallet\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(\n                              \"¥\" +\n                                _vm._s(_vm.formatNumber(_vm.availableEarnings))\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"可提现金额\")\n                          ])\n                        ]\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"card-action\" },\n                        [\n                          _c(\n                            \"a-button\",\n                            {\n                              attrs: {\n                                type: \"primary\",\n                                size: \"small\",\n                                disabled:\n                                  _vm.availableEarnings <= 0 || _vm.loading\n                              },\n                              on: { click: _vm.openWithdrawModal }\n                            },\n                            [\n                              _vm._v(\n                                \"\\n                    立即提现\\n                  \"\n                              )\n                            ]\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card info\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"team\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(_vm._s(Math.floor(_vm.totalReferrals)))\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"邀请注册人数\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"earning-card warning\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"crown\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-content\" },\n                    [\n                      _c(\n                        \"a-spin\",\n                        { attrs: { spinning: _vm.loading, size: \"small\" } },\n                        [\n                          _c(\"div\", { staticClass: \"earning-number\" }, [\n                            _vm._v(_vm._s(Math.floor(_vm.memberReferrals)))\n                          ]),\n                          _c(\"div\", { staticClass: \"earning-label\" }, [\n                            _vm._v(\"转化人数\")\n                          ])\n                        ]\n                      )\n                    ],\n                    1\n                  )\n                ])\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"commission-progress\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [\n                _vm._v(\"奖励等级进度\")\n              ]),\n              _c(\"div\", { staticClass: \"progress-card\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"level-timeline-horizontal\" },\n                  _vm._l(_vm.commissionLevels, function(level, index) {\n                    return _c(\n                      \"div\",\n                      {\n                        key: index,\n                        staticClass: \"level-step-horizontal\",\n                        class: {\n                          current: level.isCurrent,\n                          completed: level.isCompleted,\n                          upcoming: level.isUpcoming\n                        }\n                      },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"step-circle-horizontal\" },\n                          [\n                            level.isCompleted\n                              ? _c(\"a-icon\", { attrs: { type: \"check\" } })\n                              : level.isCurrent\n                              ? _c(\"span\", { staticClass: \"current-dot\" })\n                              : _c(\"span\", { staticClass: \"step-number\" }, [\n                                  _vm._v(_vm._s(index + 1))\n                                ])\n                          ],\n                          1\n                        ),\n                        _c(\"div\", { staticClass: \"step-content-horizontal\" }, [\n                          _c(\"div\", { staticClass: \"step-title\" }, [\n                            _vm._v(_vm._s(level.name))\n                          ]),\n                          _c(\"div\", { staticClass: \"step-rate\" }, [\n                            _vm._v(_vm._s(level.rate) + \"%\")\n                          ]),\n                          _c(\"div\", { staticClass: \"step-requirement\" }, [\n                            _vm._v(_vm._s(level.requirement) + \"人\")\n                          ]),\n                          level.remaining > 0\n                            ? _c(\"div\", { staticClass: \"step-remaining\" }, [\n                                _vm._v(\n                                  \"\\n                    还需\" +\n                                    _vm._s(level.remaining) +\n                                    \"个\\n                  \"\n                                )\n                              ])\n                            : level.isCompleted\n                            ? _c(\"div\", { staticClass: \"step-completed\" }, [\n                                _vm._v(\n                                  \"\\n                    已达成\\n                  \"\n                                )\n                              ])\n                            : _vm._e()\n                        ]),\n                        index < _vm.commissionLevels.length - 1\n                          ? _c(\"div\", { staticClass: \"step-line-horizontal\" })\n                          : _vm._e()\n                      ]\n                    )\n                  }),\n                  0\n                )\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"commission-rules\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"奖励规则\")]),\n              _c(\n                \"div\",\n                { staticClass: \"rules-table\" },\n                [\n                  _c(\"div\", { staticClass: \"rule-row header\" }, [\n                    _c(\"div\", { staticClass: \"rule-cell\" }, [\n                      _vm._v(\"用户等级\")\n                    ]),\n                    _c(\"div\", { staticClass: \"rule-cell\" }, [\n                      _vm._v(\"邀请人数要求\")\n                    ]),\n                    _c(\"div\", { staticClass: \"rule-cell\" }, [\n                      _vm._v(\"奖励比例\")\n                    ]),\n                    _c(\"div\", { staticClass: \"rule-cell\" }, [_vm._v(\"说明\")])\n                  ]),\n                  _c(\n                    \"a-spin\",\n                    { attrs: { spinning: _vm.loading, size: \"small\" } },\n                    _vm._l(_vm.allLevelConfigs, function(config) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: config.id,\n                          staticClass: \"rule-row\",\n                          class: {\n                            vip: config.role_code === \"VIP\",\n                            svip: config.role_code === \"SVIP\"\n                          }\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"rule-cell\" }, [\n                            _vm._v(\n                              _vm._s(_vm.getRoleDisplayName(config.role_code))\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"rule-cell\" }, [\n                            _vm._v(_vm._s(_vm.getRequirementText(config)))\n                          ]),\n                          _c(\"div\", { staticClass: \"rule-cell highlight\" }, [\n                            _vm._v(_vm._s(config.commission_rate) + \"%\")\n                          ]),\n                          _c(\"div\", { staticClass: \"rule-cell\" }, [\n                            _vm._v(_vm._s(config.level_name))\n                          ])\n                        ]\n                      )\n                    }),\n                    0\n                  )\n                ],\n                1\n              )\n            ]),\n            _c(\"div\", { staticClass: \"referral-users\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [\n                _vm._v(\"我的邀请用户\")\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"users-table-container\" },\n                [\n                  _c(\"a-table\", {\n                    attrs: {\n                      columns: _vm.userColumns,\n                      \"data-source\": _vm.referralUsers,\n                      loading: _vm.usersLoading,\n                      pagination: _vm.usersPagination,\n                      size: \"middle\"\n                    },\n                    on: { change: _vm.handleUsersTableChange },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"avatar\",\n                        fn: function(text, record) {\n                          return [\n                            _c(\n                              \"a-avatar\",\n                              {\n                                style: { backgroundColor: \"#87d068\" },\n                                attrs: { src: _vm.getAvatarUrl(record.avatar) }\n                              },\n                              [\n                                _vm._v(\n                                  \"\\n                  \" +\n                                    _vm._s(\n                                      record.nickname\n                                        ? record.nickname.charAt(0)\n                                        : \"U\"\n                                    ) +\n                                    \"\\n                \"\n                                )\n                              ]\n                            )\n                          ]\n                        }\n                      },\n                      {\n                        key: \"reward\",\n                        fn: function(text) {\n                          return [\n                            _c(\"span\", { staticClass: \"reward-amount\" }, [\n                              _vm._v(\"¥\" + _vm._s(text || \"0.00\"))\n                            ])\n                          ]\n                        }\n                      }\n                    ])\n                  })\n                ],\n                1\n              )\n            ]),\n            _c(\"div\", { staticClass: \"withdraw-records\" }, [\n              _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"提现记录\")]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"filter-section\",\n                  staticStyle: {\n                    \"margin-bottom\": \"16px\",\n                    padding: \"16px\",\n                    background: \"#fafafa\",\n                    \"border-radius\": \"6px\"\n                  }\n                },\n                [\n                  _c(\n                    \"a-row\",\n                    { attrs: { gutter: 16 } },\n                    [\n                      _c(\n                        \"a-col\",\n                        { attrs: { span: 5 } },\n                        [\n                          _c(\n                            \"a-form-item\",\n                            { attrs: { label: \"提现金额\" } },\n                            [\n                              _c(\n                                \"a-input-group\",\n                                { attrs: { compact: \"\" } },\n                                [\n                                  _c(\"a-input-number\", {\n                                    staticStyle: { width: \"50%\" },\n                                    attrs: {\n                                      placeholder: \"最小金额\",\n                                      min: 0,\n                                      precision: 2\n                                    },\n                                    model: {\n                                      value: _vm.withdrawFilter.minAmount,\n                                      callback: function($$v) {\n                                        _vm.$set(\n                                          _vm.withdrawFilter,\n                                          \"minAmount\",\n                                          $$v\n                                        )\n                                      },\n                                      expression: \"withdrawFilter.minAmount\"\n                                    }\n                                  }),\n                                  _c(\"a-input-number\", {\n                                    staticStyle: { width: \"50%\" },\n                                    attrs: {\n                                      placeholder: \"最大金额\",\n                                      min: 0,\n                                      precision: 2\n                                    },\n                                    model: {\n                                      value: _vm.withdrawFilter.maxAmount,\n                                      callback: function($$v) {\n                                        _vm.$set(\n                                          _vm.withdrawFilter,\n                                          \"maxAmount\",\n                                          $$v\n                                        )\n                                      },\n                                      expression: \"withdrawFilter.maxAmount\"\n                                    }\n                                  })\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-col\",\n                        { attrs: { span: 5 } },\n                        [\n                          _c(\n                            \"a-form-item\",\n                            { attrs: { label: \"申请时间\" } },\n                            [\n                              _c(\"a-range-picker\", {\n                                staticStyle: { width: \"100%\" },\n                                attrs: {\n                                  format: \"YYYY-MM-DD\",\n                                  placeholder: \"['开始日期', '结束日期']\"\n                                },\n                                model: {\n                                  value: _vm.withdrawFilter.dateRange,\n                                  callback: function($$v) {\n                                    _vm.$set(\n                                      _vm.withdrawFilter,\n                                      \"dateRange\",\n                                      $$v\n                                    )\n                                  },\n                                  expression: \"withdrawFilter.dateRange\"\n                                }\n                              })\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-col\",\n                        { attrs: { span: 4 } },\n                        [\n                          _c(\n                            \"a-form-item\",\n                            { attrs: { label: \"状态\" } },\n                            [\n                              _c(\n                                \"a-select\",\n                                {\n                                  staticStyle: { width: \"100%\" },\n                                  attrs: { placeholder: \"选择状态\" },\n                                  model: {\n                                    value: _vm.withdrawFilter.status,\n                                    callback: function($$v) {\n                                      _vm.$set(\n                                        _vm.withdrawFilter,\n                                        \"status\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"withdrawFilter.status\"\n                                  }\n                                },\n                                [\n                                  _c(\n                                    \"a-select-option\",\n                                    { attrs: { value: null } },\n                                    [_vm._v(\"全部\")]\n                                  ),\n                                  _c(\n                                    \"a-select-option\",\n                                    { attrs: { value: 1 } },\n                                    [_vm._v(\"待审核\")]\n                                  ),\n                                  _c(\n                                    \"a-select-option\",\n                                    { attrs: { value: 2 } },\n                                    [_vm._v(\"已发放\")]\n                                  ),\n                                  _c(\n                                    \"a-select-option\",\n                                    { attrs: { value: 3 } },\n                                    [_vm._v(\"审核拒绝\")]\n                                  ),\n                                  _c(\n                                    \"a-select-option\",\n                                    { attrs: { value: 4 } },\n                                    [_vm._v(\"已取消\")]\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-col\",\n                        { attrs: { span: 5 } },\n                        [\n                          _c(\n                            \"a-form-item\",\n                            { attrs: { label: \"完成时间\" } },\n                            [\n                              _c(\"a-range-picker\", {\n                                staticStyle: { width: \"100%\" },\n                                attrs: {\n                                  format: \"YYYY-MM-DD\",\n                                  placeholder: \"['开始日期', '结束日期']\"\n                                },\n                                model: {\n                                  value: _vm.withdrawFilter.completeDateRange,\n                                  callback: function($$v) {\n                                    _vm.$set(\n                                      _vm.withdrawFilter,\n                                      \"completeDateRange\",\n                                      $$v\n                                    )\n                                  },\n                                  expression: \"withdrawFilter.completeDateRange\"\n                                }\n                              })\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-col\",\n                        { attrs: { span: 5 } },\n                        [\n                          _c(\n                            \"a-form-item\",\n                            { attrs: { label: \" \" } },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticStyle: { \"margin-right\": \"8px\" },\n                                  attrs: {\n                                    type: \"primary\",\n                                    loading: _vm.recordsLoading\n                                  },\n                                  on: { click: _vm.handleWithdrawFilter }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                    搜索\\n                  \"\n                                  )\n                                ]\n                              ),\n                              _c(\n                                \"a-button\",\n                                { on: { click: _vm.handleWithdrawReset } },\n                                [_vm._v(\"重置\")]\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"records-table-container\" },\n                [\n                  _c(\"a-table\", {\n                    attrs: {\n                      columns: _vm.withdrawColumns,\n                      \"data-source\": _vm.withdrawRecords,\n                      loading: _vm.recordsLoading,\n                      pagination: _vm.withdrawPagination,\n                      size: \"middle\"\n                    },\n                    on: { change: _vm.handleWithdrawTableChange },\n                    scopedSlots: _vm._u([\n                      {\n                        key: \"status\",\n                        fn: function(text) {\n                          return [\n                            _c(\n                              \"a-tag\",\n                              { attrs: { color: _vm.getStatusColor(text) } },\n                              [\n                                _vm._v(\n                                  \"\\n                  \" +\n                                    _vm._s(text) +\n                                    \"\\n                \"\n                                )\n                              ]\n                            )\n                          ]\n                        }\n                      },\n                      {\n                        key: \"amount\",\n                        fn: function(text) {\n                          return [\n                            _c(\"span\", { staticClass: \"withdraw-amount\" }, [\n                              _vm._v(\"¥\" + _vm._s(text))\n                            ])\n                          ]\n                        }\n                      },\n                      {\n                        key: \"action\",\n                        fn: function(text, record) {\n                          return [\n                            record.rawStatus === 1\n                              ? _c(\n                                  \"a-button\",\n                                  {\n                                    attrs: {\n                                      type: \"danger\",\n                                      size: \"small\",\n                                      loading: _vm.cancelLoading\n                                    },\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.handleCancelWithdraw(record)\n                                      }\n                                    }\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                  取消提现\\n                \"\n                                    )\n                                  ]\n                                )\n                              : _c(\"span\", [_vm._v(\"-\")])\n                          ]\n                        }\n                      }\n                    ])\n                  })\n                ],\n                1\n              )\n            ])\n          ])\n        ]),\n        _c(\n          \"a-modal\",\n          {\n            attrs: {\n              title: \"邀请二维码\",\n              footer: null,\n              width: \"400px\",\n              centered: \"\"\n            },\n            model: {\n              value: _vm.showQRModal,\n              callback: function($$v) {\n                _vm.showQRModal = $$v\n              },\n              expression: \"showQRModal\"\n            }\n          },\n          [\n            _c(\"div\", { staticClass: \"qr-modal-content\" }, [\n              _vm.qrCodeUrl\n                ? _c(\"div\", { staticClass: \"qr-code-container\" }, [\n                    _c(\"img\", {\n                      staticClass: \"qr-code-image\",\n                      attrs: { src: _vm.qrCodeUrl, alt: \"邀请二维码\" }\n                    })\n                  ])\n                : _vm._e(),\n              _c(\n                \"div\",\n                { staticClass: \"qr-actions\" },\n                [\n                  _vm.qrCodeUrl\n                    ? _c(\n                        \"a-button\",\n                        {\n                          attrs: { type: \"primary\", block: \"\" },\n                          on: { click: _vm.downloadQRCode }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"download\" } }),\n                          _vm._v(\"\\n            下载二维码\\n          \")\n                        ],\n                        1\n                      )\n                    : _vm._e()\n                ],\n                1\n              )\n            ])\n          ]\n        ),\n        _c(\n          \"a-modal\",\n          {\n            attrs: {\n              title: \"申请提现\",\n              footer: null,\n              width: \"500px\",\n              centered: \"\"\n            },\n            model: {\n              value: _vm.showWithdrawModal,\n              callback: function($$v) {\n                _vm.showWithdrawModal = $$v\n              },\n              expression: \"showWithdrawModal\"\n            }\n          },\n          [\n            _c(\n              \"div\",\n              { staticClass: \"withdraw-modal-content\" },\n              [\n                _c(\"div\", { staticClass: \"withdraw-info\" }, [\n                  _c(\"div\", { staticClass: \"info-item\" }, [\n                    _c(\"span\", { staticClass: \"info-label\" }, [\n                      _vm._v(\"可提现金额：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"info-value\" }, [\n                      _vm._v(\n                        \"¥\" + _vm._s(_vm.formatNumber(_vm.availableEarnings))\n                      )\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"info-item\" }, [\n                    _c(\"span\", { staticClass: \"info-label\" }, [\n                      _vm._v(\"最低提现金额：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"info-value\" }, [\n                      _vm._v(\"¥50.00\")\n                    ])\n                  ])\n                ]),\n                _c(\n                  \"a-form\",\n                  {\n                    attrs: { form: _vm.withdrawForm },\n                    on: { submit: _vm.handleWithdraw }\n                  },\n                  [\n                    _c(\n                      \"a-form-item\",\n                      { attrs: { label: \"提现金额\" } },\n                      [\n                        _c(\n                          \"a-input-number\",\n                          {\n                            directives: [\n                              {\n                                name: \"decorator\",\n                                rawName: \"v-decorator\",\n                                value: [\n                                  \"amount\",\n                                  {\n                                    rules: [\n                                      {\n                                        required: true,\n                                        message: \"请输入提现金额\"\n                                      },\n                                      {\n                                        type: \"number\",\n                                        min: 50,\n                                        message: \"最低提现金额为50元\"\n                                      },\n                                      {\n                                        type: \"number\",\n                                        max: _vm.availableEarnings,\n                                        message: \"提现金额不能超过可提现金额\"\n                                      }\n                                    ]\n                                  }\n                                ],\n                                expression:\n                                  \"['amount', {\\n                rules: [\\n                  { required: true, message: '请输入提现金额' },\\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\\n                ]\\n              }]\"\n                              }\n                            ],\n                            staticStyle: { width: \"100%\" },\n                            attrs: {\n                              min: 50,\n                              max: _vm.availableEarnings,\n                              precision: 2,\n                              placeholder: \"请输入提现金额\"\n                            }\n                          },\n                          [\n                            _c(\"template\", { slot: \"addonAfter\" }, [\n                              _vm._v(\"元\")\n                            ])\n                          ],\n                          2\n                        )\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"a-form-item\",\n                      { attrs: { label: \"提现方式\" } },\n                      [\n                        _c(\n                          \"a-select\",\n                          {\n                            directives: [\n                              {\n                                name: \"decorator\",\n                                rawName: \"v-decorator\",\n                                value: [\n                                  \"method\",\n                                  {\n                                    rules: [\n                                      {\n                                        required: true,\n                                        message: \"请选择提现方式\"\n                                      }\n                                    ],\n                                    initialValue: \"alipay\"\n                                  }\n                                ],\n                                expression:\n                                  \"['method', {\\n                rules: [{ required: true, message: '请选择提现方式' }],\\n                initialValue: 'alipay'\\n              }]\"\n                              }\n                            ],\n                            attrs: {\n                              placeholder: \"请选择提现方式\",\n                              disabled: \"\"\n                            }\n                          },\n                          [\n                            _c(\n                              \"a-select-option\",\n                              { attrs: { value: \"alipay\" } },\n                              [_vm._v(\"支付宝\")]\n                            )\n                          ],\n                          1\n                        )\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"a-form-item\",\n                      { attrs: { label: \"支付宝手机号\" } },\n                      [\n                        _c(\"a-input\", {\n                          directives: [\n                            {\n                              name: \"decorator\",\n                              rawName: \"v-decorator\",\n                              value: [\n                                \"alipayAccount\",\n                                {\n                                  rules: [\n                                    {\n                                      required: true,\n                                      message: \"请输入支付宝手机号\"\n                                    },\n                                    {\n                                      pattern: /^1[3-9]\\d{9}$/,\n                                      message: \"请输入正确的手机号格式\"\n                                    }\n                                  ]\n                                }\n                              ],\n                              expression:\n                                \"['alipayAccount', {\\n                rules: [\\n                  { required: true, message: '请输入支付宝手机号' },\\n                  { pattern: /^1[3-9]\\\\d{9}$/, message: '请输入正确的手机号格式' }\\n                ]\\n              }]\"\n                            }\n                          ],\n                          attrs: { placeholder: \"请输入支付宝手机号\" }\n                        })\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"a-form-item\",\n                      { attrs: { label: \"收款人真实姓名\" } },\n                      [\n                        _c(\"a-input\", {\n                          directives: [\n                            {\n                              name: \"decorator\",\n                              rawName: \"v-decorator\",\n                              value: [\n                                \"realName\",\n                                {\n                                  rules: [\n                                    {\n                                      required: true,\n                                      message: \"请输入收款人真实姓名\"\n                                    },\n                                    {\n                                      pattern: /^[\\u4e00-\\u9fa5]{2,4}$/,\n                                      message:\n                                        \"请输入正确的中文姓名（2-4个汉字）\"\n                                    }\n                                  ]\n                                }\n                              ],\n                              expression:\n                                \"['realName', {\\n                rules: [\\n                  { required: true, message: '请输入收款人真实姓名' },\\n                  { pattern: /^[\\\\u4e00-\\\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\\n                ]\\n              }]\"\n                            }\n                          ],\n                          attrs: { placeholder: \"请输入收款人真实姓名\" }\n                        })\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"withdraw-actions\" },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        staticStyle: { \"margin-right\": \"8px\" },\n                        on: {\n                          click: function($event) {\n                            _vm.showWithdrawModal = false\n                          }\n                        }\n                      },\n                      [_vm._v(\"\\n            取消\\n          \")]\n                    ),\n                    _c(\n                      \"a-button\",\n                      {\n                        attrs: {\n                          type: \"primary\",\n                          loading: _vm.withdrawLoading\n                        },\n                        on: { click: _vm.handleWithdraw }\n                      },\n                      [_vm._v(\"\\n            申请提现\\n          \")]\n                    )\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ]\n        )\n      ],\n      1\n    )\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}