<template>
  <div class="credits-page">
    <div class="page-header">
      <h1 class="page-title">账户管理</h1>
      <p class="page-description">管理您的账户余额、查看交易记录和充值</p>
    </div>

    <div class="credits-content">
      <!-- 余额概览 -->
      <div class="balance-overview">
        <div class="balance-cards">
          <StatsCard
            :value="balanceData.currentBalance"
            unit="元"
            label="当前余额"
            icon="anticon anticon-wallet"
            icon-color="#10b981"
            :trend="balanceTrend"
            :loading="loading"
            @click="handleQuickRecharge"
          />
          
          <StatsCard
            :value="balanceData.totalRecharge"
            unit="元"
            label="累计充值"
            icon="anticon anticon-plus-circle"
            icon-color="#7c8aed"
            :loading="loading"
          />
          
          <StatsCard
            :value="balanceData.totalConsumption"
            unit="元"
            label="累计消费"
            icon="anticon anticon-minus-circle"
            icon-color="#ef4444"
            :loading="loading"
          />
          
          <StatsCard
            :value="balanceData.monthlyConsumption"
            unit="元"
            label="本月消费"
            icon="anticon anticon-bar-chart"
            icon-color="#f59e0b"
            :trend="monthlyTrend"
            :loading="loading"
          />
        </div>

        <!-- 快速充值 -->
        <div class="quick-recharge">
          <h3 class="section-title">快速充值</h3>
          <div class="recharge-options">
            <div 
              v-for="option in rechargeOptions" 
              :key="option.amount"
              class="recharge-option"
              :class="{ selected: selectedAmount === option.amount }"
              @click="selectRechargeAmount(option.amount)"
            >
              <div class="option-amount">¥{{ option.amount }}</div>
              <div class="option-label">{{ option.label }}</div>
            </div>
          </div>
          
          <div class="custom-amount">
            <a-input-number
              v-model="customAmount"
              :min="0.01"
              :max="10000"
              :step="0.01"
              placeholder="自定义金额（最低0.01元）"
              size="large"
              style="width: 200px"
            />
            <a-button 
              type="primary" 
              size="large"
              :loading="rechargeLoading"
              @click="handleRecharge"
            >
              立即充值
            </a-button>
          </div>
        </div>
      </div>



      <!-- 交易记录 -->
      <DataTable
        ref="transactionTable"
        title="交易记录"
        :data-source="transactionList"
        :columns="transactionColumns"
        :loading="transactionLoading"
        :pagination="pagination"
        :show-action-column="false"
        :type-options="transactionTypeOptions"
        :status-options="[]"
        :show-search="true"
        type-filter-placeholder="交易类型"
        status-filter-placeholder="交易状态"
        search-placeholder="搜索交易描述"
        :date-filter-placeholder="['交易时间', '交易时间']"
        @filter-change="handleFilterChange"
        @table-change="handleTableChange"
        @refresh="loadTransactionData"
      >
        <!-- 自定义操作按钮 -->
        <template #actions>
          <a-button
            @click="handleResetFilters"
            style="margin-right: 8px; background: linear-gradient(135deg, #64748b 0%, #475569 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3); color: white;"
          >
            <a-icon type="reload" style="margin-right: 6px;" />
            重置
          </a-button>
          <a-button
            type="primary"
            @click="handleExportTransactions"
            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);"
          >
            <a-icon type="download" style="margin-right: 6px;" />
            导出交易记录
          </a-button>
        </template>
      </DataTable>
    </div>

    <!-- 充值确认模态框 -->
    <a-modal
      v-model="showRechargeModal"
      title="确认充值"
      :footer="null"
      width="500px"
    >
      <div class="recharge-confirm">
        <div class="confirm-info">
          <div class="info-row">
            <span class="info-label">充值金额：</span>
            <span class="info-value">¥{{ finalRechargeAmount }}</span>
          </div>
          <div class="info-row total">
            <span class="info-label">到账金额：</span>
            <span class="info-value">¥{{ finalRechargeAmount }}</span>
          </div>
        </div>

        <div class="payment-methods">
          <h4>选择支付方式</h4>
          <a-radio-group v-model="selectedPaymentMethod">
            <a-radio value="alipay-page">
              <span class="payment-option">
                <i class="payment-icon alipay"></i>
                支付宝网页支付
                <small style="color: #999; margin-left: 8px;">(PC端推荐)</small>
              </span>
            </a-radio>
            <a-radio value="alipay-qr">
              <span class="payment-option">
                <i class="payment-icon alipay"></i>
                支付宝扫码支付
                <small style="color: #999; margin-left: 8px;">(移动端推荐)</small>
              </span>
            </a-radio>
            <a-radio value="wechat">
              <span class="payment-option">
                <i class="payment-icon wechat"></i>
                微信支付
              </span>
            </a-radio>
            <a-radio value="bank">
              <span class="payment-option">
                <i class="payment-icon bank"></i>
                银行卡
              </span>
            </a-radio>
          </a-radio-group>
        </div>

        <div class="modal-actions">
          <a-button @click="showRechargeModal = false">取消</a-button>
          <a-button 
            type="primary" 
            :loading="paymentLoading"
            @click="handleConfirmRecharge"
          >
            确认支付
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 交易详情模态框 -->
    <a-modal
      v-model="showTransactionDetail"
      title="交易详情"
      :footer="null"
      width="600px"
    >
      <div class="transaction-detail" v-if="selectedTransaction">
        <div class="detail-header">
          <div class="transaction-type" :class="getTransactionTypeClass(selectedTransaction.transactionType)">
            <i :class="getTransactionTypeIcon(selectedTransaction.transactionType)"></i>
            {{ getTransactionTypeText(selectedTransaction.transactionType) }}
          </div>
          <div class="transaction-amount" :class="getAmountClass(selectedTransaction.transactionType)">
            {{ formatAmount(selectedTransaction.amount, selectedTransaction.transactionType) }}
          </div>
        </div>

        <div class="detail-content">
          <div class="detail-row">
            <span class="detail-label">交易单号：</span>
            <span class="detail-value">{{ selectedTransaction.id }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">交易时间：</span>
            <span class="detail-value">{{ formatDateTime(selectedTransaction.transactionTime) }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">交易描述：</span>
            <span class="detail-value">{{ selectedTransaction.description }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">交易前余额：</span>
            <span class="detail-value">¥{{ formatNumber(selectedTransaction.balanceBefore) }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">交易后余额：</span>
            <span class="detail-value">¥{{ formatNumber(selectedTransaction.balanceAfter) }}</span>
          </div>
          <div class="detail-row" v-if="selectedTransaction.relatedOrderId">
            <span class="detail-label">关联订单：</span>
            <span class="detail-value">{{ selectedTransaction.relatedOrderId }}</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import StatsCard from '../components/StatsCard.vue'
import DataTable from '../components/DataTable.vue'
import {
  getTransactionList,
  getTransactionStats,
  createRechargeOrder,
  exportTransactions
  // getRechargeOptions, // 不再需要，使用前端固定配置
  // getConsumptionChart // 临时注释，等后端接口实现
} from '@/api/usercenter'
import { authMixin } from '@/mixins/authMixin'

export default {
  name: 'UserCenterCredits',
  mixins: [authMixin],
  components: {
    StatsCard,
    DataTable
  },
  data() {
    return {
      loading: true,
      transactionLoading: false,
      chartLoading: false,
      rechargeLoading: false,
      paymentLoading: false,
      
      // 余额数据
      balanceData: {
        currentBalance: 0,
        totalRecharge: 0,
        totalConsumption: 0,
        monthlyConsumption: 0
      },
      
      // 充值相关 - 固定配置
      rechargeOptions: [
        { amount: 50, label: '体验套餐' },
        { amount: 100, label: '基础套餐' },
        { amount: 300, label: '进阶套餐' },
        { amount: 500, label: '专业套餐' },
        { amount: 1000, label: '企业套餐' }
      ],
      selectedAmount: 0,
      customAmount: null,
      showRechargeModal: false,
      selectedPaymentMethod: 'alipay-qr', // 默认选择扫码支付
      

      
      // 交易记录
      transactionList: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      filters: {},

      // 表格列配置
      transactionColumns: [
        {
          title: '交易时间',
          dataIndex: 'transactionTime',
          key: 'transactionTime',
          width: 180,
          align: 'center',
          customRender: (text) => {
            if (!text) return '-'
            try {
              let date
              if (typeof text === 'number') {
                date = new Date(text)
              } else {
                date = new Date(text)
              }
              return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })
            } catch (error) {
              return '-'
            }
          }
        },
        {
          title: '交易类型',
          dataIndex: 'transactionType',
          key: 'transactionType',
          width: 120,
          align: 'center',
          customRender: (text) => {
            console.log('🔍 交易类型 customRender - 接收到的值:', text, '类型:', typeof text)
            const typeMap = {
              1: '消费',
              2: '充值',
              3: '退款',
              4: '奖励',
              5: '会员订阅',
              '1': '消费',
              '2': '充值',
              '3': '退款',
              '4': '奖励',
              '5': '会员订阅'
            }
            const result = typeMap[text] || text || '-'
            console.log('🔍 交易类型 customRender - 返回结果:', result)
            return result
          }
        },
        {
          title: '订单号',
          dataIndex: 'relatedOrderId',
          key: 'relatedOrderId',
          width: 200,
          align: 'center',
          customRender: (text) => {
            return text || '-'
          }
        },
        {
          title: '交易描述',
          dataIndex: 'description',
          key: 'description',
          align: 'center',
          ellipsis: true
        },
        {
          title: '交易金额',
          dataIndex: 'amount',
          key: 'amount',
          width: 120,
          align: 'center',
          customRender: (text, record) => {
            const amount = parseFloat(text || 0).toFixed(2)
            const isPositive = [2, 3, 4].includes(Number(record.transactionType)) // 充值、退款、奖励为正
            const prefix = isPositive ? '+' : '-'
            const colorStyle = isPositive ? 'color: #52c41a; font-weight: 600;' : 'color: #ff4d4f; font-weight: 600;'
            return <span style={colorStyle}>{prefix}¥{amount}</span>
          }
        },
        {
          title: '余额',
          dataIndex: 'balanceAfter',
          key: 'balanceAfter',
          width: 120,
          align: 'center',
          customRender: (text) => {
            const balance = parseFloat(text || 0).toFixed(2)
            const balanceStyle = 'color: #1890ff; font-weight: 600;'
            return <span style={balanceStyle}>¥{balance}</span>
          }
        },
        {
          title: '状态',
          dataIndex: 'orderStatus',
          key: 'orderStatus',
          width: 100,
          align: 'center',
          customRender: (text) => {
            // 后端查询条件已经限制为 order_status = 3（已完成）
            // 所以这里的数据都应该是已完成的交易

            // 如果有明确的状态字段，使用映射
            if (text !== undefined && text !== null) {
              const statusMap = {
                1: { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                2: { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                3: { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                4: { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                5: { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                '1': { text: '待支付', style: 'color: #faad14; background: #fff7e6; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                '2': { text: '已支付', style: 'color: #1890ff; background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                '3': { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                '4': { text: '已取消', style: 'color: #ff4d4f; background: #fff2f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' },
                '5': { text: '已退款', style: 'color: #722ed1; background: #f9f0ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }
              }
              const status = statusMap[text] || { text: '已完成', style: 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;' }
              return <span style={status.style}>{status.text}</span>
            }

            // 如果没有状态字段，默认为已完成
            // 因为后端查询已经过滤为已完成的交易
            const defaultStyle = 'color: #52c41a; background: #f6ffed; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;'
            return <span style={defaultStyle}>已完成</span>
          }
        }
      ],

      // 筛选选项
      transactionTypeOptions: [
        { value: 1, label: '消费' },
        { value: 2, label: '充值' },
        { value: 3, label: '退款' },
        { value: 4, label: '奖励' },
        { value: 5, label: '会员订阅' }
      ],
      // 🚫 取消状态筛选选项
      // transactionStatusOptions: [
      //   { value: 1, label: '待支付' },
      //   { value: 3, label: '已完成' },
      //   { value: 4, label: '已取消' },
      //   { value: 5, label: '已退款' }
      // ],

      // 交易详情
      showTransactionDetail: false,
      selectedTransaction: null
    }
  },
  computed: {
    balanceTrend() {
      // 根据实际数据计算趋势（暂时返回null，等待后端提供趋势数据）
      // TODO: 后端需要提供上月对比数据来计算真实趋势
      return null
    },

    monthlyTrend() {
      // 根据实际数据计算月度趋势（暂时返回null，等待后端提供趋势数据）
      // TODO: 后端需要提供月度对比数据来计算真实趋势
      return null
    },
    
    finalRechargeAmount() {
      return this.selectedAmount || this.customAmount || 0
    }
  },
  async mounted() {
    await this.loadData()

    // 检查是否是支付成功返回
    this.checkPaymentSuccess()
  },
  methods: {
    async loadData() {
      try {
        this.loading = true
        
        // 并行加载数据
        await Promise.all([
          this.loadBalanceData(),
          this.loadTransactionData()
        ])
      } catch (error) {
        console.error('加载账户管理数据失败:', error)
        this.$message.error('加载数据失败，请刷新重试')
      } finally {
        this.loading = false
      }
    },
    
    async loadBalanceData() {
      try {
        const response = await getTransactionStats()
        if (response.success) {
          // 修复：使用 result 字段并正确映射字段名
          const stats = response.result || {}

          // 🔧 修复字段映射问题
          this.balanceData = {
            currentBalance: stats.accountBalance || 0,        // 后端返回accountBalance，前端期望currentBalance
            totalRecharge: stats.totalRecharge || 0,          // 字段名一致
            totalConsumption: stats.totalConsumption || 0,    // 字段名一致
            monthlyConsumption: stats.monthlyConsumption || 0, // 如果后端有这个字段
            transactionCount: stats.transactionCount || 0     // 交易记录总数
          }

          console.log('🔍 Credits页面 - 余额数据映射结果:', this.balanceData)
          console.log('🔍 Credits页面 - 后端原始数据:', stats)
        }
      } catch (error) {
        console.error('加载余额数据失败:', error)
      }
    },
    
    async loadTransactionData() {
      try {
        this.transactionLoading = true

        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.filters
        }

        // 🔧 处理日期范围筛选
        if (this.filters.dateRange && this.filters.dateRange.length === 2) {
          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')
          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')
          console.log('🔍 日期范围筛选 - 原始dateRange:', this.filters.dateRange)
          console.log('🔍 日期范围筛选 - startDate:', params.startDate, 'endDate:', params.endDate)
          // 移除dateRange，避免传递给后端
          delete params.dateRange
        }

        console.log('🔍 Credits页面 - 最终查询参数:', params)

        const response = await getTransactionList(params)
        if (response.success) {
          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法
          const records = (response.result && response.result.records) || []

          // 调试：打印后端返回的数据结构
          console.log('🔍 Credits页面 - 后端返回的完整响应:', response)
          console.log('🔍 Credits页面 - 交易记录数组:', records)
          if (records.length > 0) {
            console.log('🔍 Credits页面 - 第一条记录详情:', records[0])
            console.log('🔍 Credits页面 - 第一条记录的所有字段:', Object.keys(records[0]))
            console.log('🔍 Credits页面 - transactionType字段值:', records[0].transactionType, '类型:', typeof records[0].transactionType)
            console.log('🔍 Credits页面 - status字段值:', records[0].status, '类型:', typeof records[0].status)

          // 检查所有可能的状态相关字段
          console.log('🔍 Credits页面 - 检查状态相关字段:')
          console.log('  - status:', records[0].status)
          console.log('  - transactionStatus:', records[0].transactionStatus)
          console.log('  - orderStatus:', records[0].orderStatus)
          console.log('  - paymentStatus:', records[0].paymentStatus)
          console.log('  - state:', records[0].state)
          console.log('  - delFlag:', records[0].delFlag)
          }

          this.transactionList = records
          this.pagination.total = (response.result && response.result.total) || 0
        }
      } catch (error) {
        console.error('加载交易记录失败:', error)
        this.transactionList = []
      } finally {
        this.transactionLoading = false
      }
    },


    

    
    // 充值相关方法
    selectRechargeAmount(amount) {
      this.selectedAmount = amount
      this.customAmount = null
    },
    
    handleQuickRecharge() {
      this.selectedAmount = 100
      this.handleRecharge()
    },
    
    handleRecharge() {
      if (!this.finalRechargeAmount || this.finalRechargeAmount < 0.01) {
        this.$message.warning('请选择或输入充值金额，最低0.01元')
        return
      }

      this.showRechargeModal = true
    },
    
    async handleConfirmRecharge() {
      try {
        this.paymentLoading = true

        const orderData = {
          amount: this.finalRechargeAmount,
          paymentMethod: this.selectedPaymentMethod
        }

        const response = await createRechargeOrder(orderData)
        if (response.success) {
          const result = response.result

          if (this.selectedPaymentMethod === 'alipay-page') {
            // 支付宝网页支付流程
            await this.handleAlipayPagePayment(result.orderId, result.amount)
          } else if (this.selectedPaymentMethod === 'alipay-qr') {
            // 支付宝扫码支付流程
            await this.handleAlipayQrPayment(result.orderId, result.amount)
          } else if (this.selectedPaymentMethod === 'wechat') {
            this.$message.info('微信支付功能开发中，敬请期待')
          } else if (this.selectedPaymentMethod === 'bank') {
            this.$message.info('银行卡支付功能开发中，敬请期待')
          }

          this.showRechargeModal = false
        } else {
          this.$message.error(response.message || '创建充值订单失败')
        }
      } catch (error) {
        console.error('创建充值订单失败:', error)
        this.$message.error('充值失败，请重试')
      } finally {
        this.paymentLoading = false
      }
    },

    // 处理支付宝网页支付
    async handleAlipayPagePayment(orderId, amount) {
      try {
        console.log('🔍 开始处理支付宝支付 - 订单号:', orderId, '金额:', amount)
        this.$message.loading('正在跳转到支付宝支付...', 0)

        // 调用支付宝支付接口
        const paymentData = {
          orderId: orderId,
          amount: amount,
          subject: '智界Aigc账户充值',
          body: `充值金额：¥${amount}`
        }

        console.log('🔍 发送支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createOrder', paymentData)
        console.log('🔍 支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const payForm = payResponse.result.payForm
          console.log('🔍 获取到支付表单:', payForm ? '有内容' : '为空')

          if (!payForm) {
            this.$message.error('支付表单为空')
            return
          }

          // 创建表单并提交到支付宝
          const div = document.createElement('div')
          div.innerHTML = payForm
          document.body.appendChild(div)

          const form = div.querySelector('form')
          if (form) {
            console.log('🔍 找到支付表单，准备提交')
            form.submit()
          } else {
            console.error('🔍 未找到支付表单')
            this.$message.error('支付表单创建失败')
          }

          // 清理DOM
          setTimeout(() => {
            if (document.body.contains(div)) {
              document.body.removeChild(div)
            }
          }, 1000)

        } else {
          console.error('🔍 支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建支付订单失败')
        }

      } catch (error) {
        this.$message.destroy()
        console.error('支付宝支付失败:', error)
        this.$message.error('支付宝支付失败，请重试')
      }
    },

    // 处理支付宝扫码支付
    async handleAlipayQrPayment(orderId, amount) {
      try {
        console.log('🔍 开始处理支付宝扫码支付 - 订单号:', orderId, '金额:', amount)
        this.$message.loading('正在生成支付二维码...', 0)

        // 调用支付宝扫码支付接口
        const paymentData = {
          orderId: orderId,
          amount: amount,
          subject: '智界Aigc账户充值',
          body: `充值金额：¥${amount}`
        }

        console.log('🔍 发送扫码支付请求数据:', paymentData)
        const payResponse = await this.$http.post('/api/alipay/createQrOrder', paymentData)
        console.log('🔍 扫码支付响应:', payResponse)
        this.$message.destroy()

        if (payResponse.success) {
          const qrCode = payResponse.result.qrCode
          console.log('🔍 获取到支付二维码:', qrCode ? '已生成' : '为空')

          if (!qrCode) {
            this.$message.error('支付二维码生成失败')
            return
          }

          // 显示二维码支付弹窗
          this.showQrCodeModal(qrCode, orderId, amount)

        } else {
          console.error('🔍 扫码支付请求失败:', payResponse.message)
          this.$message.error(payResponse.message || '创建扫码支付订单失败')
        }

      } catch (error) {
        this.$message.destroy()
        console.error('支付宝扫码支付失败:', error)
        this.$message.error('支付宝扫码支付失败，请重试')
      }
    },

    // 显示二维码支付弹窗
    showQrCodeModal(qrCode, orderId, amount) {
      this.$modal.info({
        title: '支付宝扫码支付',
        width: 400,
        content: h => h('div', {
          style: {
            textAlign: 'center',
            padding: '20px'
          }
        }, [
          h('div', {
            style: {
              marginBottom: '16px',
              fontSize: '16px',
              fontWeight: 'bold'
            }
          }, `充值金额：¥${amount}`),
          h('div', {
            style: {
              marginBottom: '16px',
              color: '#666'
            }
          }, '请使用支付宝扫描下方二维码完成支付'),
          h('div', {
            style: {
              display: 'flex',
              justifyContent: 'center',
              marginBottom: '16px'
            }
          }, [
            h('img', {
              src: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCode)}`,
              style: {
                width: '200px',
                height: '200px',
                border: '1px solid #d9d9d9'
              }
            })
          ]),
          h('div', {
            style: {
              color: '#999',
              fontSize: '12px'
            }
          }, '支付完成后页面将自动跳转')
        ]),
        onOk: () => {
          // 用户点击确定后，可以查询订单状态
          this.checkOrderStatus(orderId)
        }
      })

      // 定时查询订单状态
      this.startOrderStatusPolling(orderId)
    },

    // 开始轮询订单状态
    startOrderStatusPolling(orderId) {
      const pollInterval = setInterval(async () => {
        try {
          const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)
          if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {
            clearInterval(pollInterval)
            this.$modal.destroyAll()
            this.$message.success('支付成功！正在更新账户余额...')
            setTimeout(() => {
              this.loadData()
            }, 1000)
          }
        } catch (error) {
          console.error('查询订单状态失败:', error)
        }
      }, 3000) // 每3秒查询一次

      // 15分钟后停止轮询
      setTimeout(() => {
        clearInterval(pollInterval)
      }, 15 * 60 * 1000)
    },

    // 检查订单状态
    async checkOrderStatus(orderId) {
      try {
        const response = await this.$http.get(`/api/alipay/queryOrder?orderId=${orderId}`)
        if (response.success && response.result.tradeStatus === 'TRADE_SUCCESS') {
          this.$message.success('支付成功！正在更新账户余额...')
          setTimeout(() => {
            this.loadData()
          }, 1000)
        } else {
          this.$message.info('订单尚未支付，请继续扫码支付')
        }
      } catch (error) {
        console.error('查询订单状态失败:', error)
        this.$message.error('查询订单状态失败')
      }
    },

    // 检查支付成功状态
    checkPaymentSuccess() {
      const urlParams = new URLSearchParams(window.location.search)
      const paymentSuccess = urlParams.get('paymentSuccess')
      const orderId = urlParams.get('orderId')

      if (paymentSuccess === 'true' && orderId) {
        console.log('🎉 检测到支付成功返回 - 订单号:', orderId)

        // 显示支付成功消息
        this.$message.success('支付成功！正在更新账户余额...')

        // 刷新数据
        setTimeout(() => {
          this.loadData()
        }, 1000)

        // 清理URL参数
        const newUrl = window.location.pathname
        window.history.replaceState({}, document.title, newUrl)
      }
    },

    // 表格相关方法
    handleFilterChange(filters) {
      this.filters = filters
      this.pagination.current = 1
      this.loadTransactionData()
    },

    handleTableChange({ pagination }) {
      this.pagination = { ...this.pagination, ...pagination }
      this.loadTransactionData()
    },

    // 重置筛选条件
    handleResetFilters() {
      // 调用DataTable组件的重置方法
      if (this.$refs.transactionTable) {
        this.$refs.transactionTable.resetFilters()
      }
      // 重置本地筛选条件
      this.filters = {}
      this.pagination.current = 1
      // 重新加载数据
      this.loadTransactionData()
      this.$message.success('筛选条件已重置')
    },

    // 导出交易记录
    async handleExportTransactions() {
      try {
        this.$message.loading('正在导出交易记录...', 0)

        // 使用当前的筛选条件导出
        const params = {
          transactionType: this.filters.type,
          status: this.filters.status,
          keyword: this.filters.keyword
        }

        // 处理日期范围
        if (this.filters.dateRange && this.filters.dateRange.length === 2) {
          params.startDate = this.filters.dateRange[0].format('YYYY-MM-DD')
          params.endDate = this.filters.dateRange[1].format('YYYY-MM-DD')
        }

        console.log('🎯 导出交易记录参数:', params)

        const response = await exportTransactions(params)
        this.$message.destroy()

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0')

        link.download = `交易记录_${timestamp}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('交易记录导出成功！')

      } catch (error) {
        this.$message.destroy()
        console.error('🎯 导出交易记录失败:', error)
        this.$message.error('导出失败，请重试')
      }
    },
    
    handleViewTransactionDetail(transaction) {
      this.selectedTransaction = transaction
      this.showTransactionDetail = true
    },
    

    
    // 工具方法
    getTransactionTypeClass(type) {
      const classMap = {
        1: 'type-consume',
        2: 'type-recharge',
        3: 'type-refund',
        4: 'type-reward'
      }
      return classMap[type] || ''
    },
    
    getTransactionTypeIcon(type) {
      const iconMap = {
        1: 'anticon anticon-minus-circle',
        2: 'anticon anticon-plus-circle',
        3: 'anticon anticon-undo',
        4: 'anticon anticon-gift'
      }
      return iconMap[type] || 'anticon anticon-question-circle'
    },
    
    getTransactionTypeText(type) {
      const textMap = {
        1: '消费',
        2: '充值',
        3: '退款',
        4: '奖励'
      }
      return textMap[type] || '未知'
    },
    
    getAmountClass(type) {
      return {
        'amount-positive': [2, 3, 4].includes(type), // 充值、退款、奖励为正
        'amount-negative': type === 1 // 消费为负
      }
    },
    
    formatAmount(amount, type) {
      const prefix = [2, 3, 4].includes(type) ? '+' : '-'
      return `${prefix}¥${this.formatNumber(Math.abs(amount))}`
    },
    
    formatNumber(number) {
      if (!number) return '0.00'
      return parseFloat(number).toFixed(2)
    },
    
    formatDateTime(dateString) {
      if (!dateString) return '-'
      
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return '-'
      }
    }
  }
}
</script>

<style scoped>
.credits-page {
  padding: 2rem;
}

.page-header {
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #334155;
  margin: 0 0 0.5rem 0;
}

.page-description {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

.credits-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* 余额概览 */
.balance-overview {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
}

.balance-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

/* 快速充值 */
.quick-recharge {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 138, 237, 0.1);
  height: fit-content;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334155;
  margin: 0 0 2rem 0;
}

.recharge-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.recharge-option {
  padding: 1rem;
  border: 2px solid rgba(124, 138, 237, 0.1);
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(124, 138, 237, 0.02);
}

.recharge-option:hover {
  border-color: rgba(124, 138, 237, 0.3);
  background: rgba(124, 138, 237, 0.05);
}

.recharge-option.selected {
  border-color: #7c8aed;
  background: rgba(124, 138, 237, 0.1);
}

.option-amount {
  font-size: 1.2rem;
  font-weight: 700;
  color: #334155;
  margin-bottom: 0.25rem;
}



.option-label {
  font-size: 0.9rem;
  color: #64748b;
}

.custom-amount {
  display: flex;
  gap: 1rem;
  align-items: center;
}



/* 充值确认模态框 */
.recharge-confirm {
  padding: 1rem 0;
}

.confirm-info {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(124, 138, 237, 0.05);
  border-radius: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row.total {
  padding-top: 1rem;
  border-top: 1px solid rgba(124, 138, 237, 0.1);
  font-weight: 600;
}

.info-label {
  color: #64748b;
}

.info-value {
  color: #334155;
  font-weight: 500;
}

.info-value.bonus {
  color: #ef4444;
}

.payment-methods {
  margin-bottom: 2rem;
}

.payment-methods h4 {
  margin-bottom: 1rem;
  color: #334155;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.payment-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
}

.payment-icon.alipay {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzAwOUZFOCIvPgo8L3N2Zz4K');
}

.payment-icon.wechat {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzA5QjEzMiIvPgo8L3N2Zz4K');
}

.payment-icon.bank {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiByeD0iNCIgZmlsbD0iIzY0NzQ4QiIvPgo8L3N2Zz4K');
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* 交易详情模态框 */
.transaction-detail {
  padding: 1rem 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(124, 138, 237, 0.1);
}

.transaction-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 500;
}

.type-recharge {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.type-consume {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.type-refund {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.type-reward {
  background: rgba(124, 138, 237, 0.1);
  color: #7c8aed;
}

.transaction-amount {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.amount-positive {
  color: #10b981;
}

.amount-negative {
  color: #ef4444;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(124, 138, 237, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  color: #334155;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .balance-overview {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .balance-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .credits-page {
    padding: 1rem;
  }
  
  .balance-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .recharge-options {
    grid-template-columns: 1fr;
  }
  
  .custom-amount {
    flex-direction: column;
    align-items: stretch;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
</style>
