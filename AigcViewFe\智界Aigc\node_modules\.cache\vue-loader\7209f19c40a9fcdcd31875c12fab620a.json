{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue", "mtime": 1753692759803}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WebsiteNotFound from '@/views/website/exception/NotFound.vue'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { isAdmin, getUserRole } from '@/utils/roleUtils'\nimport { generateIndexRouter } from '@/utils/util'\nimport Vue from 'vue'\n\nexport default {\n  name: 'SmartNotFound',\n  components: {\n    WebsiteNotFound\n  },\n  data() {\n    return {\n      loading: true\n    }\n  },\n  async mounted() {\n    console.log('🔍 SmartNotFound: 开始智能路由检查')\n    console.log('  📍 当前访问路径:', this.$route.path)\n    console.log('  📍 完整路径:', this.$route.fullPath)\n    \n    await this.checkAndHandleRoute()\n  },\n  methods: {\n    async checkAndHandleRoute() {\n      try {\n        // 1. 检查是否有TOKEN\n        const hasToken = !!Vue.ls.get(ACCESS_TOKEN)\n        console.log('  🔑 是否有TOKEN:', hasToken)\n        \n        if (!hasToken) {\n          console.log('  ❌ 无TOKEN，显示404页面')\n          this.loading = false\n          return\n        }\n\n        // 2. 检查用户角色\n        const userRole = await getUserRole()\n        const isAdminUser = await isAdmin()\n        console.log('  👤 用户角色:', userRole)\n        console.log('  🔐 是否管理员:', isAdminUser)\n\n        if (!isAdminUser) {\n          console.log('  ❌ 非管理员用户，显示404页面')\n          this.loading = false\n          return\n        }\n\n        // 3. 检查访问路径是否可能是后台页面\n        const currentPath = this.$route.path\n        const isLikelyBackendPage = this.isLikelyBackendPage(currentPath)\n        console.log('  🏢 是否疑似后台页面:', isLikelyBackendPage)\n\n        if (!isLikelyBackendPage) {\n          console.log('  ❌ 不是后台页面，显示404页面')\n          this.loading = false\n          return\n        }\n\n        // 4. admin用户访问疑似后台页面，尝试加载动态路由\n        console.log('  🚀 admin访问后台页面，尝试加载动态路由')\n        await this.loadDynamicRoutesAndRedirect(currentPath)\n\n      } catch (error) {\n        console.error('  ❌ 智能路由检查失败:', error)\n        this.loading = false\n      }\n    },\n\n    isLikelyBackendPage(path) {\n      // 判断是否可能是后台页面的逻辑\n      const backendPatterns = [\n        // 现有的模块\n        '/views/',      // 所有views下的页面\n        '/dashboard/',  // 仪表板页面\n        '/system/',     // 系统管理页面\n        '/aigcview/',   // 智界Aigc模块页面\n        '/cjsc',        // 插件商城管理\n        '/spjc',        // 视频教程管理\n        '/sensitive-word', // 敏感词管理页面\n        '/usercenter/withdrawal', // 提现管理页面\n\n        // JeecgBoot核心模块\n        '/online/',     // 在线开发模块\n        '/jmreport/',   // 积木报表模块\n        '/bigscreen/',  // 大屏报表模块\n        '/desform/',    // 表单设计器\n        '/act/',        // 工作流模块\n        '/plug-in/',    // 插件管理\n        '/generic/',    // 通用功能\n        '/eoa/',        // OA办公模块\n        '/joa/',        // OA流程模块\n\n        // 简化路径和其他模块\n        '/isystem',     // 系统管理简化路径\n        '/modules/',    // 功能模块路径\n        '/examples/',   // 示例页面路径\n        '/jeecg/',      // JeecgBoot示例路径\n      ]\n\n      return backendPatterns.some(pattern => path.startsWith(pattern))\n    },\n\n    async loadDynamicRoutesAndRedirect(targetPath) {\n      try {\n        console.log('  📡 开始获取权限数据')\n        \n        // 检查动态路由是否已加载\n        if (this.$store.getters.permissionList.length > 0) {\n          console.log('  ✅ 动态路由已存在，直接跳转')\n          this.$router.replace(targetPath)\n          return\n        }\n\n        // 获取权限数据\n        const res = await this.$store.dispatch('GetPermissionList')\n        console.log('  📡 权限接口响应:', res)\n\n        const menuData = res.result.menu\n        if (!menuData || menuData.length === 0) {\n          console.log('  ❌ 无菜单权限，显示404页面')\n          this.loading = false\n          return\n        }\n\n        // 生成动态路由\n        console.log('  🏗️ 生成动态路由')\n        const constRoutes = generateIndexRouter(menuData)\n        \n        // 更新路由状态\n        await this.$store.dispatch('UpdateAppRouter', { constRoutes })\n        \n        // 添加动态路由\n        this.$router.addRoutes(this.$store.getters.addRouters)\n        \n        console.log('  ✅ 动态路由加载成功，跳转到目标页面:', targetPath)\n        \n        // 跳转到目标页面\n        this.$router.replace(targetPath)\n\n      } catch (error) {\n        console.error('  ❌ 动态路由加载失败:', error)\n        this.loading = false\n      }\n    }\n  }\n}\n", null]}