{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\UserCenter.vue", "mtime": 1753687910547}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport Sidebar from './components/Sidebar.vue';\nimport FloatingNotifications from './components/FloatingNotifications.vue';\nimport Overview from './views/Overview.vue';\nimport Profile from './views/Profile.vue';\nimport Credits from './views/Credits.vue';\nimport Orders from './views/Orders.vue';\nimport Usage from './views/Usage.vue'; // 🚫 临时注释掉会员服务和推荐奖励组件\n// import Membership from './views/Membership.vue'\n// import Referral from './views/Referral.vue'\n\nimport Notifications from './pages/Notifications.vue';\nimport { getUserFullInfo } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { usercenterAnimations } from '@/animations/gsap/pages/usercenterAnimations.js';\nimport Vue from 'vue';\nexport default {\n  name: 'UserCenter',\n  components: {\n    WebsitePage: WebsitePage,\n    Sidebar: Sidebar,\n    FloatingNotifications: FloatingNotifications,\n    Overview: Overview,\n    Profile: Profile,\n    Credits: Credits,\n    Orders: Orders,\n    Usage: Usage,\n    // 🚫 临时注释掉会员服务和推荐奖励组件\n    // Membership,\n    // Referral,\n    Notifications: Notifications\n  },\n  data: function data() {\n    return {\n      loading: true,\n      currentPage: 'overview',\n      userInfo: {\n        nickname: '',\n        email: '',\n        avatar: '',\n        phone: '',\n        accountBalance: 0,\n        currentRole: '普通用户',\n        totalConsumption: 0,\n        totalRecharge: 0,\n        memberExpireTime: null,\n        apiKey: '',\n        createTime: null,\n        // 🔑 关键：添加密码修改状态字段\n        passwordChanged: 0\n      },\n      pageMap: {\n        overview: '概览',\n        profile: '账户设置',\n        credits: '账户管理',\n        orders: '订单记录',\n        usage: '使用记录',\n        notifications: '系统通知' // 🚫 临时注释掉会员服务和推荐奖励页面\n        // membership: '会员服务',\n        // referral: '推荐奖励'\n\n      }\n    };\n  },\n  computed: {\n    currentPageTitle: function currentPageTitle() {\n      return this.pageMap[this.currentPage] || '';\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              if (this.checkLoginStatus()) {\n                _context.next = 2;\n                break;\n              }\n\n              return _context.abrupt(\"return\");\n\n            case 2:\n              _context.next = 4;\n              return this.loadUserInfo();\n\n            case 4:\n              this.initAnimations();\n\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    /**\n     * 检查登录状态 - 使用与路由守卫相同的TOKEN检查方法\n     */\n    checkLoginStatus: function checkLoginStatus() {\n      var token = Vue.ls.get(ACCESS_TOKEN);\n\n      if (!token) {\n        console.log('🔍 UserCenter: 未登录，重定向到登录页');\n        this.$message.warning('请先登录');\n        this.$router.push({\n          path: '/login',\n          query: {\n            redirect: this.$route.fullPath\n          }\n        });\n        return false;\n      }\n\n      console.log('🔍 UserCenter: 已登录，TOKEN存在');\n      return true;\n    },\n    loadUserInfo: function () {\n      var _loadUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response, rawData, mappedData;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                this.loading = true;\n                _context2.next = 4;\n                return getUserFullInfo();\n\n              case 4:\n                response = _context2.sent;\n                console.log('🔍 UserCenter: 完整的响应对象:', response);\n                console.log('🔍 UserCenter: response.success:', response.success);\n                console.log('🔍 UserCenter: response.data:', response.data);\n                console.log('🔍 UserCenter: response.result:', response.result);\n                console.log('🔍 UserCenter: response.message:', response.message);\n\n                if (!response.success) {\n                  _context2.next = 20;\n                  break;\n                }\n\n                // 使用正确的字段：response.result 而不是 response.data\n                rawData = response.result || response.data || {};\n                console.log('🔍 UserCenter: 后端返回的原始数据:', rawData); // 字段名映射：后端下划线 -> 前端驼峰命名\n\n                mappedData = {\n                  nickname: rawData.nickname || '',\n                  email: rawData.email || '',\n                  avatar: rawData.avatar || '',\n                  phone: rawData.phone || '',\n                  accountBalance: rawData.account_balance || 0,\n                  totalConsumption: rawData.total_consumption || 0,\n                  totalRecharge: rawData.total_recharge || 0,\n                  currentRole: rawData.current_role || '普通用户',\n                  apiKey: rawData.api_key || '',\n                  createTime: rawData.user_create_time || null,\n                  username: rawData.username || '',\n                  realname: rawData.realname || '',\n                  // 🔑 关键：添加密码修改状态字段\n                  passwordChanged: rawData.password_changed || 0\n                };\n                console.log('🔍 UserCenter: 映射后的数据:', mappedData); // 使用Object.assign确保响应式更新\n\n                Object.assign(this.userInfo, mappedData);\n                console.log('🔍 UserCenter: 最终的userInfo:', this.userInfo); // 强制触发视图更新\n\n                this.$forceUpdate();\n                _context2.next = 29;\n                break;\n\n              case 20:\n                console.error('🔍 UserCenter: 获取用户信息失败');\n                console.error('🔍 UserCenter: response.code:', response.code);\n                console.error('🔍 UserCenter: response.message:', response.message);\n                console.error('🔍 UserCenter: 完整响应:', response); // 检查是否是认证失败\n\n                if (!(response.code === 401 || response.message && response.message.includes('Token') || response.message && response.message.includes('登录'))) {\n                  _context2.next = 28;\n                  break;\n                }\n\n                this.$message.warning('登录已过期，请重新登录');\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 28:\n                this.$message.error(\"\\u83B7\\u53D6\\u7528\\u6237\\u4FE1\\u606F\\u5931\\u8D25: \".concat(response.message || '未知错误'));\n\n              case 29:\n                _context2.next = 39;\n                break;\n\n              case 31:\n                _context2.prev = 31;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('加载用户信息失败:', _context2.t0); // 检查是否是认证相关错误\n\n                if (!(_context2.t0.response && _context2.t0.response.status === 401)) {\n                  _context2.next = 38;\n                  break;\n                }\n\n                this.$message.warning('登录已过期，请重新登录');\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context2.abrupt(\"return\");\n\n              case 38:\n                this.$message.error('加载用户信息失败，请刷新重试');\n\n              case 39:\n                _context2.prev = 39;\n                this.loading = false;\n                return _context2.finish(39);\n\n              case 42:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 31, 39, 42]]);\n      }));\n\n      function loadUserInfo() {\n        return _loadUserInfo.apply(this, arguments);\n      }\n\n      return loadUserInfo;\n    }(),\n    // 刷新用户信息的方法（供子组件调用）\n    getUserInfo: function () {\n      var _getUserInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                console.log('🔍 UserCenter: 收到刷新用户信息请求');\n                _context3.next = 3;\n                return this.loadUserInfo();\n\n              case 3:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function getUserInfo() {\n        return _getUserInfo.apply(this, arguments);\n      }\n\n      return getUserInfo;\n    }(),\n    initAnimations: function initAnimations() {\n      var _this = this;\n\n      this.$nextTick(function () {\n        // 初始化GSAP动画\n        if (_this.$animationManager) {\n          usercenterAnimations.init(_this.$animationManager);\n        }\n      });\n    },\n    handleMenuChange: function handleMenuChange(page) {\n      if (this.currentPage !== page) {\n        // 页面切换动画\n        var fromPage = \".page-\".concat(this.currentPage);\n        var toPage = \".page-\".concat(page);\n\n        if (this.$animationManager) {\n          usercenterAnimations.switchPage(fromPage, toPage);\n        }\n\n        this.currentPage = page; // 更新URL（可选）\n\n        this.$router.replace({\n          path: '/usercenter',\n          query: {\n            page: page\n          }\n        });\n      }\n    },\n    handleNavigate: function handleNavigate(page) {\n      this.handleMenuChange(page);\n    },\n    handleSidebarAction: function handleSidebarAction(action) {\n      switch (action) {\n        case 'recharge':\n          this.handleMenuChange('credits');\n          break;\n        // 🚫 临时注释掉升级会员功能\n        // case 'upgrade':\n        //   this.handleMenuChange('membership')\n        //   break\n\n        default:\n          console.log('未知操作:', action);\n      }\n    },\n    handleNavigateToNotifications: function handleNavigateToNotifications() {\n      // 导航到系统通知页面\n      this.handleMenuChange('notifications');\n    },\n    handleNotificationUpdated: function handleNotificationUpdated() {\n      // 通知更新时，刷新悬浮通知组件的数据\n      if (this.$refs.floatingNotifications) {\n        this.$refs.floatingNotifications.loadNotifications();\n      } // 同时通知Sidebar组件更新未读通知数量\n\n\n      if (this.$refs.sidebar) {\n        this.$refs.sidebar.loadUnreadNotificationCount();\n      }\n    },\n    handleAvatarUpdated: function handleAvatarUpdated(newAvatar) {\n      // 头像更新时，更新用户信息\n      console.log('🔍 UserCenter: 头像更新事件，新头像:', newAvatar);\n      this.userInfo.avatar = newAvatar;\n      this.$forceUpdate();\n    },\n    handleInfoUpdated: function handleInfoUpdated(updatedInfo) {\n      // 基本信息更新时，更新用户信息\n      console.log('🔍 UserCenter: 基本信息更新事件:', updatedInfo);\n      Object.assign(this.userInfo, updatedInfo);\n      this.$forceUpdate();\n    },\n    handleApiKeyUpdated: function handleApiKeyUpdated(newApiKey) {\n      // API Key更新时，保存当前滚动位置\n      var currentScrollY = window.pageYOffset || document.documentElement.scrollTop;\n      console.log('🔍 UserCenter: API Key更新事件，当前滚动位置:', currentScrollY); // 使用Vue.set确保响应式更新\n\n      this.$set(this.userInfo, 'apiKey', newApiKey); // 在下一个tick恢复滚动位置\n\n      this.$nextTick(function () {\n        window.scrollTo(0, currentScrollY);\n        console.log('🔍 UserCenter: 已恢复滚动位置到:', currentScrollY);\n      });\n    },\n    // 🔑 新增：密码修改处理\n    handlePasswordChanged: function handlePasswordChanged() {\n      console.log('🔍 UserCenter: 收到密码修改事件');\n\n      if (this.userInfo) {\n        this.userInfo.passwordChanged = 1;\n        console.log('🔍 UserCenter: passwordChanged已更新为1');\n      }\n    }\n  },\n  // 路由守卫：根据URL参数设置当前页面\n  beforeRouteEnter: function beforeRouteEnter(to, _from, next) {\n    next(function (vm) {\n      var page = to.query.page;\n\n      if (page && vm.pageMap[page]) {\n        vm.currentPage = page;\n      }\n    });\n  },\n  beforeRouteUpdate: function beforeRouteUpdate(to, _from, next) {\n    var page = to.query.page;\n\n    if (page && this.pageMap[page]) {\n      this.currentPage = page;\n    }\n\n    next();\n  }\n};", null]}