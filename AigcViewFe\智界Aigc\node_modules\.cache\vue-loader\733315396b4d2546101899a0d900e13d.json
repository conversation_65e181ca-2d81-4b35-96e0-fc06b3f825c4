{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue?vue&type=style&index=0&id=033d3257&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue", "mtime": 1753756420429}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.payment-failure-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.failure-container {\n  background: white;\n  border-radius: 16px;\n  padding: 48px 40px;\n  text-align: center;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n}\n\n.failure-icon {\n  font-size: 80px;\n  color: #ff4d4f;\n  margin-bottom: 24px;\n}\n\n.failure-title {\n  font-size: 32px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 32px;\n}\n\n.failure-message {\n  margin-bottom: 32px;\n  color: #595959;\n  line-height: 1.6;\n  text-align: left;\n}\n\n.reason-list {\n  margin: 16px 0;\n  padding-left: 20px;\n}\n\n.reason-list li {\n  margin-bottom: 8px;\n  color: #8c8c8c;\n}\n\n.order-info {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 32px;\n  text-align: left;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  color: #8c8c8c;\n  font-size: 14px;\n}\n\n.value {\n  color: #262626;\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.value.amount {\n  color: #f5222d;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.action-buttons {\n  margin-bottom: 32px;\n}\n\n.help-info {\n  text-align: left;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .failure-container {\n    padding: 32px 24px;\n  }\n  \n  .failure-title {\n    font-size: 24px;\n  }\n  \n  .failure-icon {\n    font-size: 60px;\n  }\n  \n  .action-buttons .ant-btn {\n    margin: 8px 4px !important;\n  }\n}\n", null]}