{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue", "mtime": 1753702910988}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport StatsCard from '../components/StatsCard.vue';\nimport { getReferralStats, generateReferralLink, getReferralList } from '@/api/usercenter';\nexport default {\n  name: 'UserCenterReferral',\n  components: {\n    StatsCard: StatsCard\n  },\n  data: function data() {\n    return {\n      loading: true,\n      linkLoading: false,\n      recordLoading: false,\n      withdrawalLoading: false,\n      withdrawalHistoryLoading: false,\n      defaultAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',\n      // 推荐统计\n      referralStats: {\n        totalReferrals: 0,\n        totalRewards: 0,\n        availableRewards: 0,\n        monthlyReferrals: 0\n      },\n      // 佣金比例配置\n      commissionConfig: {\n        userType: 'NORMAL',\n        // 当前用户类型\n        commissionLevel: 1,\n        // 当前佣金等级\n        inviteCount: 0 // 有效邀请数量\n\n      },\n      // 佣金比例数据\n      normalRate: 30,\n      normalHighRate: 40,\n      normalTopRate: 50,\n      vipRate: 35,\n      vipHighRate: 45,\n      vipTopRate: 50,\n      svipRate: 50,\n      currentCommissionRate: 30,\n      // 推荐链接\n      referralLink: '',\n      showQRModal: false,\n      // 推荐记录\n      recordFilters: {\n        status: '',\n        dateRange: []\n      },\n      referralRecords: [],\n      recordPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      recordColumns: [{\n        title: '好友信息',\n        key: 'friendInfo',\n        width: 200,\n        scopedSlots: {\n          customRender: 'friendInfo'\n        }\n      }, {\n        title: '注册时间',\n        dataIndex: 'registerTime',\n        key: 'registerTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'time'\n        }\n      }, {\n        title: '奖励金额',\n        dataIndex: 'rewardAmount',\n        key: 'rewardAmount',\n        width: 120,\n        align: 'right',\n        scopedSlots: {\n          customRender: 'rewardAmount'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        width: 100,\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }],\n      // 提现相关\n      withdrawalAmount: 50,\n      realName: '',\n      alipayAccount: '',\n      withdrawalInfo: {\n        availableAmount: 0,\n        frozenAmount: 0,\n        minWithdrawalAmount: 50,\n        hasPendingRequest: false,\n        pendingAmount: 0,\n        pendingTime: null,\n        canWithdraw: false,\n        message: ''\n      },\n      showConfirmWithdrawal: false,\n      confirmChecked: false,\n      withdrawalHistory: [],\n      withdrawalPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0\n      },\n      withdrawalColumns: [{\n        title: '提现金额',\n        dataIndex: 'withdrawal_amount',\n        key: 'amount',\n        width: 120,\n        align: 'right',\n        scopedSlots: {\n          customRender: 'amount'\n        }\n      }, {\n        title: '真实姓名',\n        dataIndex: 'alipay_name',\n        key: 'realName',\n        width: 100\n      }, {\n        title: '支付宝账号',\n        dataIndex: 'alipay_account',\n        key: 'alipayAccount',\n        ellipsis: true\n      }, {\n        title: '申请时间',\n        dataIndex: 'apply_time',\n        key: 'applyTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'time'\n        }\n      }, {\n        title: '审核时间',\n        dataIndex: 'review_time',\n        key: 'reviewTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'time'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        width: 100,\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }]\n    };\n  },\n  computed: {\n    monthlyTrend: function monthlyTrend() {\n      return {\n        type: 'up',\n        value: 20.5,\n        text: '较上月增长20.5%'\n      };\n    },\n    isFormValid: function isFormValid() {\n      return this.withdrawalAmount >= 50 && this.withdrawalAmount <= this.withdrawalInfo.availableAmount && this.realName.trim() !== '' && this.alipayAccount.trim() !== '';\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    loadData: function () {\n      var _loadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                this.loading = true;\n                _context2.next = 4;\n                return Promise.all([this.loadReferralStats(), this.loadReferralLink(), this.loadReferralRecords(), this.loadWithdrawalInfo(), this.loadWithdrawalHistory(), this.loadCommissionConfig()]);\n\n              case 4:\n                _context2.next = 10;\n                break;\n\n              case 6:\n                _context2.prev = 6;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('加载推荐数据失败:', _context2.t0);\n                this.$message.error('加载数据失败，请刷新重试');\n\n              case 10:\n                _context2.prev = 10;\n                this.loading = false;\n                return _context2.finish(10);\n\n              case 13:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 6, 10, 13]]);\n      }));\n\n      function loadData() {\n        return _loadData.apply(this, arguments);\n      }\n\n      return loadData;\n    }(),\n    loadReferralStats: function () {\n      var _loadReferralStats = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                _context3.next = 3;\n                return getReferralStats();\n\n              case 3:\n                response = _context3.sent;\n\n                if (response.success) {\n                  // 修复：使用 result 字段而不是 data 字段\n                  this.referralStats = response.result || {};\n                }\n\n                _context3.next = 10;\n                break;\n\n              case 7:\n                _context3.prev = 7;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('加载推荐统计失败:', _context3.t0);\n\n              case 10:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n\n      function loadReferralStats() {\n        return _loadReferralStats.apply(this, arguments);\n      }\n\n      return loadReferralStats;\n    }(),\n    loadReferralLink: function () {\n      var _loadReferralLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n\n                if (this.referralLink) {\n                  _context4.next = 4;\n                  break;\n                }\n\n                _context4.next = 4;\n                return this.handleGenerateLink();\n\n              case 4:\n                _context4.next = 9;\n                break;\n\n              case 6:\n                _context4.prev = 6;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('加载推荐链接失败:', _context4.t0);\n\n              case 9:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 6]]);\n      }));\n\n      function loadReferralLink() {\n        return _loadReferralLink.apply(this, arguments);\n      }\n\n      return loadReferralLink;\n    }(),\n    loadReferralRecords: function () {\n      var _loadReferralRecords = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                this.recordLoading = true;\n                params = _objectSpread({\n                  current: this.recordPagination.current,\n                  size: this.recordPagination.pageSize\n                }, this.recordFilters);\n                _context5.next = 5;\n                return getReferralList(params);\n\n              case 5:\n                response = _context5.sent;\n\n                if (response.success) {\n                  // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n                  this.referralRecords = response.result && response.result.records || [];\n                  this.recordPagination.total = response.result && response.result.total || 0;\n                }\n\n                _context5.next = 13;\n                break;\n\n              case 9:\n                _context5.prev = 9;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('加载推荐记录失败:', _context5.t0);\n                this.referralRecords = [];\n\n              case 13:\n                _context5.prev = 13;\n                this.recordLoading = false;\n                return _context5.finish(13);\n\n              case 16:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 9, 13, 16]]);\n      }));\n\n      function loadReferralRecords() {\n        return _loadReferralRecords.apply(this, arguments);\n      }\n\n      return loadReferralRecords;\n    }(),\n    loadCommissionConfig: function () {\n      var _loadCommissionConfig = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                _context6.next = 3;\n                return this.$http.get('/api/user/commission-config');\n\n              case 3:\n                response = _context6.sent;\n\n                if (response.data && response.data.success) {\n                  this.commissionConfig = response.data.result || {};\n                  this.calculateCurrentCommissionRate();\n                }\n\n                _context6.next = 12;\n                break;\n\n              case 7:\n                _context6.prev = 7;\n                _context6.t0 = _context6[\"catch\"](0);\n                console.error('加载佣金配置失败:', _context6.t0); // 使用默认配置\n\n                this.commissionConfig = {\n                  userType: 'NORMAL',\n                  commissionLevel: 1,\n                  inviteCount: 0\n                };\n                this.calculateCurrentCommissionRate();\n\n              case 12:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 7]]);\n      }));\n\n      function loadCommissionConfig() {\n        return _loadCommissionConfig.apply(this, arguments);\n      }\n\n      return loadCommissionConfig;\n    }(),\n    calculateCurrentCommissionRate: function calculateCurrentCommissionRate() {\n      var _this$commissionConfi = this.commissionConfig,\n          userType = _this$commissionConfi.userType,\n          inviteCount = _this$commissionConfi.inviteCount;\n\n      if (userType === 'SVIP') {\n        this.currentCommissionRate = this.svipRate;\n      } else if (userType === 'VIP') {\n        if (inviteCount >= 30) {\n          this.currentCommissionRate = this.vipTopRate;\n        } else if (inviteCount >= 10) {\n          this.currentCommissionRate = this.vipHighRate;\n        } else {\n          this.currentCommissionRate = this.vipRate;\n        }\n      } else {\n        // NORMAL用户\n        if (inviteCount >= 30) {\n          this.currentCommissionRate = this.normalTopRate;\n        } else if (inviteCount >= 10) {\n          this.currentCommissionRate = this.normalHighRate;\n        } else {\n          this.currentCommissionRate = this.normalRate;\n        }\n      }\n    },\n    // 加载提现信息\n    loadWithdrawalInfo: function () {\n      var _loadWithdrawalInfo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                _context7.prev = 0;\n                _context7.next = 3;\n                return this.$http.get('/api/usercenter/withdrawalInfo');\n\n              case 3:\n                response = _context7.sent;\n\n                if (response.data.success) {\n                  this.withdrawalInfo = response.data.result;\n                }\n\n                _context7.next = 10;\n                break;\n\n              case 7:\n                _context7.prev = 7;\n                _context7.t0 = _context7[\"catch\"](0);\n                console.error('获取提现信息失败:', _context7.t0);\n\n              case 10:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[0, 7]]);\n      }));\n\n      function loadWithdrawalInfo() {\n        return _loadWithdrawalInfo.apply(this, arguments);\n      }\n\n      return loadWithdrawalInfo;\n    }(),\n    loadWithdrawalHistory: function () {\n      var _loadWithdrawalHistory = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                this.withdrawalHistoryLoading = true;\n                params = {\n                  current: this.withdrawalPagination.current,\n                  size: this.withdrawalPagination.pageSize\n                };\n                _context8.next = 5;\n                return this.$http.get('/api/usercenter/withdrawalHistory', {\n                  params: params\n                });\n\n              case 5:\n                response = _context8.sent;\n\n                if (response && response.success) {\n                  // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n                  this.withdrawalHistory = response.result && response.result.records || [];\n                  this.withdrawalPagination.total = response.result && response.result.total || 0;\n                } else {\n                  this.withdrawalHistory = [];\n                }\n\n                _context8.next = 12;\n                break;\n\n              case 9:\n                _context8.prev = 9;\n                _context8.t0 = _context8[\"catch\"](0);\n                console.error('加载提现记录失败:', _context8.t0);\n\n              case 12:\n                _context8.prev = 12;\n                this.withdrawalHistoryLoading = false;\n                return _context8.finish(12);\n\n              case 15:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[0, 9, 12, 15]]);\n      }));\n\n      function loadWithdrawalHistory() {\n        return _loadWithdrawalHistory.apply(this, arguments);\n      }\n\n      return loadWithdrawalHistory;\n    }(),\n    // 推荐链接相关方法\n    handleGenerateLink: function () {\n      var _handleGenerateLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                _context9.prev = 0;\n                this.linkLoading = true;\n                _context9.next = 4;\n                return generateReferralLink();\n\n              case 4:\n                response = _context9.sent;\n\n                if (response.success) {\n                  // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法\n                  this.referralLink = response.result && response.result.link || '';\n                  this.$message.success('推荐链接生成成功');\n                }\n\n                _context9.next = 12;\n                break;\n\n              case 8:\n                _context9.prev = 8;\n                _context9.t0 = _context9[\"catch\"](0);\n                console.error('生成推荐链接失败:', _context9.t0);\n                this.$message.error('生成链接失败，请重试');\n\n              case 12:\n                _context9.prev = 12;\n                this.linkLoading = false;\n                return _context9.finish(12);\n\n              case 15:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[0, 8, 12, 15]]);\n      }));\n\n      function handleGenerateLink() {\n        return _handleGenerateLink.apply(this, arguments);\n      }\n\n      return handleGenerateLink;\n    }(),\n    handleCopyLink: function handleCopyLink() {\n      var _this = this;\n\n      if (!this.referralLink) {\n        this.$message.warning('请先生成推荐链接');\n        return;\n      }\n\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(this.referralLink).then(function () {\n          _this.$message.success('推荐链接已复制到剪贴板');\n        }).catch(function () {\n          _this.fallbackCopyTextToClipboard(_this.referralLink);\n        });\n      } else {\n        this.fallbackCopyTextToClipboard(this.referralLink);\n      }\n    },\n    fallbackCopyTextToClipboard: function fallbackCopyTextToClipboard(text) {\n      var textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n\n      try {\n        document.execCommand('copy');\n        this.$message.success('推荐链接已复制到剪贴板');\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制');\n      }\n\n      document.body.removeChild(textArea);\n    },\n    handleGenerateQRCode: function handleGenerateQRCode() {\n      var _this2 = this;\n\n      if (!this.referralLink) {\n        this.$message.warning('请先生成推荐链接');\n        return;\n      }\n\n      this.showQRModal = true;\n      this.$nextTick(function () {\n        _this2.renderQRCode();\n      });\n    },\n    renderQRCode: function renderQRCode() {\n      // TODO: 使用 QRCode.js 生成二维码\n      console.log('生成二维码:', this.referralLink);\n    },\n    handleDownloadQR: function handleDownloadQR() {\n      this.$message.info('下载二维码功能开发中...');\n    },\n    handleShareToSocial: function handleShareToSocial() {\n      this.$message.info('分享到社交媒体功能开发中...');\n    },\n    // 提现相关方法\n    setMaxAmount: function setMaxAmount() {\n      this.withdrawalAmount = this.withdrawalInfo.availableAmount;\n    },\n    showConfirmModal: function showConfirmModal() {\n      if (!this.isFormValid) {\n        this.$message.warning('请完善提现信息');\n        return;\n      } // 验证姓名格式\n\n\n      if (!/^[\\u4e00-\\u9fa5]{2,4}$/.test(this.realName.trim())) {\n        this.$message.error('请输入正确的中文姓名（2-4个汉字）');\n        return;\n      } // 验证支付宝账号格式（只允许手机号）\n\n\n      var phoneRegex = /^1[3-9]\\d{9}$/;\n\n      if (!phoneRegex.test(this.alipayAccount)) {\n        this.$message.error('请输入正确的手机号');\n        return;\n      }\n\n      this.showConfirmWithdrawal = true;\n      this.confirmChecked = false;\n    },\n    handleConfirmWithdrawal: function () {\n      var _handleConfirmWithdrawal = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                _context10.prev = 0;\n                this.withdrawalLoading = true;\n                params = {\n                  withdrawalAmount: this.withdrawalAmount,\n                  realName: this.realName.trim(),\n                  alipayAccount: this.alipayAccount.trim()\n                };\n                _context10.next = 5;\n                return this.$http.post('/api/usercenter/applyWithdrawal', params);\n\n              case 5:\n                response = _context10.sent;\n\n                if (!response.data.success) {\n                  _context10.next = 16;\n                  break;\n                }\n\n                this.$message.success('提现申请提交成功，请等待审核'); // 关闭弹窗\n\n                this.showConfirmWithdrawal = false; // 重置表单\n\n                this.withdrawalAmount = 50;\n                this.realName = '';\n                this.alipayAccount = ''; // 刷新数据\n\n                _context10.next = 14;\n                return Promise.all([this.loadWithdrawalInfo(), this.loadWithdrawalHistory()]);\n\n              case 14:\n                _context10.next = 17;\n                break;\n\n              case 16:\n                this.$message.error(response.data.message || '提现申请失败');\n\n              case 17:\n                _context10.next = 23;\n                break;\n\n              case 19:\n                _context10.prev = 19;\n                _context10.t0 = _context10[\"catch\"](0);\n                console.error('提现申请失败:', _context10.t0);\n                this.$message.error('提现申请失败，请重试');\n\n              case 23:\n                _context10.prev = 23;\n                this.withdrawalLoading = false;\n                return _context10.finish(23);\n\n              case 26:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[0, 19, 23, 26]]);\n      }));\n\n      function handleConfirmWithdrawal() {\n        return _handleConfirmWithdrawal.apply(this, arguments);\n      }\n\n      return handleConfirmWithdrawal;\n    }(),\n    // 表格相关方法\n    handleRecordFilterChange: function handleRecordFilterChange() {\n      this.recordPagination.current = 1;\n      this.loadReferralRecords();\n    },\n    handleRecordTableChange: function handleRecordTableChange(_ref) {\n      var pagination = _ref.pagination;\n      this.recordPagination = _objectSpread(_objectSpread({}, this.recordPagination), pagination);\n      this.loadReferralRecords();\n    },\n    handleWithdrawalTableChange: function handleWithdrawalTableChange(_ref2) {\n      var pagination = _ref2.pagination;\n      this.withdrawalPagination = _objectSpread(_objectSpread({}, this.withdrawalPagination), pagination);\n      this.loadWithdrawalHistory();\n    },\n    // 工具方法\n    maskEmail: function maskEmail(email) {\n      if (!email) return '';\n\n      var _email$split = email.split('@'),\n          _email$split2 = _slicedToArray(_email$split, 2),\n          username = _email$split2[0],\n          domain = _email$split2[1];\n\n      if (username.length <= 3) {\n        return \"\".concat(username[0], \"***@\").concat(domain);\n      }\n\n      return \"\".concat(username.substring(0, 3), \"***@\").concat(domain);\n    },\n    getRecordStatusClass: function getRecordStatusClass(status) {\n      var classMap = {\n        pending: 'status-pending',\n        confirmed: 'status-confirmed',\n        rewarded: 'status-rewarded'\n      };\n      return classMap[status] || '';\n    },\n    getRecordStatusText: function getRecordStatusText(status) {\n      var textMap = {\n        pending: '待确认',\n        confirmed: '已确认',\n        rewarded: '已奖励'\n      };\n      return textMap[status] || '未知状态';\n    },\n    getWithdrawalStatusColor: function getWithdrawalStatusColor(status) {\n      var colorMap = {\n        1: 'orange',\n        // 待审核 - 橙色\n        2: 'green',\n        // 已发放 - 绿色\n        3: 'red' // 审核拒绝 - 红色\n\n      };\n      return colorMap[status] || 'default';\n    },\n    getWithdrawalStatusText: function getWithdrawalStatusText(status, reviewRemark) {\n      var textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝'\n      };\n      var statusText = textMap[status] || '未知状态'; // 如果是审核拒绝状态且有拒绝原因，则添加原因\n\n      if (status === 3 && reviewRemark) {\n        statusText += \"\\uFF08\".concat(reviewRemark, \"\\uFF09\");\n      }\n\n      return statusText;\n    },\n    formatNumber: function formatNumber(number) {\n      if (!number) return '0.00';\n      return parseFloat(number).toFixed(2);\n    },\n    formatDateTime: function formatDateTime(dateString) {\n      if (!dateString) return '-';\n\n      try {\n        var date = new Date(dateString);\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } catch (error) {\n        return '-';\n      }\n    }\n  }\n};", null]}