{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=template&id=2812af9c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753672621586}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nav',{ref:\"navbar\",staticClass:\"website-navbar\",class:{ 'scrolled': _vm.isScrolled, 'transparent': _vm.isTransparent }},[_c('div',{staticClass:\"nav-container\"},[_c('div',{ref:\"navBrand\",staticClass:\"nav-brand\",on:{\"click\":_vm.goHome}},[_c('LogoImage',{attrs:{\"size\":\"medium\",\"hover\":true,\"container-class\":\"brand-logo-container\",\"image-class\":\"brand-logo-image\",\"fallback-class\":\"brand-logo-fallback\"}}),_c('span',{staticClass:\"brand-text\"},[_vm._v(\"智界AIGC\")])],1),_c('div',{ref:\"navMenu\",staticClass:\"nav-menu\"},_vm._l((_vm.menuItems),function(item){return _c(item.path && item.path !== '' ? 'router-link' : 'span',{key:item.name,tag:\"component\",staticClass:\"nav-link\",class:{\n          'active': _vm.$route.path === item.path,\n          'nav-link-disabled': !item.path || item.path === ''\n        },attrs:{\"to\":item.path && item.path !== '' ? item.path : undefined},on:{\"click\":function($event){(!item.path || item.path === '') ? _vm.handleDevelopingClick(item.name) : undefined}}},[_c('a-icon',{staticClass:\"nav-icon\",attrs:{\"type\":item.icon}}),_c('span',{staticClass:\"nav-text\"},[_vm._v(_vm._s(item.name))])],1)}),1),_c('div',{ref:\"navActions\",staticClass:\"nav-actions\"},[(!_vm.isLoggedIn)?_c('button',{staticClass:\"btn-secondary\",on:{\"click\":_vm.handleLogin}},[_vm._v(\"登录\")]):(_vm.isAdmin)?_c('button',{staticClass:\"btn-admin\",on:{\"click\":_vm.goToAdmin}},[_c('a-icon',{attrs:{\"type\":\"dashboard\"}}),_vm._v(\"\\n        后台管理\\n      \")],1):_vm._e()]),_c('button',{ref:\"mobileMenuBtn\",staticClass:\"mobile-menu-btn\",on:{\"click\":_vm.toggleMobileMenu}},[_c('a-icon',{attrs:{\"type\":_vm.mobileMenuOpen ? 'close' : 'menu'}})],1)]),_c('div',{ref:\"mobileMenu\",staticClass:\"mobile-menu\",class:{ 'open': _vm.mobileMenuOpen }},[_vm._l((_vm.menuItems),function(item){return _c(item.path && item.path !== '' ? 'router-link' : 'span',{key:item.name,tag:\"component\",staticClass:\"mobile-nav-link\",class:{ 'mobile-nav-link-disabled': !item.path || item.path === '' },attrs:{\"to\":item.path && item.path !== '' ? item.path : undefined},on:{\"click\":function($event){return _vm.handleMobileMenuClick(item)}}},[_c('a-icon',{staticClass:\"mobile-nav-icon\",attrs:{\"type\":item.icon}}),_c('span',{staticClass:\"mobile-nav-text\"},[_vm._v(_vm._s(item.name))])],1)}),_c('div',{staticClass:\"mobile-actions\"},[(!_vm.isLoggedIn)?_c('button',{staticClass:\"mobile-btn-login\",on:{\"click\":_vm.handleLogin}},[_vm._v(\"登录\")]):(_vm.isAdmin)?_c('button',{staticClass:\"mobile-btn-admin\",on:{\"click\":_vm.goToAdmin}},[_c('a-icon',{attrs:{\"type\":\"dashboard\"}}),_vm._v(\"\\n        后台管理\\n      \")],1):_vm._e()])],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}