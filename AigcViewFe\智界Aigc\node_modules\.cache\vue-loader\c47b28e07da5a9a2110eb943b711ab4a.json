{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue?vue&type=template&id=2a8a8cf4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\home\\components\\HomeHeader.vue", "mtime": 1753672674026}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nav',{ref:\"navbar\",staticClass:\"home-navbar\",class:{ 'scrolled': _vm.isScrolled }},[_c('div',{staticClass:\"nav-container\"},[_c('div',{ref:\"navBrand\",staticClass:\"nav-brand\"},[_c('LogoImage',{attrs:{\"size\":\"medium\",\"hover\":true,\"container-class\":\"brand-logo-container\",\"image-class\":\"brand-logo-image\",\"fallback-class\":\"brand-logo-fallback\"}}),_c('span',{staticClass:\"brand-text\"},[_vm._v(\"智界AIGC\")])],1),_c('div',{ref:\"navMenu\",staticClass:\"nav-menu\"},_vm._l((_vm.menuItems),function(item){return _c('a',{key:item.name,staticClass:\"nav-link\",attrs:{\"href\":item.href}},[_c('a-icon',{staticClass:\"nav-icon\",attrs:{\"type\":item.icon}}),_c('span',{staticClass:\"nav-text\"},[_vm._v(_vm._s(item.name))])],1)}),0),_c('div',{ref:\"navActions\",staticClass:\"nav-actions\"},[_c('button',{staticClass:\"btn-secondary\"},[_vm._v(\"登录\")])]),_c('button',{ref:\"mobileMenuBtn\",staticClass:\"mobile-menu-btn\",on:{\"click\":_vm.toggleMobileMenu}},[_c('a-icon',{attrs:{\"type\":_vm.mobileMenuOpen ? 'close' : 'menu'}})],1)])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}