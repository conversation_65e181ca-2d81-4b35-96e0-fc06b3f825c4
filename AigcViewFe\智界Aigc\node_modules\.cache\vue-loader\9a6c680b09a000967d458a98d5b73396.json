{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=template&id=2812af9c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753672621586}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<nav class=\"website-navbar\" :class=\"{ 'scrolled': isScrolled, 'transparent': isTransparent }\" ref=\"navbar\">\n  <div class=\"nav-container\">\n    <div class=\"nav-brand\" ref=\"navBrand\" @click=\"goHome\">\n      <LogoImage\n        size=\"medium\"\n        :hover=\"true\"\n        container-class=\"brand-logo-container\"\n        image-class=\"brand-logo-image\"\n        fallback-class=\"brand-logo-fallback\"\n      />\n      <span class=\"brand-text\">智界AIGC</span>\n    </div>\n\n    <div class=\"nav-menu\" ref=\"navMenu\">\n      <component\n        v-for=\"item in menuItems\"\n        :key=\"item.name\"\n        :is=\"item.path && item.path !== '' ? 'router-link' : 'span'\"\n        :to=\"item.path && item.path !== '' ? item.path : undefined\"\n        class=\"nav-link\"\n        :class=\"{\n          'active': $route.path === item.path,\n          'nav-link-disabled': !item.path || item.path === ''\n        }\"\n        @click=\"(!item.path || item.path === '') ? handleDevelopingClick(item.name) : undefined\"\n      >\n        <a-icon :type=\"item.icon\" class=\"nav-icon\" />\n        <span class=\"nav-text\">{{ item.name }}</span>\n      </component>\n    </div>\n\n    <div class=\"nav-actions\" ref=\"navActions\">\n      <button v-if=\"!isLoggedIn\" class=\"btn-secondary\" @click=\"handleLogin\">登录</button>\n      <button v-else-if=\"isAdmin\" class=\"btn-admin\" @click=\"goToAdmin\">\n        <a-icon type=\"dashboard\" />\n        后台管理\n      </button>\n    </div>\n\n    <button class=\"mobile-menu-btn\" @click=\"toggleMobileMenu\" ref=\"mobileMenuBtn\">\n      <a-icon :type=\"mobileMenuOpen ? 'close' : 'menu'\" />\n    </button>\n  </div>\n\n  <!-- 移动端菜单 -->\n  <div class=\"mobile-menu\" :class=\"{ 'open': mobileMenuOpen }\" ref=\"mobileMenu\">\n    <component\n      v-for=\"item in menuItems\"\n      :key=\"item.name\"\n      :is=\"item.path && item.path !== '' ? 'router-link' : 'span'\"\n      :to=\"item.path && item.path !== '' ? item.path : undefined\"\n      class=\"mobile-nav-link\"\n      :class=\"{ 'mobile-nav-link-disabled': !item.path || item.path === '' }\"\n      @click=\"handleMobileMenuClick(item)\"\n    >\n      <a-icon :type=\"item.icon\" class=\"mobile-nav-icon\" />\n      <span class=\"mobile-nav-text\">{{ item.name }}</span>\n    </component>\n    <div class=\"mobile-actions\">\n      <button v-if=\"!isLoggedIn\" class=\"mobile-btn-login\" @click=\"handleLogin\">登录</button>\n      <button v-else-if=\"isAdmin\" class=\"mobile-btn-admin\" @click=\"goToAdmin\">\n        <a-icon type=\"dashboard\" />\n        后台管理\n      </button>\n    </div>\n  </div>\n</nav>\n", null]}