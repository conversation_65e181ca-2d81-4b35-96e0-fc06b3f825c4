{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=template&id=7ef8aeb4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"website-login\">\n  <!-- 动态背景 -->\n  <div class=\"login-background\">\n    <div class=\"bg-animated-grid\"></div>\n    <div class=\"bg-floating-elements\"></div>\n    <div class=\"bg-gradient-overlay\"></div>\n  </div>\n\n  <!-- 复用官网页头组件 -->\n  <WebsiteHeader />\n\n  <!-- 主要内容区域 -->\n  <div class=\"login-main\">\n    <!-- 左侧信息展示 -->\n    <div class=\"login-info\" ref=\"loginInfo\">\n      <div class=\"info-content\">\n        <div class=\"brand-showcase\">\n          <div class=\"brand-logo-large\">\n            <LogoImage\n              size=\"large\"\n              :hover=\"false\"\n              container-class=\"login-logo-container\"\n              image-class=\"login-logo-image\"\n              fallback-class=\"login-logo-fallback\"\n            />\n            <h1 class=\"brand-title\">智界AIGC</h1>\n          </div>\n          <p class=\"brand-slogan\">AI驱动的内容生成平台</p>\n        </div>\n\n        <div class=\"feature-highlights\">\n          <div class=\"feature-item\" v-for=\"(feature, index) in features\" :key=\"index\">\n            <div class=\"feature-icon\">\n              <a-icon :type=\"feature.icon\" />\n            </div>\n            <div class=\"feature-text\">\n              <h3>{{ feature.title }}</h3>\n              <p>{{ feature.description }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧登录表单 -->\n    <div class=\"login-container\" ref=\"loginContainer\">\n      <div class=\"login-card\">\n        <!-- 登录头部 -->\n        <div class=\"login-header\">\n          <h2 class=\"login-title\">欢迎使用智界AIGC</h2>\n          <p class=\"login-subtitle\">{{ inviteCodeFromUrl ? '您正在通过邀请链接登录' : '选择您的登录方式，开启AI创作之旅' }}</p>\n        </div>\n\n        <!-- 登录方式切换Tab -->\n        <div class=\"auth-tabs\">\n          <div class=\"tab-buttons\">\n            <button\n              :class=\"['tab-btn', { active: loginType === 'phone' }]\"\n              @click=\"switchLoginType('phone')\"\n            >\n              <a-icon type=\"mobile\" />\n              <span class=\"tab-text\">手机号</span>\n            </button>\n            <button\n              :class=\"['tab-btn', { active: loginType === 'email' }]\"\n              @click=\"switchLoginType('email')\"\n            >\n              <a-icon type=\"mail\" />\n              <span class=\"tab-text\">邮箱</span>\n            </button>\n            <!-- 🔐 微信登录（暂时隐藏，待后续配置） -->\n            <!-- <button\n              :class=\"['tab-btn', { active: loginType === 'wechat' }]\"\n              @click=\"switchLoginType('wechat')\"\n            >\n              <a-icon type=\"wechat\" />\n              <span class=\"tab-text\">微信</span>\n            </button> -->\n            <button\n              :class=\"['tab-btn', { active: loginType === 'password' }]\"\n              @click=\"switchLoginType('password')\"\n            >\n              <a-icon type=\"lock\" />\n              <span class=\"tab-text\">密码登录</span>\n            </button>\n          </div>\n        </div>\n\n        <!-- 登录表单 -->\n        <div class=\"login-form\">\n          <!-- 密码登录 -->\n          <div v-if=\"loginType === 'password'\" class=\"login-content\">\n            <a-form :form=\"form\" @submit=\"handleSubmit\" class=\"account-login-form\">\n            <!-- 用户名输入 -->\n            <div class=\"input-group\">\n              <a-form-item>\n                <a-input\n                  v-decorator=\"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]\"\n                  size=\"large\"\n                  placeholder=\"用户名或邮箱\"\n                  class=\"clean-input\"\n                >\n                  <a-icon slot=\"prefix\" type=\"user\" />\n                </a-input>\n              </a-form-item>\n            </div>\n\n            <!-- 密码输入 -->\n            <div class=\"input-group\">\n              <a-form-item>\n                <a-input-password\n                  v-decorator=\"['password', { rules: [{ required: true, message: '请输入密码' }] }]\"\n                  size=\"large\"\n                  placeholder=\"密码\"\n                  class=\"clean-input\"\n                >\n                  <a-icon slot=\"prefix\" type=\"lock\" />\n                </a-input-password>\n              </a-form-item>\n            </div>\n\n            <!-- 验证码 -->\n            <div class=\"input-group\">\n              <a-form-item>\n                <div class=\"captcha-row\">\n                  <a-input\n                    v-decorator=\"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                    size=\"large\"\n                    placeholder=\"验证码\"\n                    class=\"clean-input captcha-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                  </a-input>\n                  <div class=\"captcha-image-container\" @click=\"handleChangeCheckCode\">\n                    <img\n                      :src=\"randCodeImage\"\n                      class=\"captcha-image\"\n                      alt=\"验证码\"\n                    />\n                    <div class=\"captcha-refresh-overlay\">\n                      <a-icon type=\"reload\" />\n                    </div>\n                  </div>\n                </div>\n              </a-form-item>\n            </div>\n\n            <!-- 登录选项 -->\n            <div class=\"login-options\">\n              <a-checkbox v-model=\"rememberMe\" class=\"remember-me\">\n                记住我\n              </a-checkbox>\n              <a class=\"forgot-link\" @click=\"handleForgotPassword\">\n                忘记密码？\n              </a>\n            </div>\n\n            <!-- 登录按钮 -->\n            <a-form-item class=\"login-button-item\">\n              <a-button\n                type=\"primary\"\n                html-type=\"submit\"\n                size=\"large\"\n                :loading=\"loginLoading\"\n                class=\"login-submit-button\"\n                block\n              >\n                <span v-if=\"!loginLoading\">登录</span>\n                <span v-else>登录中...</span>\n              </a-button>\n            </a-form-item>\n            </a-form>\n          </div>\n\n          <!-- 手机号登录 -->\n          <div v-if=\"loginType === 'phone'\" class=\"login-content\">\n            <a-form :form=\"phoneLoginForm\" @submit=\"handlePhoneLogin\" class=\"phone-login-form\">\n              <!-- 手机号输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input\n                    v-decorator=\"['phone', { rules: [\n                      { required: true, message: '请输入手机号' },\n                      { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确' }\n                    ] }]\"\n                    size=\"large\"\n                    placeholder=\"请输入手机号\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"mobile\" />\n                  </a-input>\n                </a-form-item>\n              </div>\n\n              <!-- 短信验证码 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <div class=\"verify-code-row\">\n                    <a-input\n                      v-decorator=\"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入短信验证码\"\n                      class=\"clean-input verify-code-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                    </a-input>\n                    <a-button\n                      :disabled=\"smsCodeSending || smsCountdown > 0\"\n                      @click=\"sendLoginSmsCode\"\n                      class=\"send-code-btn\"\n                      size=\"large\"\n                    >\n                      {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '发送验证码' }}\n                    </a-button>\n                  </div>\n                </a-form-item>\n              </div>\n\n              <!-- 登录按钮 -->\n              <a-form-item class=\"login-button-item\">\n                <a-button\n                  type=\"primary\"\n                  html-type=\"submit\"\n                  size=\"large\"\n                  :loading=\"phoneLoginLoading\"\n                  class=\"login-submit-button\"\n                  block\n                >\n                  <span v-if=\"!phoneLoginLoading\">登录</span>\n                  <span v-else>登录中...</span>\n                </a-button>\n              </a-form-item>\n\n              <!-- 提示信息 -->\n              <div class=\"phone-login-tip\">\n                <a-alert\n                  message=\"手机号登录说明\"\n                  description=\"首次使用手机号登录将自动为您创建账户，无需设置密码\"\n                  type=\"info\"\n                  show-icon\n                />\n              </div>\n            </a-form>\n          </div>\n\n          <!-- 邮箱登录 -->\n          <div v-if=\"loginType === 'email'\" class=\"login-content\">\n            <a-form :form=\"emailLoginForm\" @submit=\"handleEmailLogin\" class=\"email-login-form\">\n              <!-- 邮箱输入 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <a-input\n                    v-decorator=\"['email', { rules: [\n                      { required: true, message: '请输入邮箱' },\n                      { type: 'email', message: '邮箱格式不正确' }\n                    ] }]\"\n                    size=\"large\"\n                    placeholder=\"请输入邮箱\"\n                    class=\"clean-input\"\n                  >\n                    <a-icon slot=\"prefix\" type=\"mail\" />\n                  </a-input>\n                </a-form-item>\n              </div>\n\n              <!-- 邮箱验证码 -->\n              <div class=\"input-group\">\n                <a-form-item>\n                  <div class=\"verify-code-row\">\n                    <a-input\n                      v-decorator=\"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"\n                      size=\"large\"\n                      placeholder=\"请输入邮箱验证码\"\n                      class=\"clean-input verify-code-input\"\n                    >\n                      <a-icon slot=\"prefix\" type=\"safety-certificate\" />\n                    </a-input>\n                    <a-button\n                      :disabled=\"emailCodeSending || emailCountdown > 0\"\n                      @click=\"sendLoginEmailCode\"\n                      class=\"send-code-btn\"\n                      size=\"large\"\n                    >\n                      {{ emailCountdown > 0 ? `${emailCountdown}s后重发` : '发送验证码' }}\n                    </a-button>\n                  </div>\n                </a-form-item>\n              </div>\n\n              <!-- 登录按钮 -->\n              <a-form-item class=\"login-button-item\">\n                <a-button\n                  type=\"primary\"\n                  html-type=\"submit\"\n                  size=\"large\"\n                  :loading=\"emailLoginLoading\"\n                  class=\"login-submit-button\"\n                  block\n                >\n                  <span v-if=\"!emailLoginLoading\">登录</span>\n                  <span v-else>登录中...</span>\n                </a-button>\n              </a-form-item>\n\n              <!-- 提示信息 -->\n              <div class=\"email-login-tip\">\n                <a-alert\n                  message=\"邮箱登录说明\"\n                  description=\"首次使用邮箱登录将自动为您创建账户，无需设置密码\"\n                  type=\"info\"\n                  show-icon\n                />\n              </div>\n            </a-form>\n          </div>\n\n          <!-- 微信登录 -->\n          <div v-if=\"loginType === 'wechat'\" class=\"login-content\">\n            <div class=\"wechat-login-container\">\n              <div class=\"wechat-qr-section\">\n                <div class=\"qr-code-container\">\n                  <img :src=\"wechatLoginQrCode\" alt=\"微信登录二维码\" class=\"qr-code-image\" v-if=\"wechatLoginQrCode\" />\n                  <div class=\"qr-loading\" v-else>\n                    <a-spin size=\"large\" />\n                    <p>正在生成二维码...</p>\n                  </div>\n                </div>\n                <div class=\"qr-instructions\">\n                  <h4>使用微信扫码登录</h4>\n                  <p>1. 打开微信扫一扫</p>\n                  <p>2. 扫描上方二维码</p>\n                  <p>3. 确认登录</p>\n                  <p v-if=\"inviteCodeFromUrl\" class=\"invite-tip\">* 您正在通过邀请链接登录</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}