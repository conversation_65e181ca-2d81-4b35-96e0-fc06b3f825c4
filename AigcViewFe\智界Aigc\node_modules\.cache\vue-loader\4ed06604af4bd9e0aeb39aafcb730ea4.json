{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=template&id=e299c5fe&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"withdrawal-management\"},[_vm._m(0),_c('div',{staticClass:\"search-section\"},[_c('a-card',{attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"layout\":\"inline\",\"model\":_vm.searchForm},on:{\"submit\":_vm.handleSearch}},[_c('a-form-item',{attrs:{\"label\":\"申请状态\"}},[_c('a-select',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"请选择状态\",\"allowClear\":\"\"},model:{value:(_vm.searchForm.status),callback:function ($$v) {_vm.$set(_vm.searchForm, \"status\", $$v)},expression:\"searchForm.status\"}},[_c('a-select-option',{attrs:{\"value\":1}},[_vm._v(\"待审核\")]),_c('a-select-option',{attrs:{\"value\":2}},[_vm._v(\"已发放\")]),_c('a-select-option',{attrs:{\"value\":3}},[_vm._v(\"审核拒绝\")]),_c('a-select-option',{attrs:{\"value\":4}},[_vm._v(\"已取消\")])],1)],1),_c('a-form-item',{attrs:{\"label\":\"申请时间\"}},[_c('a-range-picker',{attrs:{\"format\":\"YYYY-MM-DD\",\"placeholder\":['开始时间', '结束时间']},model:{value:(_vm.searchForm.dateRange),callback:function ($$v) {_vm.$set(_vm.searchForm, \"dateRange\", $$v)},expression:\"searchForm.dateRange\"}})],1),_c('a-form-item',{attrs:{\"label\":\"用户名\"}},[_c('a-input',{staticStyle:{\"width\":\"150px\"},attrs:{\"placeholder\":\"请输入用户名\"},model:{value:(_vm.searchForm.username),callback:function ($$v) {_vm.$set(_vm.searchForm, \"username\", $$v)},expression:\"searchForm.username\"}})],1),_c('a-form-item',{attrs:{\"label\":\"支付宝信息\"}},[_c('a-input',{staticStyle:{\"width\":\"150px\"},attrs:{\"placeholder\":\"支付宝账号或姓名\"},model:{value:(_vm.searchForm.alipayInfo),callback:function ($$v) {_vm.$set(_vm.searchForm, \"alipayInfo\", $$v)},expression:\"searchForm.alipayInfo\"}})],1),_c('a-form-item',[_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.loading},on:{\"click\":_vm.handleSearch}},[_c('a-icon',{attrs:{\"type\":\"search\"}}),_vm._v(\"\\n            搜索\\n          \")],1),_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},on:{\"click\":_vm.handleReset}},[_c('a-icon',{attrs:{\"type\":\"reload\"}}),_vm._v(\"\\n            重置\\n          \")],1)],1)],1)],1)],1),_c('div',{staticClass:\"table-section\"},[_c('a-card',{attrs:{\"bordered\":false}},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.dataSource,\"loading\":_vm.loading,\"pagination\":_vm.pagination,\"row-key\":\"id\",\"scroll\":{ x: 1200 }},on:{\"change\":_vm.handleTableChange},scopedSlots:_vm._u([{key:\"userInfo\",fn:function(text, record){return [(record)?_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"username\"},[_vm._v(_vm._s(record.username || '-'))]),_c('div',{staticClass:\"user-id\"},[_vm._v(\"ID: \"+_vm._s(record.user_id || '-'))])]):_c('span',[_vm._v(\"-\")])]}},{key:\"amount\",fn:function(text, record){return [(record)?_c('div',{staticClass:\"amount-info\"},[_c('div',{staticClass:\"amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(record.withdrawal_amount)))])]):_c('span',[_vm._v(\"-\")])]}},{key:\"alipayInfo\",fn:function(text, record){return [(record)?_c('div',{staticClass:\"alipay-info\"},[_c('div',{staticClass:\"name\"},[_vm._v(_vm._s(record.alipay_name || '-'))]),_c('div',{staticClass:\"account\"},[_vm._v(_vm._s(record.alipay_account || '-'))])]):_c('span',[_vm._v(\"-\")])]}},{key:\"status\",fn:function(text, record){return [(record)?_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record && record.status)}},[_vm._v(\"\\n            \"+_vm._s(_vm.getStatusText(record.status, record.review_remark))+\"\\n          \")]):_c('span',[_vm._v(\"-\")])]}},{key:\"applyTime\",fn:function(text, record){return [_c('span',[_vm._v(_vm._s(record && record.apply_time ? _vm.formatDateTime(record.apply_time) : '-'))])]}},{key:\"reviewTime\",fn:function(text, record){return [_c('span',[_vm._v(_vm._s(record && record.review_time ? _vm.formatDateTime(record.review_time) : '-'))])]}},{key:\"action\",fn:function(text, record){return [(record)?_c('div',{staticClass:\"action-buttons\"},[(record.status === 1)?_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"loading\":record.approving},on:{\"click\":function($event){return _vm.handleApprove(record)}}},[_vm._v(\"\\n              审核通过\\n            \")]):_vm._e(),(record.status === 1)?_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},attrs:{\"type\":\"danger\",\"size\":\"small\",\"loading\":record.rejecting},on:{\"click\":function($event){return _vm.handleReject(record)}}},[_vm._v(\"\\n              审核拒绝\\n            \")]):_vm._e(),_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.handleViewDetail(record)}}},[_vm._v(\"\\n              查看详情\\n            \")])],1):_c('span',[_vm._v(\"-\")])]}}])})],1)],1),_c('a-modal',{attrs:{\"title\":\"审核拒绝\",\"footer\":null,\"width\":\"500px\"},model:{value:(_vm.showRejectModal),callback:function ($$v) {_vm.showRejectModal=$$v},expression:\"showRejectModal\"}},[_c('div',{staticClass:\"reject-modal\"},[_c('a-alert',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"message\":\"请填写拒绝原因\",\"type\":\"warning\",\"show-icon\":\"\"}}),_c('a-form',{attrs:{\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":\"拒绝原因\",\"required\":\"\"}},[_c('a-textarea',{attrs:{\"placeholder\":\"请输入拒绝原因\",\"rows\":4,\"maxLength\":200},model:{value:(_vm.rejectReason),callback:function ($$v) {_vm.rejectReason=$$v},expression:\"rejectReason\"}})],1)],1),_c('div',{staticClass:\"modal-actions\"},[_c('a-button',{on:{\"click\":function($event){_vm.showRejectModal = false}}},[_vm._v(\"\\n          取消\\n        \")]),_c('a-button',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"type\":\"danger\",\"loading\":_vm.rejecting,\"disabled\":!_vm.rejectReason.trim()},on:{\"click\":_vm.confirmReject}},[_vm._v(\"\\n          确认拒绝\\n        \")])],1)],1)]),_c('a-modal',{attrs:{\"title\":\"提现申请详情\",\"footer\":null,\"width\":\"600px\"},model:{value:(_vm.showDetailModal),callback:function ($$v) {_vm.showDetailModal=$$v},expression:\"showDetailModal\"}},[(_vm.currentRecord)?_c('div',{staticClass:\"detail-modal\"},[_c('a-descriptions',{attrs:{\"column\":2,\"bordered\":\"\"}},[_c('a-descriptions-item',{attrs:{\"label\":\"申请ID\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.id)+\"\\n        \")]),_c('a-descriptions-item',{attrs:{\"label\":\"用户名\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.username)+\"\\n        \")]),_c('a-descriptions-item',{attrs:{\"label\":\"提现金额\"}},[_c('span',{staticClass:\"amount-text\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.currentRecord.withdrawal_amount)))])]),_c('a-descriptions-item',{attrs:{\"label\":\"申请状态\"}},[_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(_vm.currentRecord.status)}},[_vm._v(\"\\n            \"+_vm._s(_vm.getStatusText(_vm.currentRecord.status, _vm.currentRecord.review_remark))+\"\\n          \")])],1),_c('a-descriptions-item',{attrs:{\"label\":\"真实姓名\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.alipay_name)+\"\\n        \")]),_c('a-descriptions-item',{attrs:{\"label\":\"支付宝账号\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.alipay_account)+\"\\n        \")]),_c('a-descriptions-item',{attrs:{\"label\":\"申请时间\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.apply_time ? _vm.formatDateTime(_vm.currentRecord.apply_time) : '-')+\"\\n        \")]),_c('a-descriptions-item',{attrs:{\"label\":\"审核时间\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.review_time ? _vm.formatDateTime(_vm.currentRecord.review_time) : '-')+\"\\n        \")]),(_vm.currentRecord.review_by)?_c('a-descriptions-item',{attrs:{\"label\":\"审核人\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.review_by)+\"\\n        \")]):_vm._e(),(_vm.currentRecord.review_remark)?_c('a-descriptions-item',{attrs:{\"label\":\"审核备注\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.currentRecord.review_remark)+\"\\n        \")]):_vm._e()],1)],1):_vm._e()])],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"page-header\"},[_c('h2',[_vm._v(\"提现管理\")]),_c('p',[_vm._v(\"管理用户提现申请，审核通过或拒绝申请\")])])}]\n\nexport { render, staticRenderFns }"]}