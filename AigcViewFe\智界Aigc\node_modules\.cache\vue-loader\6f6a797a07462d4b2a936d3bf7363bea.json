{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=e0dcc94e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('WebsitePage',[_c('div',{staticClass:\"affiliate-container\"},[_c('div',{staticClass:\"simple-header\"},[_c('h1',{staticClass:\"simple-title\"},[_vm._v(\"邀请奖励\")]),_c('p',{staticClass:\"simple-subtitle\"},[_vm._v(\"邀请好友注册智界AIGC，获得丰厚奖励\")]),_c('div',{staticClass:\"commission-badge\"},[_c('span',{staticClass:\"badge-text\"},[_vm._v(\"当前奖励比例：\"+_vm._s(_vm.currentCommissionRate)+\"%\")]),_c('span',{staticClass:\"badge-level\"},[_vm._v(_vm._s(_vm.commissionLevelText))])])]),_c('section',{staticClass:\"affiliate-section\"},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"promotion-link-section\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"您的专属邀请链接\")]),_c('div',{staticClass:\"link-main-container\"},[_c('div',{staticClass:\"link-input-large\"},[_c('a-input',{attrs:{\"value\":_vm.affiliateLink || '正在生成邀请链接...',\"readonly\":\"\",\"loading\":_vm.loading,\"size\":\"large\",\"placeholder\":\"邀请链接生成中...\"}})],1),_c('div',{staticClass:\"link-actions\"},[_c('a-button',{staticClass:\"copy-btn\",attrs:{\"type\":\"primary\",\"size\":\"large\",\"disabled\":!_vm.affiliateLink || _vm.loading},on:{\"click\":_vm.copyLink}},[_c('a-icon',{attrs:{\"type\":\"copy\"}}),_vm._v(\"\\n                复制链接\\n              \")],1),_c('a-button',{staticClass:\"qr-btn\",attrs:{\"size\":\"large\",\"loading\":_vm.qrLoading},on:{\"click\":_vm.generateQRCode}},[_c('a-icon',{attrs:{\"type\":\"qrcode\"}}),_vm._v(\"\\n                邀请二维码\\n              \")],1)],1)]),_c('div',{staticClass:\"link-tips\"},[_c('a-icon',{attrs:{\"type\":\"info-circle\"}}),_vm._v(\"\\n            分享此链接，您将获得好友付费的 \"),_c('strong',[_vm._v(_vm._s(_vm.currentCommissionRate)+\"%\")]),_vm._v(\" 奖励\\n          \")],1)]),_c('div',{staticClass:\"earnings-dashboard\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"收益概览\")]),_c('div',{staticClass:\"earnings-grid\"},[_c('div',{staticClass:\"earning-card primary\"},[_c('div',{staticClass:\"card-icon\"},[_c('a-icon',{attrs:{\"type\":\"dollar\"}})],1),_c('div',{staticClass:\"card-content\"},[_c('a-spin',{attrs:{\"spinning\":_vm.loading,\"size\":\"small\"}},[_c('div',{staticClass:\"earning-number\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.totalEarnings)))]),_c('div',{staticClass:\"earning-label\"},[_vm._v(\"累计收益\")])])],1)]),_c('div',{staticClass:\"earning-card success\"},[_c('div',{staticClass:\"card-icon\"},[_c('a-icon',{attrs:{\"type\":\"wallet\"}})],1),_c('div',{staticClass:\"card-content\"},[_c('a-spin',{attrs:{\"spinning\":_vm.loading,\"size\":\"small\"}},[_c('div',{staticClass:\"earning-number\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.availableEarnings)))]),_c('div',{staticClass:\"earning-label\"},[_vm._v(\"可提现金额\")])]),_c('div',{staticClass:\"card-action\"},[_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"disabled\":_vm.availableEarnings <= 0 || _vm.loading},on:{\"click\":_vm.openWithdrawModal}},[_vm._v(\"\\n                    立即提现\\n                  \")])],1)],1)]),_c('div',{staticClass:\"earning-card info\"},[_c('div',{staticClass:\"card-icon\"},[_c('a-icon',{attrs:{\"type\":\"team\"}})],1),_c('div',{staticClass:\"card-content\"},[_c('a-spin',{attrs:{\"spinning\":_vm.loading,\"size\":\"small\"}},[_c('div',{staticClass:\"earning-number\"},[_vm._v(_vm._s(Math.floor(_vm.totalReferrals)))]),_c('div',{staticClass:\"earning-label\"},[_vm._v(\"邀请注册人数\")])])],1)]),_c('div',{staticClass:\"earning-card warning\"},[_c('div',{staticClass:\"card-icon\"},[_c('a-icon',{attrs:{\"type\":\"crown\"}})],1),_c('div',{staticClass:\"card-content\"},[_c('a-spin',{attrs:{\"spinning\":_vm.loading,\"size\":\"small\"}},[_c('div',{staticClass:\"earning-number\"},[_vm._v(_vm._s(Math.floor(_vm.memberReferrals)))]),_c('div',{staticClass:\"earning-label\"},[_vm._v(\"转化人数\")])])],1)])])]),_c('div',{staticClass:\"commission-progress\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"奖励等级进度\")]),_c('div',{staticClass:\"progress-card\"},[_c('div',{staticClass:\"level-timeline-horizontal\"},_vm._l((_vm.commissionLevels),function(level,index){return _c('div',{key:index,staticClass:\"level-step-horizontal\",class:{\n                  'current': level.isCurrent,\n                  'completed': level.isCompleted,\n                  'upcoming': level.isUpcoming\n                }},[_c('div',{staticClass:\"step-circle-horizontal\"},[(level.isCompleted)?_c('a-icon',{attrs:{\"type\":\"check\"}}):(level.isCurrent)?_c('span',{staticClass:\"current-dot\"}):_c('span',{staticClass:\"step-number\"},[_vm._v(_vm._s(index + 1))])],1),_c('div',{staticClass:\"step-content-horizontal\"},[_c('div',{staticClass:\"step-title\"},[_vm._v(_vm._s(level.name))]),_c('div',{staticClass:\"step-rate\"},[_vm._v(_vm._s(level.rate)+\"%\")]),_c('div',{staticClass:\"step-requirement\"},[_vm._v(_vm._s(level.requirement)+\"人\")]),(level.remaining > 0)?_c('div',{staticClass:\"step-remaining\"},[_vm._v(\"\\n                    还需\"+_vm._s(level.remaining)+\"个\\n                  \")]):(level.isCompleted)?_c('div',{staticClass:\"step-completed\"},[_vm._v(\"\\n                    已达成\\n                  \")]):_vm._e()]),(index < _vm.commissionLevels.length - 1)?_c('div',{staticClass:\"step-line-horizontal\"}):_vm._e()])}),0)])]),_c('div',{staticClass:\"commission-rules\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"奖励规则\")]),_c('div',{staticClass:\"rules-table\"},[_c('div',{staticClass:\"rule-row header\"},[_c('div',{staticClass:\"rule-cell\"},[_vm._v(\"用户等级\")]),_c('div',{staticClass:\"rule-cell\"},[_vm._v(\"邀请人数要求\")]),_c('div',{staticClass:\"rule-cell\"},[_vm._v(\"奖励比例\")]),_c('div',{staticClass:\"rule-cell\"},[_vm._v(\"说明\")])]),_c('a-spin',{attrs:{\"spinning\":_vm.loading,\"size\":\"small\"}},_vm._l((_vm.allLevelConfigs),function(config){return _c('div',{key:config.id,staticClass:\"rule-row\",class:{\n                  'vip': config.role_code === 'VIP',\n                  'svip': config.role_code === 'SVIP'\n                }},[_c('div',{staticClass:\"rule-cell\"},[_vm._v(_vm._s(_vm.getRoleDisplayName(config.role_code)))]),_c('div',{staticClass:\"rule-cell\"},[_vm._v(_vm._s(_vm.getRequirementText(config)))]),_c('div',{staticClass:\"rule-cell highlight\"},[_vm._v(_vm._s(config.commission_rate)+\"%\")]),_c('div',{staticClass:\"rule-cell\"},[_vm._v(_vm._s(config.level_name))])])}),0)],1)]),_c('div',{staticClass:\"referral-users\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"我的邀请用户\")]),_c('div',{staticClass:\"users-table-container\"},[_c('a-table',{attrs:{\"columns\":_vm.userColumns,\"data-source\":_vm.referralUsers,\"loading\":_vm.usersLoading,\"pagination\":_vm.usersPagination,\"size\":\"middle\"},on:{\"change\":_vm.handleUsersTableChange},scopedSlots:_vm._u([{key:\"avatar\",fn:function(text, record){return [_c('a-avatar',{style:({ backgroundColor: '#87d068' }),attrs:{\"src\":_vm.getAvatarUrl(record.avatar)}},[_vm._v(\"\\n                  \"+_vm._s(record.nickname ? record.nickname.charAt(0) : 'U')+\"\\n                \")])]}},{key:\"reward\",fn:function(text){return [_c('span',{staticClass:\"reward-amount\"},[_vm._v(\"¥\"+_vm._s(text || '0.00'))])]}}])})],1)]),_c('div',{staticClass:\"withdraw-records\"},[_c('h2',{staticClass:\"section-title\"},[_vm._v(\"提现记录\")]),_c('div',{staticClass:\"filter-section\",staticStyle:{\"margin-bottom\":\"16px\",\"padding\":\"16px\",\"background\":\"#fafafa\",\"border-radius\":\"6px\"}},[_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"提现金额\"}},[_c('a-input-group',{attrs:{\"compact\":\"\"}},[_c('a-input-number',{staticStyle:{\"width\":\"50%\"},attrs:{\"placeholder\":\"最小金额\",\"min\":0,\"precision\":2},model:{value:(_vm.withdrawFilter.minAmount),callback:function ($$v) {_vm.$set(_vm.withdrawFilter, \"minAmount\", $$v)},expression:\"withdrawFilter.minAmount\"}}),_c('a-input-number',{staticStyle:{\"width\":\"50%\"},attrs:{\"placeholder\":\"最大金额\",\"min\":0,\"precision\":2},model:{value:(_vm.withdrawFilter.maxAmount),callback:function ($$v) {_vm.$set(_vm.withdrawFilter, \"maxAmount\", $$v)},expression:\"withdrawFilter.maxAmount\"}})],1)],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"申请时间\"}},[_c('a-range-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"format\":\"YYYY-MM-DD\",\"placeholder\":\"['开始日期', '结束日期']\"},model:{value:(_vm.withdrawFilter.dateRange),callback:function ($$v) {_vm.$set(_vm.withdrawFilter, \"dateRange\", $$v)},expression:\"withdrawFilter.dateRange\"}})],1)],1),_c('a-col',{attrs:{\"span\":4}},[_c('a-form-item',{attrs:{\"label\":\"状态\"}},[_c('a-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"选择状态\"},model:{value:(_vm.withdrawFilter.status),callback:function ($$v) {_vm.$set(_vm.withdrawFilter, \"status\", $$v)},expression:\"withdrawFilter.status\"}},[_c('a-select-option',{attrs:{\"value\":null}},[_vm._v(\"全部\")]),_c('a-select-option',{attrs:{\"value\":1}},[_vm._v(\"待审核\")]),_c('a-select-option',{attrs:{\"value\":2}},[_vm._v(\"已发放\")]),_c('a-select-option',{attrs:{\"value\":3}},[_vm._v(\"审核拒绝\")]),_c('a-select-option',{attrs:{\"value\":4}},[_vm._v(\"已取消\")])],1)],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"完成时间\"}},[_c('a-range-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"format\":\"YYYY-MM-DD\",\"placeholder\":\"['开始日期', '结束日期']\"},model:{value:(_vm.withdrawFilter.completeDateRange),callback:function ($$v) {_vm.$set(_vm.withdrawFilter, \"completeDateRange\", $$v)},expression:\"withdrawFilter.completeDateRange\"}})],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\" \"}},[_c('a-button',{staticStyle:{\"margin-right\":\"8px\"},attrs:{\"type\":\"primary\",\"loading\":_vm.recordsLoading},on:{\"click\":_vm.handleWithdrawFilter}},[_vm._v(\"\\n                    搜索\\n                  \")]),_c('a-button',{on:{\"click\":_vm.handleWithdrawReset}},[_vm._v(\"重置\")])],1)],1)],1)],1),_c('div',{staticClass:\"records-table-container\"},[_c('a-table',{attrs:{\"columns\":_vm.withdrawColumns,\"data-source\":_vm.withdrawRecords,\"loading\":_vm.recordsLoading,\"pagination\":_vm.withdrawPagination,\"size\":\"middle\"},on:{\"change\":_vm.handleWithdrawTableChange},scopedSlots:_vm._u([{key:\"status\",fn:function(text){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(text)}},[_vm._v(\"\\n                  \"+_vm._s(text)+\"\\n                \")])]}},{key:\"amount\",fn:function(text){return [_c('span',{staticClass:\"withdraw-amount\"},[_vm._v(\"¥\"+_vm._s(text))])]}},{key:\"action\",fn:function(text, record){return [(record.rawStatus === 1)?_c('a-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"loading\":_vm.cancelLoading},on:{\"click\":function($event){return _vm.handleCancelWithdraw(record)}}},[_vm._v(\"\\n                  取消提现\\n                \")]):_c('span',[_vm._v(\"-\")])]}}])})],1)])])]),_c('a-modal',{attrs:{\"title\":\"邀请二维码\",\"footer\":null,\"width\":\"400px\",\"centered\":\"\"},model:{value:(_vm.showQRModal),callback:function ($$v) {_vm.showQRModal=$$v},expression:\"showQRModal\"}},[_c('div',{staticClass:\"qr-modal-content\"},[(_vm.qrCodeUrl)?_c('div',{staticClass:\"qr-code-container\"},[_c('img',{staticClass:\"qr-code-image\",attrs:{\"src\":_vm.qrCodeUrl,\"alt\":\"邀请二维码\"}})]):_vm._e(),_c('div',{staticClass:\"qr-actions\"},[(_vm.qrCodeUrl)?_c('a-button',{attrs:{\"type\":\"primary\",\"block\":\"\"},on:{\"click\":_vm.downloadQRCode}},[_c('a-icon',{attrs:{\"type\":\"download\"}}),_vm._v(\"\\n            下载二维码\\n          \")],1):_vm._e()],1)])]),_c('a-modal',{attrs:{\"title\":\"申请提现\",\"footer\":null,\"width\":\"500px\",\"centered\":\"\"},model:{value:(_vm.showWithdrawModal),callback:function ($$v) {_vm.showWithdrawModal=$$v},expression:\"showWithdrawModal\"}},[_c('div',{staticClass:\"withdraw-modal-content\"},[_c('div',{staticClass:\"withdraw-info\"},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(\"可提现金额：\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.availableEarnings)))])]),_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(\"最低提现金额：\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(\"¥50.00\")])])]),_c('a-form',{attrs:{\"form\":_vm.withdrawForm},on:{\"submit\":_vm.handleWithdraw}},[_c('a-form-item',{attrs:{\"label\":\"提现金额\"}},[_c('a-input-number',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['amount', {\n                rules: [\n                  { required: true, message: '请输入提现金额' },\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\n                  { type: 'number', max: _vm.availableEarnings, message: '提现金额不能超过可提现金额' }\n                ]\n              }]),expression:\"['amount', {\\n                rules: [\\n                  { required: true, message: '请输入提现金额' },\\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\\n                ]\\n              }]\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"min\":50,\"max\":_vm.availableEarnings,\"precision\":2,\"placeholder\":\"请输入提现金额\"}},[_c('template',{slot:\"addonAfter\"},[_vm._v(\"元\")])],2)],1),_c('a-form-item',{attrs:{\"label\":\"提现方式\"}},[_c('a-select',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['method', {\n                rules: [{ required: true, message: '请选择提现方式' }],\n                initialValue: 'alipay'\n              }]),expression:\"['method', {\\n                rules: [{ required: true, message: '请选择提现方式' }],\\n                initialValue: 'alipay'\\n              }]\"}],attrs:{\"placeholder\":\"请选择提现方式\",\"disabled\":\"\"}},[_c('a-select-option',{attrs:{\"value\":\"alipay\"}},[_vm._v(\"支付宝\")])],1)],1),_c('a-form-item',{attrs:{\"label\":\"支付宝手机号\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['alipayAccount', {\n                rules: [\n                  { required: true, message: '请输入支付宝手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                ]\n              }]),expression:\"['alipayAccount', {\\n                rules: [\\n                  { required: true, message: '请输入支付宝手机号' },\\n                  { pattern: /^1[3-9]\\\\d{9}$/, message: '请输入正确的手机号格式' }\\n                ]\\n              }]\"}],attrs:{\"placeholder\":\"请输入支付宝手机号\"}})],1),_c('a-form-item',{attrs:{\"label\":\"收款人真实姓名\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['realName', {\n                rules: [\n                  { required: true, message: '请输入收款人真实姓名' },\n                  { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                ]\n              }]),expression:\"['realName', {\\n                rules: [\\n                  { required: true, message: '请输入收款人真实姓名' },\\n                  { pattern: /^[\\\\u4e00-\\\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\\n                ]\\n              }]\"}],attrs:{\"placeholder\":\"请输入收款人真实姓名\"}})],1)],1),_c('div',{staticClass:\"withdraw-actions\"},[_c('a-button',{staticStyle:{\"margin-right\":\"8px\"},on:{\"click\":function($event){_vm.showWithdrawModal = false}}},[_vm._v(\"\\n            取消\\n          \")]),_c('a-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.withdrawLoading},on:{\"click\":_vm.handleWithdraw}},[_vm._v(\"\\n            申请提现\\n          \")])],1)],1)])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}