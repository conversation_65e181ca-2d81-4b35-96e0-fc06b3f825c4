{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { login, getCaptcha, phoneLogin, emailLogin } from '@/api/login'\nimport { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'\nimport { getAction } from '@/api/manage'\nimport { gsap } from 'gsap'\nimport WebsiteHeader from '@/components/website/WebsiteHeader.vue'\nimport LogoImage from '@/components/common/LogoImage.vue'\nimport { ACCESS_TOKEN, USER_NAME, USER_INFO, UI_CACHE_DB_DICT_DATA } from '@/store/mutation-types'\nimport {\n  checkUsername,\n  sendSmsCode,\n  sendEmailCode,\n  register,\n  generateWechatQrCode\n} from '@/api/register'\nimport Vue from 'vue'\nimport { handleLoginConflict } from '@/utils/loginConflictHandler'\n\nexport default {\n  name: 'WebsiteLogin',\n  components: {\n    WebsiteHeader,\n    LogoImage\n  },\n  data() {\n    return {\n      // 登录相关\n      form: this.$form.createForm(this),\n      phoneLoginForm: this.$form.createForm(this),\n      emailLoginForm: this.$form.createForm(this),\n      loginLoading: false,\n      phoneLoginLoading: false,\n      emailLoginLoading: false,\n      rememberMe: false,\n      randCodeImage: '',\n      currdatetime: new Date().getTime(),\n      encryptedString: '',\n\n      // 登录方式切换\n      loginType: 'phone', // password, phone, email, wechat - 默认手机号登录\n\n      // 验证码相关\n      smsCodeSending: false,\n      smsCountdown: 0,\n      emailCodeSending: false,\n      emailCountdown: 0,\n\n      // 邀请码（静默处理）\n      inviteCodeFromUrl: '',\n\n      // 微信登录\n      wechatLoginQrCode: '',\n\n      features: [\n        {\n          icon: 'robot',\n          title: 'AI智能创作',\n          description: '强大的AI算法，助您快速生成高质量内容'\n        },\n        {\n          icon: 'thunderbolt',\n          title: '极速响应',\n          description: '毫秒级响应速度，让创作灵感不再等待'\n        },\n        {\n          icon: 'safety-certificate',\n          title: '安全可靠',\n          description: '企业级安全保障，保护您的创作成果'\n        },\n        {\n          icon: 'global',\n          title: '全球服务',\n          description: '覆盖全球的CDN网络，随时随地畅享服务'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.getEncrypte()\n    this.handleChangeCheckCode()\n    this.initAnimations()\n    this.checkInviteCode()\n  },\n  methods: {\n    // 获取密码加密规则\n    getEncrypte() {\n      getEncryptedString().then((data) => {\n        this.encryptedString = data\n      })\n    },\n\n    // 刷新验证码\n    handleChangeCheckCode() {\n      this.currdatetime = new Date().getTime()\n      getAction(`/sys/randomImage/${this.currdatetime}`).then(res => {\n        if (res.success) {\n          this.randCodeImage = res.result\n        } else {\n          this.$message.error(res.message)\n        }\n      }).catch(() => {\n        this.$message.error('验证码加载失败')\n      })\n    },\n\n    handleSubmit(e) {\n      e.preventDefault()\n      this.form.validateFields((err, values) => {\n        if (!err) {\n          this.loginLoading = true\n          console.log('官网登录信息:', values)\n\n          // 使用真实的登录API\n          let user = encryption(values.username, this.encryptedString.key, this.encryptedString.iv)\n          let pwd = encryption(values.password, this.encryptedString.key, this.encryptedString.iv)\n          let loginParams = {\n            username: user,\n            password: pwd,\n            captcha: values.inputCode,\n            checkKey: this.currdatetime,\n            remember_me: this.rememberMe,\n            loginType: 'website' // 标识为官网用户登录\n          }\n\n          console.log(\"官网登录参数\", loginParams)\n          login(loginParams).then(async (res) => {\n            this.loginLoading = false\n            console.log(\"🔍 登录响应:\", res)\n            console.log(\"🔍 响应code:\", res.code, \"类型:\", typeof res.code)\n            if (res.code === 200 || res.code === '200') {\n              this.$notification.success({\n                message: '登录成功',\n                description: '欢迎回来！正在跳转到个人中心...',\n                placement: 'topRight',\n                duration: 3,\n                style: {\n                  width: '350px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n\n              // ✅ 存储登录信息\n              const result = res.result\n              const userInfo = result.userInfo\n              Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n              Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n              // ✅ 获取用户角色信息\n              try {\n                const roleRes = await getAction(\"/sys/user/getCurrentUserDeparts\")\n                if (roleRes.success) {\n                  const userRole = roleRes.result.role\n                  const departId = roleRes.result.departId\n\n                  // 存储角色信息\n                  localStorage.setItem('userRole', userRole || '')\n                  localStorage.setItem('departId', departId || '')\n\n                  // 优先处理重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  console.log('🔍 登录成功，检查重定向参数:', redirectPath)\n\n                  if (redirectPath) {\n                    // 有重定向参数，直接跳转到目标页面\n                    console.log('🔄 有重定向参数，跳转到:', redirectPath)\n                    this.$router.push(redirectPath)\n                  } else {\n                    // 没有重定向参数，根据角色决定跳转\n                    if (this.isAdminRole(userRole)) {\n                      // 管理员用户，跳转到后台\n                      console.log('🔄 管理员用户，跳转到后台管理')\n                      this.$router.push('/dashboard/analysis')\n                    } else {\n                      // 普通用户，跳转到个人中心\n                      console.log('🔄 普通用户，跳转到个人中心')\n                      this.$router.push('/usercenter')\n                    }\n                  }\n                } else {\n                  // 获取角色失败，检查重定向参数\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                }\n              } catch (error) {\n                console.error('获取角色信息失败:', error)\n                // 出错时也检查重定向参数\n                const redirectPath = this.$route.query.redirect\n                if (redirectPath) {\n                  this.$router.push(redirectPath)\n                } else {\n                  this.$router.push('/usercenter')\n                }\n              }\n            } else {\n              this.$notification.error({\n                message: '登录失败',\n                description: res.message || '用户名或密码错误，请检查后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          }).catch(async (err) => {\n            this.loginLoading = false\n\n            // 检查是否是登录冲突错误\n            if (err.response && err.response.data && err.response.data.code === 4002) {\n              console.log('检测到用户名密码登录冲突，显示确认弹窗')\n              const conflictInfo = err.response.data.result\n\n              // 创建强制登录函数\n              const forceLoginFn = async () => {\n                const forceLoginParams = {\n                  ...loginParams,\n                  loginType: 'force' // 修改登录类型为强制登录\n                }\n                console.log('用户名密码强制登录数据:', forceLoginParams)\n                return await login(forceLoginParams)\n              }\n\n              try {\n                // 显示登录冲突确认弹窗\n                const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n                if (forceLoginResponse && (forceLoginResponse.code === 200 || forceLoginResponse.code === '200')) {\n                  // 强制登录成功，执行登录成功的逻辑\n                  this.$notification.success({\n                    message: '登录成功',\n                    description: '欢迎回来！正在跳转到个人中心...',\n                    placement: 'topRight',\n                    duration: 3\n                  })\n\n                  // 存储登录信息\n                  const result = forceLoginResponse.result\n                  const userInfo = result.userInfo\n                  Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n                  Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n                  // 跳转逻辑\n                  const redirectPath = this.$route.query.redirect\n                  if (redirectPath) {\n                    this.$router.push(redirectPath)\n                  } else {\n                    this.$router.push('/usercenter')\n                  }\n                } else {\n                  throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n                }\n              } catch (conflictError) {\n                if (conflictError.message === 'USER_CANCELLED') {\n                  // 用户取消登录\n                  console.log('用户取消用户名密码强制登录')\n                  this.handleChangeCheckCode() // 刷新验证码\n                  return\n                } else {\n                  this.$notification.error({\n                    message: '登录失败',\n                    description: conflictError.message || '强制登录失败',\n                    placement: 'topRight',\n                    duration: 4\n                  })\n                  this.handleChangeCheckCode() // 刷新验证码\n                }\n              }\n            } else {\n              // 其他错误，显示原有的错误处理\n              this.$notification.error({\n                message: '登录失败',\n                description: err.message || '网络连接异常，请检查网络后重试',\n                placement: 'topRight',\n                duration: 4,\n                style: {\n                  width: '380px',\n                  marginTop: '101px',\n                  borderRadius: '8px',\n                  boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n                }\n              })\n              this.handleChangeCheckCode() // 刷新验证码\n            }\n          })\n        }\n      })\n    },\n\n    handleForgotPassword() {\n      this.$notification.info({\n        message: '忘记密码',\n        description: '忘记密码功能正在开发中，敬请期待...',\n        placement: 'topRight',\n        duration: 3,\n        style: {\n          width: '350px',\n          marginTop: '101px',\n          borderRadius: '8px',\n          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n        }\n      })\n      // TODO: 跳转到忘记密码页面\n    },\n\n    handleSocialLogin(type) {\n      const typeMap = {\n        wechat: '微信',\n        qq: 'QQ',\n        alipay: '支付宝'\n      }\n      this.$message.info(`${typeMap[type]}登录功能开发中...`)\n      // TODO: 实现第三方登录\n    },\n\n\n\n    // 检查URL中的邀请码（静默处理）\n    checkInviteCode() {\n      // 支持两种参数格式：ref（推广链接）和 invite（邀请码）\n      const refCode = this.$route.query.ref\n      const inviteCode = this.$route.query.invite\n      const finalInviteCode = refCode || inviteCode\n      \n      if (finalInviteCode) {\n        this.inviteCodeFromUrl = finalInviteCode\n        // 静默记录邀请码，不显示给用户\n        console.log('检测到推荐码:', finalInviteCode, '来源:', refCode ? 'ref参数' : 'invite参数')\n      }\n    },\n\n    // 登录方式切换\n    switchLoginType(type) {\n      this.loginType = type\n\n      // 重置验证码倒计时\n      this.smsCountdown = 0\n      this.emailCountdown = 0\n\n      if (type === 'wechat') {\n        this.generateWechatLoginQrCode()\n      }\n    },\n\n    // 手机号登录（自动注册）\n    handlePhoneLogin(e) {\n      e.preventDefault()\n      this.phoneLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.phoneLoginLoading = true\n\n          try {\n            // 先检查手机号是否已注册\n            const checkResponse = await checkUsername(values.phone, 'phone')\n\n            if (checkResponse.success) {\n              // 手机号未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('phone', values)\n            } else {\n              // 手机号已注册，直接登录\n              await this.loginWithSmsCode(values)\n            }\n          } catch (error) {\n            this.phoneLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 邮箱登录（自动注册）\n    handleEmailLogin(e) {\n      e.preventDefault()\n      this.emailLoginForm.validateFields(async (err, values) => {\n        if (!err) {\n          this.emailLoginLoading = true\n\n          try {\n            // 先检查邮箱是否已注册\n            const checkResponse = await checkUsername(values.email, 'email')\n\n            if (checkResponse.success) {\n              // 邮箱未注册，自动注册（无密码账户）\n              await this.autoRegisterAndLogin('email', values)\n            } else {\n              // 邮箱已注册，直接登录\n              await this.loginWithEmailCode(values)\n            }\n          } catch (error) {\n            this.emailLoginLoading = false\n            this.$notification.error({\n              message: '登录失败',\n              description: error.message || '登录过程中发生错误',\n              placement: 'topRight',\n              duration: 4\n            })\n          }\n        }\n      })\n    },\n\n    // 自动注册并登录（无密码账户）\n    async autoRegisterAndLogin(type, values) {\n      try {\n        // 为无密码账户生成符合要求的随机密码\n        const randomPassword = this.generateSecurePassword()\n\n        // 构建注册数据\n        const registerData = {\n          type: type,\n          [type]: values[type], // phone 或 email\n          verifyCode: values[type === 'phone' ? 'smsCode' : 'emailCode'],\n          // 生成随机密码（用户不需要知道）\n          password: randomPassword,\n          confirmPassword: randomPassword,\n          inviteCode: this.inviteCodeFromUrl, // 静默携带邀请码\n          inviteSource: this.inviteCodeFromUrl ? 'link' : null\n        }\n\n        console.log('自动注册数据:', registerData)\n\n        // 调用注册接口\n        const registerResponse = await register(registerData)\n\n        if (registerResponse.success) {\n          // 注册成功，现在需要自动登录获取token\n          console.log('注册成功，用户ID:', registerResponse.result)\n\n          // 使用生成的密码进行自动登录\n          await this.performAutoLogin(type, values, randomPassword)\n        } else {\n          throw new Error(registerResponse.message || '注册失败')\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 使用短信验证码登录\n    async loginWithSmsCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          mobile: values.phone,\n          captcha: values.smsCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('短信验证码登录:', loginData)\n\n        // 调用短信验证码登录接口\n        const loginResponse = await phoneLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到手机号登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('手机号强制登录数据:', forceLoginData)\n              return await phoneLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消手机号强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.phoneLoginLoading = false\n      }\n    },\n\n    // 使用邮箱验证码登录\n    async loginWithEmailCode(values) {\n      try {\n        // 构建登录数据\n        const loginData = {\n          email: values.email,\n          emailCode: values.emailCode,\n          loginType: 'website' // 标识为官网用户登录\n        }\n\n        console.log('邮箱验证码登录:', loginData)\n\n        // 调用邮箱验证码登录接口\n        const loginResponse = await emailLogin(loginData)\n\n        if (loginResponse.success) {\n          // 登录成功，处理token和用户信息\n          await this.handleLoginSuccess(loginResponse.result)\n        } else {\n          // 检查是否是登录冲突错误\n          if (loginResponse.code === 4002) {\n            console.log('检测到邮箱登录冲突，显示确认弹窗')\n            const conflictInfo = loginResponse.result\n\n            // 创建强制登录函数\n            const forceLoginFn = async () => {\n              const forceLoginData = {\n                ...loginData,\n                loginType: 'force' // 修改登录类型为强制登录\n              }\n              console.log('邮箱强制登录数据:', forceLoginData)\n              return await emailLogin(forceLoginData)\n            }\n\n            try {\n              // 显示登录冲突确认弹窗\n              const forceLoginResponse = await handleLoginConflict(conflictInfo, forceLoginFn)\n\n              if (forceLoginResponse && forceLoginResponse.success) {\n                // 强制登录成功\n                await this.handleLoginSuccess(forceLoginResponse.result)\n              } else {\n                throw new Error((forceLoginResponse && forceLoginResponse.message) || '强制登录失败')\n              }\n            } catch (conflictError) {\n              if (conflictError.message === 'USER_CANCELLED') {\n                // 用户取消登录\n                console.log('用户取消邮箱强制登录')\n                return\n              } else {\n                throw conflictError\n              }\n            }\n          } else {\n            throw new Error(loginResponse.message || '登录失败')\n          }\n        }\n      } catch (error) {\n        throw error\n      } finally {\n        this.emailLoginLoading = false\n      }\n    },\n\n    // 处理登录成功\n    async handleLoginSuccess(result) {\n      try {\n        // 存储token和用户信息\n        Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_NAME, result.userInfo.username, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(USER_INFO, result.userInfo, 7 * 24 * 60 * 60 * 1000)\n        Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n        // 显示登录成功消息\n        this.$notification.success({\n          message: '登录成功',\n          description: `欢迎回来，${result.userInfo.realname || result.userInfo.username}！`,\n          placement: 'topRight',\n          duration: 3\n        })\n\n        // 跳转到目标页面\n        const redirect = this.$route.query.redirect || '/'\n        this.$router.push(redirect)\n      } catch (error) {\n        console.error('处理登录成功失败:', error)\n        throw new Error('登录后处理失败')\n      }\n    },\n\n    // 发送登录短信验证码\n    async sendLoginSmsCode() {\n      const phone = this.phoneLoginForm.getFieldValue('phone')\n      if (!phone) {\n        this.$message.error('请先输入手机号')\n        return\n      }\n\n      if (!/^1[3-9]\\d{9}$/.test(phone)) {\n        this.$message.error('手机号格式不正确')\n        return\n      }\n\n      this.smsCodeSending = true\n      try {\n        const response = await sendSmsCode(phone, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收短信')\n          this.startSmsCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送短信验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.smsCodeSending = false\n      }\n    },\n\n    // 发送登录邮箱验证码\n    async sendLoginEmailCode() {\n      const email = this.emailLoginForm.getFieldValue('email')\n      if (!email) {\n        this.$message.error('请先输入邮箱')\n        return\n      }\n\n      if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n        this.$message.error('邮箱格式不正确')\n        return\n      }\n\n      this.emailCodeSending = true\n      try {\n        const response = await sendEmailCode(email, 'register')\n\n        if (response.success) {\n          this.$message.success('验证码发送成功，请查收邮件')\n          this.startEmailCountdown()\n        } else {\n          this.$message.error(response.message || '验证码发送失败')\n        }\n      } catch (error) {\n        console.error('发送邮箱验证码失败:', error)\n        this.$message.error('验证码发送失败，请稍后重试')\n      } finally {\n        this.emailCodeSending = false\n      }\n    },\n\n    // 短信验证码倒计时\n    startSmsCountdown() {\n      this.smsCountdown = 60\n      const timer = setInterval(() => {\n        this.smsCountdown--\n        if (this.smsCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 邮箱验证码倒计时\n    startEmailCountdown() {\n      this.emailCountdown = 60\n      const timer = setInterval(() => {\n        this.emailCountdown--\n        if (this.emailCountdown <= 0) {\n          clearInterval(timer)\n        }\n      }, 1000)\n    },\n\n    // 生成微信登录二维码\n    async generateWechatLoginQrCode() {\n      try {\n        const response = await generateWechatQrCode('login', this.inviteCodeFromUrl)\n        if (response.success) {\n          this.wechatLoginQrCode = response.result.qrCodeUrl\n        } else {\n          this.$message.error('生成微信二维码失败')\n        }\n      } catch (error) {\n        console.error('生成微信二维码失败:', error)\n        this.$message.error('生成微信二维码失败')\n      }\n    },\n\n    // 生成符合要求的安全密码（至少8位，包含字母和数字）\n    generateSecurePassword() {\n      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'\n      const numbers = '0123456789'\n      const allChars = letters + numbers\n\n      let password = ''\n\n      // 确保至少包含一个字母和一个数字\n      password += letters.charAt(Math.floor(Math.random() * letters.length))\n      password += numbers.charAt(Math.floor(Math.random() * numbers.length))\n\n      // 生成剩余的6位字符\n      for (let i = 0; i < 10; i++) {\n        password += allChars.charAt(Math.floor(Math.random() * allChars.length))\n      }\n\n      // 打乱字符顺序\n      return password.split('').sort(() => Math.random() - 0.5).join('')\n    },\n\n    // 注册成功后自动登录\n    async performAutoLogin(type, values, password) {\n      try {\n        // 先获取验证码图片\n        this.handleChangeCheckCode()\n\n        // 构建登录参数 - 完全按照正常登录的格式\n        const username = values[type] // phone 或 email\n        const user = encryption(username, this.encryptedString.key, this.encryptedString.iv)\n        const pwd = encryption(password, this.encryptedString.key, this.encryptedString.iv)\n\n        const loginParams = {\n          username: user,\n          password: pwd,\n          captcha: 'AUTO_LOGIN_2025', // 使用特殊验证码绕过验证\n          checkKey: this.currdatetime,\n          remember_me: true,\n          loginType: 'website'\n        }\n\n        console.log('自动登录参数:', { username, loginType: 'auto', checkKey: this.currdatetime })\n\n        const loginResponse = await login(loginParams)\n\n        if (loginResponse.code === 200 || loginResponse.code === '200') {\n          // 登录成功提示\n          this.$notification.success({\n            message: '欢迎加入智界AIGC！',\n            description: `您已成功注册并登录，账户已创建为无密码模式，今后可直接使用${type === 'phone' ? '手机号' : '邮箱'}验证码登录！`,\n            placement: 'topRight',\n            duration: 6\n          })\n\n          // 存储登录信息\n          const result = loginResponse.result\n          const userInfo = result.userInfo\n          Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)\n          Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)\n\n          // 延迟跳转\n          setTimeout(() => {\n            this.$router.push('/usercenter')\n          }, 1500)\n        } else {\n          throw new Error(loginResponse.message || '自动登录失败')\n        }\n      } catch (error) {\n        console.error('自动登录失败:', error)\n        this.$notification.error({\n          message: '注册成功，但自动登录失败',\n          description: '请手动使用验证码登录',\n          placement: 'topRight',\n          duration: 4\n        })\n      }\n    },\n\n\n\n\n\n\n\n\n\n    // 初始化页面动画\n    initAnimations() {\n      // ✅ 创建主时间线，确保动画流畅连贯\n      const tl = gsap.timeline()\n\n      // ✅ 左侧信息区域动画 - 从初始状态开始\n      tl.to(this.$refs.loginInfo, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      })\n\n      // ✅ 右侧登录表单动画 - 与左侧稍微重叠\n      tl.to(this.$refs.loginContainer, {\n        duration: 0.8,\n        x: 0,\n        opacity: 1,\n        ease: \"power3.out\"\n      }, \"-=0.6\") // 提前0.6秒开始，创造重叠效果\n\n      // ✅ 特性列表依次出现 - 更流畅的时序\n      tl.to(\".feature-item\", {\n        duration: 0.5,\n        y: 0,\n        opacity: 1,\n        stagger: 0.08, // 减少间隔，更流畅\n        ease: \"power2.out\"\n      }, \"-=0.4\") // 与右侧动画重叠\n    }\n  }\n}\n", null]}