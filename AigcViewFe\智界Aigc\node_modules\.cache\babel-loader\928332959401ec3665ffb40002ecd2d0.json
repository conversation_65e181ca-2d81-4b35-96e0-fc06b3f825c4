{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\config\\router.config.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\config\\router.config.js", "mtime": 1753756534833}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts';\n/**\n * 走菜单，走权限控制\n * @type {[null,null]}\n */\n\nexport var asyncRouterMap = [// ✅ 移除所有静态后台路由定义，完全由动态路由处理\n  // // dashboard\n  // {\n  //   path: '/dashboard',\n  //   name: 'dashboard',\n  //   redirect: '/dashboard/workplace',\n  //   component: RouteView,\n  //   meta: { title: '仪表盘', icon: 'dashboard', permission: [ 'dashboard' ] },\n  //   children: [\n  //     {\n  //       path: '/dashboard/analysis',\n  //       name: 'Analysis',\n  //       component: () => import('@/views/dashboard/Analysis'),\n  //       meta: { title: '分析页', permission: [ 'dashboard' ] }\n  //     },\n  //     {\n  //       path: '/dashboard/monitor',\n  //       name: 'Monitor',\n  //       hidden: true,\n  //       component: () => import('@/views/dashboard/Monitor'),\n  //       meta: { title: '监控页', permission: [ 'dashboard' ] }\n  //     },\n  //     {\n  //       path: '/dashboard/workplace',\n  //       name: 'Workplace',\n  //       component: () => import('@/views/dashboard/Workplace'),\n  //       meta: { title: '工作台', permission: [ 'dashboard' ] }\n  //     }\n  //   ]\n  // },\n  //\n  // // forms\n  // {\n  //   path: '/form',\n  //   redirect: '/form/basic-form',\n  //   component: PageView,\n  //   meta: { title: '表单页', icon: 'form', permission: [ 'form' ] },\n  //   children: [\n  //     {\n  //       path: '/form/base-form',\n  //       name: 'BaseForm',\n  //       component: () => import('@/views/form/BasicForm'),\n  //       meta: { title: '基础表单', permission: [ 'form' ] }\n  //     },\n  //     {\n  //       path: '/form/step-form',\n  //       name: 'StepForm',\n  //       component: () => import('@/views/form/stepForm/StepForm'),\n  //       meta: { title: '分步表单', permission: [ 'form' ] }\n  //     },\n  //     {\n  //       path: '/form/advanced-form',\n  //       name: 'AdvanceForm',\n  //       component: () => import('@/views/form/advancedForm/AdvancedForm'),\n  //       meta: { title: '高级表单', permission: [ 'form' ] }\n  //     }\n  //   ]\n  // },\n  //\n  // // list\n  // {\n  //   path: '/list',\n  //   name: 'list',\n  //   component: PageView,\n  //   redirect: '/list/query-list',\n  //   meta: { title: '列表页', icon: 'table', permission: [ 'table' ] },\n  //   children: [\n  //     {\n  //       path: '/list/query-list',\n  //       name: 'QueryList',\n  //       component: () => import('@/views/list/TableList'),\n  //       meta: { title: '查询表格', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/edit-table',\n  //       name: 'EditList',\n  //       component: () => import('@/views/list/TableInnerEditList'),\n  //       meta: { title: '内联编辑表格', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/user-list',\n  //       name: 'UserList',\n  //       component: () => import('@/views/list/UserList'),\n  //       meta: { title: '用户列表', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/role-list',\n  //       name: 'RoleList',\n  //       component: () => import('@/views/list/RoleList'),\n  //       meta: { title: '角色列表', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/permission-list',\n  //       name: 'PermissionList',\n  //       component: () => import('@/views/list/PermissionList'),\n  //       meta: { title: '权限列表', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/basic-list',\n  //       name: 'BasicList',\n  //       component: () => import('@/views/list/StandardList'),\n  //       meta: { title: '标准列表', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/card',\n  //       name: 'CardList',\n  //       component: () => import('@/views/list/CardList'),\n  //       meta: { title: '卡片列表', permission: [ 'table' ] }\n  //     },\n  //     {\n  //       path: '/list/search',\n  //       name: 'SearchList',\n  //       component: () => import('@/views/list/search/SearchLayout'),\n  //       redirect: '/list/search/article',\n  //       meta: { title: '搜索列表', permission: [ 'table' ] },\n  //       children: [\n  //         {\n  //           path: '/list/search/article',\n  //           name: 'SearchArticles',\n  //           component: () => import('../views/list/TableList'),\n  //           meta: { title: '搜索列表（文章）', permission: [ 'table' ] }\n  //         },\n  //         {\n  //           path: '/list/search/project',\n  //           name: 'SearchProjects',\n  //           component: () => import('../views/list/TableList'),\n  //           meta: { title: '搜索列表（项目）', permission: [ 'table' ] }\n  //         },\n  //         {\n  //           path: '/list/search/application',\n  //           name: 'SearchApplications',\n  //           component: () => import('../views/list/TableList'),\n  //           meta: { title: '搜索列表（应用）', permission: [ 'table' ] }\n  //         },\n  //       ]\n  //     },\n  //   ]\n  // },\n  //\n  // // profile\n  // {\n  //   path: '/profile',\n  //   name: 'profile',\n  //   component: RouteView,\n  //   redirect: '/profile/basic',\n  //   meta: { title: '详情页', icon: 'profile', permission: [ 'profile' ] },\n  //   children: [\n  //     {\n  //       path: '/profile/basic',\n  //       name: 'ProfileBasic',\n  //       component: () => import('@/views/profile/basic/Index'),\n  //       meta: { title: '基础详情页', permission: [ 'profile' ] }\n  //     },\n  //     {\n  //       path: '/profile/advanced',\n  //       name: 'ProfileAdvanced',\n  //       component: () => import('@/views/profile/advanced/Advanced'),\n  //       meta: { title: '高级详情页', permission: [ 'profile' ] }\n  //     }\n  //   ]\n  // },\n  //\n  // // result\n  // {\n  //   path: '/result',\n  //   name: 'result',\n  //   component: PageView,\n  //   redirect: '/result/success',\n  //   meta: { title: '结果页', icon: 'check-circle-o', permission: [ 'result' ] },\n  //   children: [\n  //     {\n  //       path: '/result/success',\n  //       name: 'ResultSuccess',\n  //       component: () => import(/* webpackChunkName: \"result\" */ '@/views/result/Success'),\n  //       meta: { title: '成功', hiddenHeaderContent: true, permission: [ 'result' ] }\n  //     },\n  //     {\n  //       path: '/result/fail',\n  //       name: 'ResultFail',\n  //       component: () => import(/* webpackChunkName: \"result\" */ '@/views/result/Error'),\n  //       meta: { title: '失败', hiddenHeaderContent: true, permission: [ 'result' ] }\n  //     }\n  //   ]\n  // },\n  //\n  // // Exception\n  // {\n  //   path: '/exception',\n  //   name: 'exception',\n  //   component: RouteView,\n  //   redirect: '/exception/403',\n  //   meta: { title: '异常页', icon: 'warning', permission: [ 'exception' ] },\n  //   children: [\n  //     {\n  //       path: '/exception/403',\n  //       name: 'Exception403',\n  //       component: () => import(/* webpackChunkName: \"fail\" */ '@/views/exception/403'),\n  //       meta: { title: '403', permission: [ 'exception' ] }\n  //     },\n  //     {\n  //       path: '/exception/404',\n  //       name: 'Exception404',\n  //       component: () => import(/* webpackChunkName: \"fail\" */ '@/views/exception/404'),\n  //       meta: { title: '404', permission: [ 'exception' ] }\n  //     },\n  //     {\n  //       path: '/exception/500',\n  //       name: 'Exception500',\n  //       component: () => import(/* webpackChunkName: \"fail\" */ '@/views/exception/500'),\n  //       meta: { title: '500', permission: [ 'exception' ] }\n  //     }\n  //   ]\n  // },\n  //\n  // // account\n  // {\n  //   path: '/account',\n  //   component: RouteView,\n  //   name: 'account',\n  //   meta: { title: '个人页', icon: 'user', keepAlive: true, permission: [ 'user' ] },\n  //   children: [\n  //     {\n  //       path: '/account/center',\n  //       name: 'center',\n  //       component: () => import('@/views/account/center/Index'),\n  //       meta: { title: '个人中心', keepAlive: true, permission: [ 'user' ] }\n  //     },\n  //     {\n  //       path: '/account/settings',\n  //       name: 'settings',\n  //       component: () => import('@/views/account/settings/Index'),\n  //       meta: { title: '个人设置', hideHeader: true, keepAlive: true, permission: [ 'user' ]  },\n  //       redirect: '/account/settings/base',\n  //       alwaysShow: true,\n  //       children: [\n  //         {\n  //           path: '/account/settings/base',\n  //           name: 'BaseSettings',\n  //           component: () => import('@/views/account/settings/BaseSetting'),\n  //           meta: { title: '基本设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }\n  //         },\n  //         {\n  //           path: '/account/settings/security',\n  //           name: 'SecuritySettings',\n  //           component: () => import('@/views/account/settings/Security'),\n  //           meta: { title: '安全设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }\n  //         },\n  //         {\n  //           path: '/account/settings/custom',\n  //           name: 'CustomSettings',\n  //           component: () => import('@/views/account/settings/Custom'),\n  //           meta: { title: '个性化设置', hidden: true, keepAlive: true, permission: [ 'user' ]  }\n  //         },\n  //         {\n  //           path: '/account/settings/binding',\n  //           name: 'BindingSettings',\n  //           component: () => import('@/views/account/settings/Binding'),\n  //           meta: { title: '账户绑定', hidden: true, keepAlive: true, permission: [ 'user' ]  }\n  //         },\n  //         {\n  //           path: '/account/settings/notification',\n  //           name: 'NotificationSettings',\n  //           component: () => import('@/views/account/settings/Notification'),\n  //           meta: { title: '新消息通知', hidden: true, keepAlive: true, permission: [ 'user' ]  }\n  //         },\n  //       ]\n  //     },\n];\n/**\n * 基础路由\n * @type { *[] }\n */\n\nexport var constantRouterMap = [{\n  path: '/user',\n  component: UserLayout,\n  redirect: '/user/login',\n  hidden: true,\n  children: [{\n    path: 'login',\n    name: 'login',\n    component: function component() {\n      return import(\n      /* webpackChunkName: \"user\" */\n      '@/views/user/Login');\n    }\n  }, {\n    path: 'register',\n    name: 'register',\n    component: function component() {\n      return import(\n      /* webpackChunkName: \"user\" */\n      '@/views/user/register/Register');\n    }\n  }, {\n    path: 'register-result',\n    name: 'registerResult',\n    component: function component() {\n      return import(\n      /* webpackChunkName: \"user\" */\n      '@/views/user/register/RegisterResult');\n    }\n  }, {\n    path: 'alteration',\n    name: 'alteration',\n    component: function component() {\n      return import(\n      /* webpackChunkName: \"user\" */\n      '@/views/user/alteration/Alteration');\n    }\n  }]\n}, // {\n//   path: '/',\n//   name: 'index',\n//   component: TabLayout,\n//   meta: {title: '首页'},\n//   redirect: '/dashboard/workplace',\n//   children: [\n//     {\n//       path: '/online',\n//       name: 'online',\n//       redirect: '/online',\n//       component: RouteView,\n//       meta: {title: '在线开发', icon: 'dashboard', permission: ['dashboard']},\n//       children: [\n//         {\n//           path: '/online/auto/:code',\n//           name: 'report',\n//           component: () => import('@/views/modules/online/cgreport/OnlCgreportAutoList')\n//         },\n//       ]\n//     },\n//   ]\n// },\n{\n  // OAuth2 APP页面路由\n  path: '/oauth2-app',\n  component: BlankLayout,\n  redirect: '/oauth2-app/login',\n  children: [{\n    // OAuth2 登录路由\n    path: 'login',\n    name: 'login',\n    component: function component() {\n      return import(\n      /* webpackChunkName: \"oauth2-app.login\" */\n      '@/views/user/oauth2/OAuth2Login');\n    }\n  }]\n}, {\n  path: '/test',\n  component: BlankLayout,\n  redirect: '/test/home',\n  children: [{\n    path: 'home',\n    name: 'TestHome',\n    component: function component() {\n      return import('@/views/Home');\n    }\n  }]\n}, // ✅ 官网页面使用简洁的一级路径\n{\n  path: '/home',\n  name: 'WebsiteHome',\n  component: function component() {\n    return import('@/views/website/home/<USER>');\n  },\n  meta: {\n    title: '智界AIGC - AI内容生成平台',\n    description: '基于前沿人工智能技术，为企业和个人提供智能内容生成解决方案'\n  }\n}, {\n  path: '/market',\n  name: 'WebsiteMarket',\n  component: function component() {\n    return import('@/views/website/market/Market.vue');\n  },\n  meta: {\n    title: '插件中心 - 智界AIGC',\n    description: '发现和购买优质AI插件，提升您的创作效率',\n    keepAlive: true,\n    // 启用页面缓存\n    componentName: 'Market' // 组件名称，用于缓存管理\n\n  }\n}, {\n  path: '/market/plugin/:id',\n  name: 'PluginDetail',\n  component: function component() {\n    return import('@/views/website/market/PluginDetail.vue');\n  },\n  meta: {\n    title: '插件详情 - 智界AIGC',\n    description: '查看插件详细信息、使用教程和技术说明'\n  }\n}, {\n  path: '/cases',\n  name: 'WebsiteCases',\n  component: function component() {\n    return import('@/views/website/cases/Cases.vue');\n  },\n  meta: {\n    title: '客户案例 - 智界AIGC',\n    description: '查看成功案例，了解智界AIGC如何帮助用户实现创作目标'\n  }\n}, {\n  path: '/tutorials',\n  name: 'WebsiteTutorials',\n  component: function component() {\n    return import('@/views/website/tutorials/Tutorials.vue');\n  },\n  meta: {\n    title: '教程中心 - 智界AIGC',\n    description: '详细的使用教程和操作指南，快速上手智界AIGC'\n  }\n}, {\n  path: '/signin',\n  name: 'WebsiteSignIn',\n  component: function component() {\n    return import('@/views/website/signin/SignIn.vue');\n  },\n  meta: {\n    title: '签到奖励 - 智界AIGC',\n    description: '每日签到获取积分奖励，兑换更多精彩内容'\n  }\n}, {\n  path: '/membership',\n  name: 'WebsiteMembership',\n  component: function component() {\n    return import('@/views/website/membership/Membership.vue');\n  },\n  meta: {\n    title: '订阅会员 - 智界AIGC',\n    description: '成为会员享受更多特权，解锁高级功能'\n  }\n}, {\n  path: '/affiliate',\n  name: 'WebsiteAffiliate',\n  component: function component() {\n    return import('@/views/website/affiliate/Affiliate.vue');\n  },\n  meta: {\n    title: '邀请奖励 - 智界AIGC',\n    description: '邀请好友注册智界AIGC，获得丰厚奖励'\n  }\n}, {\n  path: '/usercenter',\n  name: 'WebsiteUserCenter',\n  component: function component() {\n    return import('@/views/website/usercenter/UserCenter.vue');\n  },\n  meta: {\n    title: '个人中心 - 智界AIGC',\n    description: '管理您的账户信息、订单记录和个人设置',\n    requiresAuth: true // 需要登录验证\n\n  }\n}, {\n  path: '/JianYingDraft',\n  name: 'JianYingDraft',\n  component: function component() {\n    return import('@/views/website/JianYingDraft.vue');\n  },\n  meta: {\n    title: '剪映小助手 - 智界AIGC',\n    description: '智能视频剪辑工具，支持剪映草稿导入，让创作更简单高效'\n  }\n}, {\n  path: '/login',\n  name: 'WebsiteLogin',\n  component: function component() {\n    return import('@/views/website/auth/Login.vue');\n  },\n  meta: {\n    title: '登录 - 智界AIGC',\n    description: '登录您的智界AIGC账户，享受AI内容创作服务'\n  }\n}, {\n  path: '/user-agreement',\n  name: 'UserAgreement',\n  component: function component() {\n    return import('@/views/website/legal/UserAgreement.vue');\n  },\n  meta: {\n    title: '用户服务协议 - 智界AIGC',\n    description: '智界AIGC用户服务协议，了解您的权利和义务'\n  }\n}, {\n  path: '/privacy-policy',\n  name: 'PrivacyPolicy',\n  component: function component() {\n    return import('@/views/website/legal/PrivacyPolicy.vue');\n  },\n  meta: {\n    title: '隐私政策 - 智界AIGC',\n    description: '智界AIGC隐私政策，了解我们如何保护您的个人信息'\n  }\n}, {\n  path: '/not-found',\n  name: 'WebsiteNotFound',\n  component: function component() {\n    return import('@/views/website/exception/NotFound.vue');\n  },\n  // ✅ 修正路径\n  meta: {\n    title: '页面未找到 - 智界AIGC',\n    description: '抱歉，您访问的页面不存在'\n  }\n}, {\n  path: '/carousel-test',\n  name: 'CarouselTest',\n  component: function component() {\n    return import('@/views/website/home/<USER>');\n  },\n  meta: {\n    title: '轮播图功能测试',\n    description: '轮播图组件功能测试页面'\n  }\n}, {\n  path: '/route-test',\n  name: 'RouteTest',\n  component: function component() {\n    return import('@/views/website/test/RouteTest.vue');\n  },\n  meta: {\n    title: '路由测试 - 智界AIGC',\n    description: '路由功能测试页面'\n  }\n}, {\n  path: '/error-test',\n  name: 'ErrorTest',\n  component: function component() {\n    return import('@/views/test/SimpleErrorTest.vue');\n  },\n  meta: {\n    title: '错误处理测试 - 智界AIGC',\n    description: '错误处理功能测试页面'\n  }\n}, // ✅ 根路径处理 - 让 permission.js 处理复杂逻辑\n{\n  path: '/',\n  beforeEnter: function beforeEnter(to, from, next) {\n    // 让路由守卫处理根路径的复杂逻辑\n    next();\n  }\n}, // 支付相关页面\n{\n  path: '/payment/success',\n  name: 'PaymentSuccess',\n  component: function component() {\n    return import(\n    /* webpackChunkName: \"payment\" */\n    '@/views/payment/Success');\n  },\n  meta: {\n    title: '支付成功 - 智界AIGC',\n    description: '支付成功，感谢您的支持'\n  }\n}, {\n  path: '/payment/failure',\n  name: 'PaymentFailure',\n  component: function component() {\n    return import(\n    /* webpackChunkName: \"payment\" */\n    '@/views/payment/Failure');\n  },\n  meta: {\n    title: '支付失败 - 智界AIGC',\n    description: '支付失败，请重试'\n  }\n}, // 异常页面\n{\n  path: '/404',\n  component: function component() {\n    return import(\n    /* webpackChunkName: \"fail\" */\n    '@/views/exception/404');\n  }\n}, {\n  path: '/server-error',\n  name: 'ServerError',\n  component: function component() {\n    return import(\n    /* webpackChunkName: \"fail\" */\n    '@/views/exception/ServerError');\n  },\n  meta: {\n    title: '服务器异常 - 智界AIGC',\n    description: '服务器连接异常，请稍后重试'\n  }\n}, // 全局通配符路由 - 必须放在最后\n// 🔧 智能404处理：检查admin用户是否需要加载动态路由\n{\n  path: '*',\n  component: function component() {\n    return import('@/components/SmartNotFound.vue');\n  },\n  meta: {\n    title: '页面检查中 - 智界AIGC',\n    description: '正在检查页面权限和路由配置'\n  }\n}];", null]}