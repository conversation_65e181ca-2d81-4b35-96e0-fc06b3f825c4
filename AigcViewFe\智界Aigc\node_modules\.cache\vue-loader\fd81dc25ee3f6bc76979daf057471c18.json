{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue?vue&type=template&id=2f396a92&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue", "mtime": 1753756420429}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"payment-failure-page\" }, [\n    _c(\"div\", { staticClass: \"failure-container\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"failure-icon\" },\n        [_c(\"a-icon\", { attrs: { type: \"close-circle\", theme: \"filled\" } })],\n        1\n      ),\n      _c(\"h1\", { staticClass: \"failure-title\" }, [_vm._v(\"支付失败\")]),\n      _vm._m(0),\n      _vm.orderInfo\n        ? _c(\"div\", { staticClass: \"order-info\" }, [\n            _c(\"div\", { staticClass: \"info-item\" }, [\n              _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"订单号：\")]),\n              _c(\"span\", { staticClass: \"value\" }, [\n                _vm._v(_vm._s(_vm.orderInfo.orderId))\n              ])\n            ]),\n            _vm.orderInfo.amount\n              ? _c(\"div\", { staticClass: \"info-item\" }, [\n                  _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"订单金额：\")]),\n                  _c(\"span\", { staticClass: \"value amount\" }, [\n                    _vm._v(\"¥\" + _vm._s(_vm.orderInfo.amount))\n                  ])\n                ])\n              : _vm._e(),\n            _c(\"div\", { staticClass: \"info-item\" }, [\n              _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"失败时间：\")]),\n              _c(\"span\", { staticClass: \"value\" }, [\n                _vm._v(_vm._s(_vm.formatTime(new Date())))\n              ])\n            ])\n          ])\n        : _vm._e(),\n      _c(\n        \"div\",\n        { staticClass: \"action-buttons\" },\n        [\n          _c(\n            \"a-button\",\n            {\n              attrs: { type: \"primary\", size: \"large\" },\n              on: { click: _vm.retryPayment }\n            },\n            [_vm._v(\"\\n        重新支付\\n      \")]\n          ),\n          _c(\n            \"a-button\",\n            {\n              staticStyle: { \"margin-left\": \"16px\" },\n              attrs: { size: \"large\" },\n              on: { click: _vm.goToUserCenter }\n            },\n            [_vm._v(\"\\n        查看订单\\n      \")]\n          ),\n          _c(\n            \"a-button\",\n            {\n              staticStyle: { \"margin-left\": \"16px\" },\n              attrs: { size: \"large\" },\n              on: { click: _vm.goHome }\n            },\n            [_vm._v(\"\\n        返回首页\\n      \")]\n          )\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"help-info\" },\n        [\n          _c(\"a-alert\", {\n            attrs: {\n              message: \"需要帮助？\",\n              description:\n                \"如果问题持续存在，请联系客服：400-123-4567 或发送邮件至 <EMAIL>\",\n              type: \"warning\",\n              \"show-icon\": \"\"\n            }\n          })\n        ],\n        1\n      )\n    ])\n  ])\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"failure-message\" }, [\n      _c(\"p\", [_vm._v(\"很抱歉，您的支付未能成功完成。\")]),\n      _c(\"p\", [_vm._v(\"可能的原因：\")]),\n      _c(\"ul\", { staticClass: \"reason-list\" }, [\n        _c(\"li\", [_vm._v(\"支付过程中网络连接中断\")]),\n        _c(\"li\", [_vm._v(\"支付信息验证失败\")]),\n        _c(\"li\", [_vm._v(\"支付宝账户余额不足\")]),\n        _c(\"li\", [_vm._v(\"银行卡限额或状态异常\")])\n      ])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}