{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'AigcWithdrawalList',\n  data: function data() {\n    return {\n      loading: false,\n      // 搜索表单\n      searchForm: {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      },\n      // 表格数据\n      dataSource: [],\n      // 分页\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total) {\n          return \"\\u5171 \".concat(total, \" \\u6761\\u8BB0\\u5F55\");\n        }\n      },\n      // 表格列定义\n      columns: [{\n        title: '用户信息',\n        key: 'userInfo',\n        width: 150,\n        scopedSlots: {\n          customRender: 'userInfo'\n        }\n      }, {\n        title: '提现金额',\n        key: 'amount',\n        width: 120,\n        align: 'right',\n        scopedSlots: {\n          customRender: 'amount'\n        }\n      }, {\n        title: '支付宝信息',\n        key: 'alipayInfo',\n        width: 180,\n        scopedSlots: {\n          customRender: 'alipayInfo'\n        }\n      }, {\n        title: '申请时间',\n        dataIndex: 'apply_time',\n        key: 'applyTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'applyTime'\n        }\n      }, {\n        title: '审核时间',\n        dataIndex: 'review_time',\n        key: 'reviewTime',\n        width: 150,\n        scopedSlots: {\n          customRender: 'reviewTime'\n        }\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        width: 100,\n        scopedSlots: {\n          customRender: 'status'\n        }\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 280,\n        fixed: 'right',\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      // 弹窗状态\n      showRejectModal: false,\n      showDetailModal: false,\n      currentRecord: null,\n      rejectReason: '',\n      rejecting: false\n    };\n  },\n  mounted: function mounted() {\n    this.loadData();\n  },\n  methods: {\n    // 加载数据\n    loadData: function () {\n      var _loadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var params, response, data, firstRecord, errorMsg;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                _context.prev = 0;\n                this.loading = true;\n                params = _objectSpread({\n                  current: this.pagination.current,\n                  size: this.pagination.pageSize\n                }, this.getSearchParams());\n                _context.next = 5;\n                return this.$http.get('/api/usercenter/admin/withdrawalList', {\n                  params: params\n                });\n\n              case 5:\n                response = _context.sent;\n                console.log('提现列表完整响应:', response); // 根据实际返回的数据结构处理\n\n                data = response.data || response;\n                console.log('提现列表数据:', data);\n\n                if (data && data.success) {\n                  this.dataSource = data.result.records || [];\n                  this.pagination.total = data.result.total || 0;\n                  console.log('数据加载成功:', this.dataSource.length, '条记录');\n                  console.log('完整result结构:', data.result);\n                  console.log('records数组:', data.result.records);\n                  console.log('第一条数据结构:', this.dataSource[0]);\n                  console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {})); // 打印每个字段的值\n\n                  if (this.dataSource[0]) {\n                    firstRecord = this.dataSource[0];\n                    console.log('字段值详情:');\n                    Object.keys(firstRecord).forEach(function (key) {\n                      console.log(\"  \".concat(key, \":\"), firstRecord[key]);\n                    });\n                  }\n                } else {\n                  errorMsg = data && data.message || '获取数据失败';\n                  this.$message.error(errorMsg);\n                  this.dataSource = [];\n                  this.pagination.total = 0;\n                }\n\n                _context.next = 16;\n                break;\n\n              case 12:\n                _context.prev = 12;\n                _context.t0 = _context[\"catch\"](0);\n                console.error('加载提现数据失败:', _context.t0);\n                this.$message.error('加载数据失败');\n\n              case 16:\n                _context.prev = 16;\n                this.loading = false;\n                return _context.finish(16);\n\n              case 19:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[0, 12, 16, 19]]);\n      }));\n\n      function loadData() {\n        return _loadData.apply(this, arguments);\n      }\n\n      return loadData;\n    }(),\n    // 获取搜索参数\n    getSearchParams: function getSearchParams() {\n      var params = {};\n\n      if (this.searchForm.status !== undefined) {\n        params.status = this.searchForm.status;\n      }\n\n      if (this.searchForm.username) {\n        params.username = this.searchForm.username.trim();\n      }\n\n      if (this.searchForm.alipayInfo) {\n        params.alipayInfo = this.searchForm.alipayInfo.trim();\n      }\n\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD');\n        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD');\n      }\n\n      return params;\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.pagination.current = 1;\n      this.loadData();\n    },\n    // 重置搜索\n    handleReset: function handleReset() {\n      this.searchForm = {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      };\n      this.pagination.current = 1;\n      this.loadData();\n    },\n    // 表格变化\n    handleTableChange: function handleTableChange(pagination) {\n      this.pagination = _objectSpread(_objectSpread({}, this.pagination), pagination);\n      this.loadData();\n    },\n    // 审核通过\n    handleApprove: function () {\n      var _handleApprove = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(record) {\n        var _this = this;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.$confirm({\n                  title: '确认审核通过',\n                  content: \"\\u786E\\u5B9A\\u8981\\u5BA1\\u6838\\u901A\\u8FC7\\u7528\\u6237 \".concat(record.username, \" \\u7684\\u63D0\\u73B0\\u7533\\u8BF7\\u5417\\uFF1F\\n\\u63D0\\u73B0\\u91D1\\u989D\\uFF1A\\xA5\").concat(this.formatNumber(record.withdrawal_amount)),\n                  onOk: function () {\n                    var _onOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n                      var response, data;\n                      return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n                        while (1) {\n                          switch (_context2.prev = _context2.next) {\n                            case 0:\n                              _context2.prev = 0;\n\n                              _this.$set(record, 'approving', true);\n\n                              _context2.next = 4;\n                              return _this.$http.post('/api/usercenter/admin/approveWithdrawal', {\n                                id: record.id\n                              });\n\n                            case 4:\n                              response = _context2.sent;\n                              // 根据实际返回的数据结构处理\n                              data = response.data || response;\n\n                              if (data.success) {\n                                _this.$message.success('审核通过成功');\n\n                                _this.loadData();\n                              } else {\n                                _this.$message.error(data.message || '审核失败');\n                              }\n\n                              _context2.next = 13;\n                              break;\n\n                            case 9:\n                              _context2.prev = 9;\n                              _context2.t0 = _context2[\"catch\"](0);\n                              console.error('审核通过失败:', _context2.t0);\n\n                              _this.$message.error('审核失败，请重试');\n\n                            case 13:\n                              _context2.prev = 13;\n\n                              _this.$set(record, 'approving', false);\n\n                              return _context2.finish(13);\n\n                            case 16:\n                            case \"end\":\n                              return _context2.stop();\n                          }\n                        }\n                      }, _callee2, null, [[0, 9, 13, 16]]);\n                    }));\n\n                    function onOk() {\n                      return _onOk.apply(this, arguments);\n                    }\n\n                    return onOk;\n                  }()\n                });\n\n              case 1:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this);\n      }));\n\n      function handleApprove(_x) {\n        return _handleApprove.apply(this, arguments);\n      }\n\n      return handleApprove;\n    }(),\n    // 审核拒绝\n    handleReject: function handleReject(record) {\n      this.currentRecord = record;\n      this.rejectReason = '';\n      this.showRejectModal = true;\n    },\n    // 确认拒绝\n    confirmReject: function () {\n      var _confirmReject = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (this.rejectReason.trim()) {\n                  _context4.next = 3;\n                  break;\n                }\n\n                this.$message.warning('请填写拒绝原因');\n                return _context4.abrupt(\"return\");\n\n              case 3:\n                _context4.prev = 3;\n                this.rejecting = true;\n                _context4.next = 7;\n                return this.$http.post('/api/usercenter/admin/rejectWithdrawal', {\n                  id: this.currentRecord.id,\n                  reason: this.rejectReason.trim()\n                });\n\n              case 7:\n                response = _context4.sent;\n                // 根据实际返回的数据结构处理\n                data = response.data || response;\n\n                if (data.success) {\n                  this.$message.success('审核拒绝成功');\n                  this.showRejectModal = false;\n                  this.loadData();\n                } else {\n                  this.$message.error(data.message || '审核失败');\n                }\n\n                _context4.next = 16;\n                break;\n\n              case 12:\n                _context4.prev = 12;\n                _context4.t0 = _context4[\"catch\"](3);\n                console.error('审核拒绝失败:', _context4.t0);\n                this.$message.error('审核失败，请重试');\n\n              case 16:\n                _context4.prev = 16;\n                this.rejecting = false;\n                return _context4.finish(16);\n\n              case 19:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[3, 12, 16, 19]]);\n      }));\n\n      function confirmReject() {\n        return _confirmReject.apply(this, arguments);\n      }\n\n      return confirmReject;\n    }(),\n    // 查看详情\n    handleViewDetail: function handleViewDetail(record) {\n      this.currentRecord = record;\n      this.showDetailModal = true;\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(status) {\n      var colorMap = {\n        1: 'orange',\n        // 待审核 - 橙色\n        2: 'green',\n        // 已发放 - 绿色\n        3: 'red',\n        // 审核拒绝 - 红色\n        4: 'gray' // 已取消 - 灰色\n\n      };\n      return colorMap[status] || 'volcano'; // 未知状态用火山红色\n    },\n    // 获取状态文本\n    getStatusText: function getStatusText(status, reviewRemark) {\n      var textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝',\n        4: '已取消'\n      };\n      var statusText = textMap[status] || '未知状态'; // 如果是审核拒绝状态且有拒绝原因，则添加原因\n\n      if (status === 3 && reviewRemark) {\n        statusText += \"\\uFF08\".concat(reviewRemark, \"\\uFF09\");\n      }\n\n      return statusText;\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(number) {\n      if (!number) return '0.00';\n      return parseFloat(number).toFixed(2);\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(dateString) {\n      if (!dateString) return '-';\n\n      try {\n        var date = new Date(dateString);\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } catch (error) {\n        return '-';\n      }\n    }\n  }\n};", {"version": 3, "sources": ["AigcWithdrawalList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2OA,eAAA;AACA,EAAA,IAAA,EAAA,oBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,UAAA,EAAA;AAJA,OAHA;AASA;AACA,MAAA,UAAA,EAAA,EAVA;AAWA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,eAAA,EAAA,IAJA;AAKA,QAAA,eAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA,mBAAA,KAAA;AAAA,kCAAA,KAAA;AAAA;AANA,OAZA;AAoBA;AACA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,GAAA,EAAA,UAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OADA,EAOA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,GAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,KAAA,EAAA,OAJA;AAKA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AALA,OAPA,EAcA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,GAAA,EAAA,YAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OAdA,EAoBA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,YAFA;AAGA,QAAA,GAAA,EAAA,WAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AALA,OApBA,EA2BA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,aAFA;AAGA,QAAA,GAAA,EAAA,YAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AALA,OA3BA,EAkCA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,KAAA,EAAA,GAJA;AAKA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AALA,OAlCA,EAyCA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,KAAA,EAAA,OAJA;AAKA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AALA,OAzCA,CArBA;AAsEA;AACA,MAAA,eAAA,EAAA,KAvEA;AAwEA,MAAA,eAAA,EAAA,KAxEA;AAyEA,MAAA,aAAA,EAAA,IAzEA;AA0EA,MAAA,YAAA,EAAA,EA1EA;AA2EA,MAAA,SAAA,EAAA;AA3EA,KAAA;AA6EA,GAhFA;AAiFA,EAAA,OAjFA,qBAiFA;AACA,SAAA,QAAA;AACA,GAnFA;AAoFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,qBAAA,OAAA,GAAA,IAAA;AAEA,gBAAA,MANA;AAOA,kBAAA,OAAA,EAAA,KAAA,UAAA,CAAA,OAPA;AAQA,kBAAA,IAAA,EAAA,KAAA,UAAA,CAAA;AARA,mBASA,KAAA,eAAA,EATA;AAAA;AAAA,uBAYA,KAAA,KAAA,CAAA,GAAA,CAAA,sCAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CAZA;;AAAA;AAYA,gBAAA,QAZA;AAaA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,QAAA,EAbA,CAeA;;AACA,gBAAA,IAhBA,GAgBA,QAAA,CAAA,IAAA,IAAA,QAhBA;AAiBA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;;AAEA,oBAAA,IAAA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,uBAAA,UAAA,GAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,CAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,KAAA,UAAA,CAAA,MAAA,EAAA,KAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,IAAA,CAAA,MAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,IAAA,CAAA,KAAA,UAAA,CAAA,CAAA,KAAA,EAAA,CAAA,EAPA,CASA;;AACA,sBAAA,KAAA,UAAA,CAAA,CAAA,CAAA,EAAA;AACA,oBAAA,WADA,GACA,KAAA,UAAA,CAAA,CAAA,CADA;AAEA,oBAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,OAAA,CAAA,GAAA,aAAA,GAAA,QAAA,WAAA,CAAA,GAAA,CAAA;AACA,qBAFA;AAGA;AACA,iBAjBA,MAiBA;AACA,kBAAA,QADA,GACA,IAAA,IAAA,IAAA,CAAA,OAAA,IAAA,QADA;AAEA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,uBAAA,UAAA,GAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA,GAAA,CAAA;AACA;;AAzCA;AAAA;;AAAA;AAAA;AAAA;AA2CA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AA5CA;AAAA;AA8CA,qBAAA,OAAA,GAAA,KAAA;AA9CA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkDA;AACA,IAAA,eAnDA,6BAmDA;AACA,UAAA,MAAA,GAAA,EAAA;;AAEA,UAAA,KAAA,UAAA,CAAA,MAAA,KAAA,SAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,KAAA,UAAA,CAAA,MAAA;AACA;;AAEA,UAAA,KAAA,UAAA,CAAA,QAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,QAAA,CAAA,IAAA,EAAA;AACA;;AAEA,UAAA,KAAA,UAAA,CAAA,UAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,KAAA,UAAA,CAAA,UAAA,CAAA,IAAA,EAAA;AACA;;AAEA,UAAA,KAAA,UAAA,CAAA,SAAA,IAAA,KAAA,UAAA,CAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA,UAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CAAA;AACA;;AAEA,aAAA,MAAA;AACA,KAxEA;AA0EA;AACA,IAAA,YA3EA,0BA2EA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,QAAA;AACA,KA9EA;AAgFA;AACA,IAAA,WAjFA,yBAiFA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,UAAA,EAAA;AAJA,OAAA;AAMA,WAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,QAAA;AACA,KA1FA;AA4FA;AACA,IAAA,iBA7FA,6BA6FA,UA7FA,EA6FA;AACA,WAAA,UAAA,mCAAA,KAAA,UAAA,GAAA,UAAA;AACA,WAAA,QAAA;AACA,KAhGA;AAkGA;AACA,IAAA,aAnGA;AAAA,sGAmGA,MAnGA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAoGA,qBAAA,QAAA,CAAA;AACA,kBAAA,KAAA,EAAA,QADA;AAEA,kBAAA,OAAA,mEAAA,MAAA,CAAA,QAAA,4FAAA,KAAA,YAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,CAFA;AAGA,kBAAA,IAAA;AAAA,yFAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA,8BAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA,WAAA,EAAA,IAAA;;AAFA;AAAA,qCAIA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,yCAAA,EAAA;AACA,gCAAA,EAAA,EAAA,MAAA,CAAA;AADA,+BAAA,CAJA;;AAAA;AAIA,8BAAA,QAJA;AAQA;AACA,8BAAA,IATA,GASA,QAAA,CAAA,IAAA,IAAA,QATA;;AAWA,kCAAA,IAAA,CAAA,OAAA,EAAA;AACA,gCAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,gCAAA,KAAA,CAAA,QAAA;AACA,+BAHA,MAGA;AACA,gCAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,IAAA,MAAA;AACA;;AAhBA;AAAA;;AAAA;AAAA;AAAA;AAkBA,8BAAA,OAAA,CAAA,KAAA,CAAA,SAAA;;AACA,8BAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AAnBA;AAAA;;AAqBA,8BAAA,KAAA,CAAA,IAAA,CAAA,MAAA,EAAA,WAAA,EAAA,KAAA;;AArBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAHA,iBAAA;;AApGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkIA;AACA,IAAA,YAnIA,wBAmIA,MAnIA,EAmIA;AACA,WAAA,aAAA,GAAA,MAAA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,IAAA;AACA,KAvIA;AAyIA;AACA,IAAA,aA1IA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBA2IA,KAAA,YAAA,CAAA,IAAA,EA3IA;AAAA;AAAA;AAAA;;AA4IA,qBAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AA5IA;;AAAA;AAAA;AAiJA,qBAAA,SAAA,GAAA,IAAA;AAjJA;AAAA,uBAmJA,KAAA,KAAA,CAAA,IAAA,CAAA,wCAAA,EAAA;AACA,kBAAA,EAAA,EAAA,KAAA,aAAA,CAAA,EADA;AAEA,kBAAA,MAAA,EAAA,KAAA,YAAA,CAAA,IAAA;AAFA,iBAAA,CAnJA;;AAAA;AAmJA,gBAAA,QAnJA;AAwJA;AACA,gBAAA,IAzJA,GAyJA,QAAA,CAAA,IAAA,IAAA,QAzJA;;AA2JA,oBAAA,IAAA,CAAA,OAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA,uBAAA,eAAA,GAAA,KAAA;AACA,uBAAA,QAAA;AACA,iBAJA,MAIA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,IAAA,MAAA;AACA;;AAjKA;AAAA;;AAAA;AAAA;AAAA;AAmKA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AApKA;AAAA;AAsKA,qBAAA,SAAA,GAAA,KAAA;AAtKA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA0KA;AACA,IAAA,gBA3KA,4BA2KA,MA3KA,EA2KA;AACA,WAAA,aAAA,GAAA,MAAA;AACA,WAAA,eAAA,GAAA,IAAA;AACA,KA9KA;AAgLA;AACA,IAAA,cAjLA,0BAiLA,MAjLA,EAiLA;AACA,UAAA,QAAA,GAAA;AACA,WAAA,QADA;AACA;AACA,WAAA,OAFA;AAEA;AACA,WAAA,KAHA;AAGA;AACA,WAAA,MAJA,CAIA;;AAJA,OAAA;AAMA,aAAA,QAAA,CAAA,MAAA,CAAA,IAAA,SAAA,CAPA,CAOA;AACA,KAzLA;AA2LA;AACA,IAAA,aA5LA,yBA4LA,MA5LA,EA4LA,YA5LA,EA4LA;AACA,UAAA,OAAA,GAAA;AACA,WAAA,KADA;AAEA,WAAA,KAFA;AAGA,WAAA,MAHA;AAIA,WAAA;AAJA,OAAA;AAMA,UAAA,UAAA,GAAA,OAAA,CAAA,MAAA,CAAA,IAAA,MAAA,CAPA,CASA;;AACA,UAAA,MAAA,KAAA,CAAA,IAAA,YAAA,EAAA;AACA,QAAA,UAAA,oBAAA,YAAA,WAAA;AACA;;AAEA,aAAA,UAAA;AACA,KA3MA;AA6MA;AACA,IAAA,YA9MA,wBA8MA,MA9MA,EA8MA;AACA,UAAA,CAAA,MAAA,EAAA,OAAA,MAAA;AACA,aAAA,UAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KAjNA;AAmNA;AACA,IAAA,cApNA,0BAoNA,UApNA,EAoNA;AACA,UAAA,CAAA,UAAA,EAAA,OAAA,GAAA;;AAEA,UAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,UAAA,CAAA;AACA,eAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,IAAA,EAAA,SAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAAA,CAAA;AAOA,OATA,CASA,OAAA,KAAA,EAAA;AACA,eAAA,GAAA;AACA;AACA;AAnOA;AApFA,CAAA", "sourcesContent": ["<template>\n  <div class=\"withdrawal-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>提现管理</h2>\n      <p>管理用户提现申请，审核通过或拒绝申请</p>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <a-card :bordered=\"false\">\n        <a-form layout=\"inline\" :model=\"searchForm\" @submit=\"handleSearch\">\n          <a-form-item label=\"申请状态\">\n            <a-select v-model=\"searchForm.status\" placeholder=\"请选择状态\" style=\"width: 120px\" allowClear>\n              <a-select-option :value=\"1\">待审核</a-select-option>\n              <a-select-option :value=\"2\">已发放</a-select-option>\n              <a-select-option :value=\"3\">审核拒绝</a-select-option>\n              <a-select-option :value=\"4\">已取消</a-select-option>\n            </a-select>\n          </a-form-item>\n          <a-form-item label=\"申请时间\">\n            <a-range-picker\n              v-model=\"searchForm.dateRange\"\n              format=\"YYYY-MM-DD\"\n              :placeholder=\"['开始时间', '结束时间']\"\n            />\n          </a-form-item>\n          <a-form-item label=\"用户名\">\n            <a-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" style=\"width: 150px\" />\n          </a-form-item>\n\n          <a-form-item label=\"支付宝信息\">\n            <a-input v-model=\"searchForm.alipayInfo\" placeholder=\"支付宝账号或姓名\" style=\"width: 150px\" />\n          </a-form-item>\n\n          <a-form-item>\n            <a-button type=\"primary\" @click=\"handleSearch\" :loading=\"loading\">\n              <a-icon type=\"search\" />\n              搜索\n            </a-button>\n            <a-button @click=\"handleReset\" style=\"margin-left: 8px\">\n              <a-icon type=\"reload\" />\n              重置\n            </a-button>\n          </a-form-item>\n        </a-form>\n      </a-card>\n    </div>\n\n    <!-- 数据表格 -->\n    <div class=\"table-section\">\n      <a-card :bordered=\"false\">\n        <!-- 表格 -->\n        <a-table\n          :columns=\"columns\"\n          :data-source=\"dataSource\"\n          :loading=\"loading\"\n          :pagination=\"pagination\"\n          row-key=\"id\"\n          @change=\"handleTableChange\"\n          :scroll=\"{ x: 1200 }\"\n        >\n          <!-- 用户信息列 -->\n          <template slot=\"userInfo\" slot-scope=\"text, record\">\n            <div class=\"user-info\" v-if=\"record\">\n              <div class=\"username\">{{ record.username || '-' }}</div>\n              <div class=\"user-id\">ID: {{ record.user_id || '-' }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 提现金额列 -->\n          <template slot=\"amount\" slot-scope=\"text, record\">\n            <div class=\"amount-info\" v-if=\"record\">\n              <div class=\"amount\">¥{{ formatNumber(record.withdrawal_amount) }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 支付宝信息列 -->\n          <template slot=\"alipayInfo\" slot-scope=\"text, record\">\n            <div class=\"alipay-info\" v-if=\"record\">\n              <div class=\"name\">{{ record.alipay_name || '-' }}</div>\n              <div class=\"account\">{{ record.alipay_account || '-' }}</div>\n            </div>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 状态列 -->\n          <template slot=\"status\" slot-scope=\"text, record\">\n            <a-tag :color=\"getStatusColor(record && record.status)\" v-if=\"record\">\n              {{ getStatusText(record.status, record.review_remark) }}\n            </a-tag>\n            <span v-else>-</span>\n          </template>\n\n          <!-- 申请时间列 -->\n          <template slot=\"applyTime\" slot-scope=\"text, record\">\n            <span>{{ record && record.apply_time ? formatDateTime(record.apply_time) : '-' }}</span>\n          </template>\n\n          <!-- 审核时间列 -->\n          <template slot=\"reviewTime\" slot-scope=\"text, record\">\n            <span>{{ record && record.review_time ? formatDateTime(record.review_time) : '-' }}</span>\n          </template>\n\n          <!-- 操作列 -->\n          <template slot=\"action\" slot-scope=\"text, record\">\n            <div class=\"action-buttons\" v-if=\"record\">\n              <a-button\n                v-if=\"record.status === 1\"\n                type=\"primary\"\n                size=\"small\"\n                @click=\"handleApprove(record)\"\n                :loading=\"record.approving\"\n              >\n                审核通过\n              </a-button>\n\n              <a-button\n                v-if=\"record.status === 1\"\n                type=\"danger\"\n                size=\"small\"\n                @click=\"handleReject(record)\"\n                :loading=\"record.rejecting\"\n                style=\"margin-left: 8px\"\n              >\n                审核拒绝\n              </a-button>\n\n              <a-button\n                size=\"small\"\n                @click=\"handleViewDetail(record)\"\n                style=\"margin-left: 8px\"\n              >\n                查看详情\n              </a-button>\n            </div>\n            <span v-else>-</span>\n          </template>\n        </a-table>\n      </a-card>\n    </div>\n\n    <!-- 审核拒绝原因弹窗 -->\n    <a-modal\n      v-model=\"showRejectModal\"\n      title=\"审核拒绝\"\n      :footer=\"null\"\n      width=\"500px\"\n    >\n      <div class=\"reject-modal\">\n        <a-alert \n          message=\"请填写拒绝原因\" \n          type=\"warning\" \n          show-icon \n          style=\"margin-bottom: 20px\"\n        />\n        \n        <a-form layout=\"vertical\">\n          <a-form-item label=\"拒绝原因\" required>\n            <a-textarea \n              v-model=\"rejectReason\" \n              placeholder=\"请输入拒绝原因\"\n              :rows=\"4\"\n              :maxLength=\"200\"\n            />\n          </a-form-item>\n        </a-form>\n        \n        <div class=\"modal-actions\">\n          <a-button @click=\"showRejectModal = false\">\n            取消\n          </a-button>\n          <a-button \n            type=\"danger\" \n            @click=\"confirmReject\"\n            :loading=\"rejecting\"\n            :disabled=\"!rejectReason.trim()\"\n            style=\"margin-left: 10px\"\n          >\n            确认拒绝\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 详情弹窗 -->\n    <a-modal\n      v-model=\"showDetailModal\"\n      title=\"提现申请详情\"\n      :footer=\"null\"\n      width=\"600px\"\n    >\n      <div class=\"detail-modal\" v-if=\"currentRecord\">\n        <a-descriptions :column=\"2\" bordered>\n          <a-descriptions-item label=\"申请ID\">\n            {{ currentRecord.id }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"用户名\">\n            {{ currentRecord.username }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"提现金额\">\n            <span class=\"amount-text\">¥{{ formatNumber(currentRecord.withdrawal_amount) }}</span>\n          </a-descriptions-item>\n          <a-descriptions-item label=\"申请状态\">\n            <a-tag :color=\"getStatusColor(currentRecord.status)\">\n              {{ getStatusText(currentRecord.status, currentRecord.review_remark) }}\n            </a-tag>\n          </a-descriptions-item>\n          <a-descriptions-item label=\"真实姓名\">\n            {{ currentRecord.alipay_name }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"支付宝账号\">\n            {{ currentRecord.alipay_account }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"申请时间\">\n            {{ currentRecord.apply_time ? formatDateTime(currentRecord.apply_time) : '-' }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核时间\">\n            {{ currentRecord.review_time ? formatDateTime(currentRecord.review_time) : '-' }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核人\" v-if=\"currentRecord.review_by\">\n            {{ currentRecord.review_by }}\n          </a-descriptions-item>\n          <a-descriptions-item label=\"审核备注\" v-if=\"currentRecord.review_remark\">\n            {{ currentRecord.review_remark }}\n          </a-descriptions-item>\n        </a-descriptions>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AigcWithdrawalList',\n  data() {\n    return {\n      loading: false,\n      // 搜索表单\n      searchForm: {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      },\n      // 表格数据\n      dataSource: [],\n      // 分页\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total) => `共 ${total} 条记录`\n      },\n      // 表格列定义\n      columns: [\n        {\n          title: '用户信息',\n          key: 'userInfo',\n          width: 150,\n          scopedSlots: { customRender: 'userInfo' }\n        },\n        {\n          title: '提现金额',\n          key: 'amount',\n          width: 120,\n          align: 'right',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '支付宝信息',\n          key: 'alipayInfo',\n          width: 180,\n          scopedSlots: { customRender: 'alipayInfo' }\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'apply_time',\n          key: 'applyTime',\n          width: 150,\n          scopedSlots: { customRender: 'applyTime' }\n        },\n        {\n          title: '审核时间',\n          dataIndex: 'review_time',\n          key: 'reviewTime',\n          width: 150,\n          scopedSlots: { customRender: 'reviewTime' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          fixed: 'right',\n          scopedSlots: { customRender: 'action' }\n        }\n      ],\n      // 弹窗状态\n      showRejectModal: false,\n      showDetailModal: false,\n      currentRecord: null,\n      rejectReason: '',\n      rejecting: false\n    }\n  },\n  mounted() {\n    this.loadData()\n  },\n  methods: {\n    // 加载数据\n    async loadData() {\n      try {\n        this.loading = true\n\n        const params = {\n          current: this.pagination.current,\n          size: this.pagination.pageSize,\n          ...this.getSearchParams()\n        }\n\n        const response = await this.$http.get('/api/usercenter/admin/withdrawalList', { params })\n        console.log('提现列表完整响应:', response)\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n        console.log('提现列表数据:', data)\n\n        if (data && data.success) {\n          this.dataSource = data.result.records || []\n          this.pagination.total = data.result.total || 0\n          console.log('数据加载成功:', this.dataSource.length, '条记录')\n          console.log('完整result结构:', data.result)\n          console.log('records数组:', data.result.records)\n          console.log('第一条数据结构:', this.dataSource[0])\n          console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {}))\n\n          // 打印每个字段的值\n          if (this.dataSource[0]) {\n            const firstRecord = this.dataSource[0]\n            console.log('字段值详情:')\n            Object.keys(firstRecord).forEach(key => {\n              console.log(`  ${key}:`, firstRecord[key])\n            })\n          }\n        } else {\n          const errorMsg = (data && data.message) || '获取数据失败'\n          this.$message.error(errorMsg)\n          this.dataSource = []\n          this.pagination.total = 0\n        }\n      } catch (error) {\n        console.error('加载提现数据失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取搜索参数\n    getSearchParams() {\n      const params = {}\n\n      if (this.searchForm.status !== undefined) {\n        params.status = this.searchForm.status\n      }\n\n      if (this.searchForm.username) {\n        params.username = this.searchForm.username.trim()\n      }\n\n      if (this.searchForm.alipayInfo) {\n        params.alipayInfo = this.searchForm.alipayInfo.trim()\n      }\n\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD')\n        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD')\n      }\n\n      return params\n    },\n\n    // 搜索\n    handleSearch() {\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      }\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 表格变化\n    handleTableChange(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n      this.loadData()\n    },\n\n    // 审核通过\n    async handleApprove(record) {\n      this.$confirm({\n        title: '确认审核通过',\n        content: `确定要审核通过用户 ${record.username} 的提现申请吗？\\n提现金额：¥${this.formatNumber(record.withdrawal_amount)}`,\n        onOk: async () => {\n          try {\n            this.$set(record, 'approving', true)\n\n            const response = await this.$http.post('/api/usercenter/admin/approveWithdrawal', {\n              id: record.id\n            })\n\n            // 根据实际返回的数据结构处理\n            const data = response.data || response\n\n            if (data.success) {\n              this.$message.success('审核通过成功')\n              this.loadData()\n            } else {\n              this.$message.error(data.message || '审核失败')\n            }\n          } catch (error) {\n            console.error('审核通过失败:', error)\n            this.$message.error('审核失败，请重试')\n          } finally {\n            this.$set(record, 'approving', false)\n          }\n        }\n      })\n    },\n\n    // 审核拒绝\n    handleReject(record) {\n      this.currentRecord = record\n      this.rejectReason = ''\n      this.showRejectModal = true\n    },\n\n    // 确认拒绝\n    async confirmReject() {\n      if (!this.rejectReason.trim()) {\n        this.$message.warning('请填写拒绝原因')\n        return\n      }\n\n      try {\n        this.rejecting = true\n\n        const response = await this.$http.post('/api/usercenter/admin/rejectWithdrawal', {\n          id: this.currentRecord.id,\n          reason: this.rejectReason.trim()\n        })\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n\n        if (data.success) {\n          this.$message.success('审核拒绝成功')\n          this.showRejectModal = false\n          this.loadData()\n        } else {\n          this.$message.error(data.message || '审核失败')\n        }\n      } catch (error) {\n        console.error('审核拒绝失败:', error)\n        this.$message.error('审核失败，请重试')\n      } finally {\n        this.rejecting = false\n      }\n    },\n\n    // 查看详情\n    handleViewDetail(record) {\n      this.currentRecord = record\n      this.showDetailModal = true\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        1: 'orange',      // 待审核 - 橙色\n        2: 'green',       // 已发放 - 绿色\n        3: 'red',         // 审核拒绝 - 红色\n        4: 'gray' // 已取消 - 灰色\n      }\n      return colorMap[status] || 'volcano' // 未知状态用火山红色\n    },\n\n    // 获取状态文本\n    getStatusText(status, reviewRemark) {\n      const textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝',\n        4: '已取消'\n      }\n      let statusText = textMap[status] || '未知状态'\n\n      // 如果是审核拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n\n    // 格式化数字\n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n\n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.withdrawal-management {\n  padding: 24px;\n  background: #f0f2f5;\n  min-height: 100vh;\n}\n\n.page-header {\n  margin-bottom: 24px;\n\n  h2 {\n    margin: 0 0 8px 0;\n    font-size: 24px;\n    font-weight: 600;\n    color: #262626;\n  }\n\n  p {\n    margin: 0;\n    color: #8c8c8c;\n    font-size: 14px;\n  }\n}\n\n.search-section {\n  margin-bottom: 24px;\n}\n\n// 表格样式\n.ant-table {\n  .ant-table-thead > tr > th,\n  .ant-table-tbody > tr > td {\n    text-align: center !important;\n  }\n}\n\n// 表格内容样式\n.user-info {\n  .username {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .user-id {\n    font-size: 12px;\n    color: #8c8c8c;\n  }\n}\n\n.amount-info {\n  .amount {\n    font-weight: 600;\n    color: #262626;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n.alipay-info {\n  .name {\n    font-weight: 500;\n    color: #262626;\n    margin-bottom: 4px;\n  }\n\n  .account {\n    font-size: 12px;\n    color: #8c8c8c;\n    font-family: 'Courier New', monospace;\n  }\n}\n\n.action-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n// 弹窗样式\n.reject-modal {\n  .modal-actions {\n    text-align: center;\n    margin-top: 20px;\n  }\n}\n\n.detail-modal {\n  .amount-text {\n    font-weight: 600;\n    color: #52c41a;\n    font-family: 'Courier New', monospace;\n    font-size: 16px;\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .withdrawal-management {\n    padding: 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n\n    .ant-btn {\n      width: 100%;\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/aigcview/vue"}]}