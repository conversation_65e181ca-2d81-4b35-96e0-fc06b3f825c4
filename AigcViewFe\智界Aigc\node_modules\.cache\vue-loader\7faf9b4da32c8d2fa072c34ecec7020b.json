{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'AigcWithdrawalList',\n  data() {\n    return {\n      loading: false,\n      // 搜索表单\n      searchForm: {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      },\n      // 表格数据\n      dataSource: [],\n      // 分页\n      pagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total) => `共 ${total} 条记录`\n      },\n      // 表格列定义\n      columns: [\n        {\n          title: '用户信息',\n          key: 'userInfo',\n          width: 150,\n          scopedSlots: { customRender: 'userInfo' }\n        },\n        {\n          title: '提现金额',\n          key: 'amount',\n          width: 120,\n          align: 'right',\n          scopedSlots: { customRender: 'amount' }\n        },\n        {\n          title: '支付宝信息',\n          key: 'alipayInfo',\n          width: 180,\n          scopedSlots: { customRender: 'alipayInfo' }\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'apply_time',\n          key: 'applyTime',\n          width: 150,\n          scopedSlots: { customRender: 'applyTime' }\n        },\n        {\n          title: '审核时间',\n          dataIndex: 'review_time',\n          key: 'reviewTime',\n          width: 150,\n          scopedSlots: { customRender: 'reviewTime' }\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          scopedSlots: { customRender: 'status' }\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 280,\n          fixed: 'right',\n          scopedSlots: { customRender: 'action' }\n        }\n      ],\n      // 弹窗状态\n      showRejectModal: false,\n      showDetailModal: false,\n      currentRecord: null,\n      rejectReason: '',\n      rejecting: false\n    }\n  },\n  mounted() {\n    this.loadData()\n  },\n  methods: {\n    // 加载数据\n    async loadData() {\n      try {\n        this.loading = true\n\n        const params = {\n          current: this.pagination.current,\n          size: this.pagination.pageSize,\n          ...this.getSearchParams()\n        }\n\n        const response = await this.$http.get('/api/usercenter/admin/withdrawalList', { params })\n        console.log('提现列表完整响应:', response)\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n        console.log('提现列表数据:', data)\n\n        if (data && data.success) {\n          this.dataSource = data.result.records || []\n          this.pagination.total = data.result.total || 0\n          console.log('数据加载成功:', this.dataSource.length, '条记录')\n          console.log('完整result结构:', data.result)\n          console.log('records数组:', data.result.records)\n          console.log('第一条数据结构:', this.dataSource[0])\n          console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {}))\n\n          // 打印每个字段的值\n          if (this.dataSource[0]) {\n            const firstRecord = this.dataSource[0]\n            console.log('字段值详情:')\n            Object.keys(firstRecord).forEach(key => {\n              console.log(`  ${key}:`, firstRecord[key])\n            })\n          }\n        } else {\n          const errorMsg = (data && data.message) || '获取数据失败'\n          this.$message.error(errorMsg)\n          this.dataSource = []\n          this.pagination.total = 0\n        }\n      } catch (error) {\n        console.error('加载提现数据失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取搜索参数\n    getSearchParams() {\n      const params = {}\n\n      if (this.searchForm.status !== undefined) {\n        params.status = this.searchForm.status\n      }\n\n      if (this.searchForm.username) {\n        params.username = this.searchForm.username.trim()\n      }\n\n      if (this.searchForm.alipayInfo) {\n        params.alipayInfo = this.searchForm.alipayInfo.trim()\n      }\n\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD')\n        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD')\n      }\n\n      return params\n    },\n\n    // 搜索\n    handleSearch() {\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        status: undefined,\n        dateRange: [],\n        username: '',\n        alipayInfo: ''\n      }\n      this.pagination.current = 1\n      this.loadData()\n    },\n\n    // 表格变化\n    handleTableChange(pagination) {\n      this.pagination = { ...this.pagination, ...pagination }\n      this.loadData()\n    },\n\n    // 审核通过\n    async handleApprove(record) {\n      this.$confirm({\n        title: '确认审核通过',\n        content: `确定要审核通过用户 ${record.username} 的提现申请吗？\\n提现金额：¥${this.formatNumber(record.withdrawal_amount)}`,\n        onOk: async () => {\n          try {\n            this.$set(record, 'approving', true)\n\n            const response = await this.$http.post('/api/usercenter/admin/approveWithdrawal', {\n              id: record.id\n            })\n\n            // 根据实际返回的数据结构处理\n            const data = response.data || response\n\n            if (data.success) {\n              this.$message.success('审核通过成功')\n              this.loadData()\n            } else {\n              this.$message.error(data.message || '审核失败')\n            }\n          } catch (error) {\n            console.error('审核通过失败:', error)\n            this.$message.error('审核失败，请重试')\n          } finally {\n            this.$set(record, 'approving', false)\n          }\n        }\n      })\n    },\n\n    // 审核拒绝\n    handleReject(record) {\n      this.currentRecord = record\n      this.rejectReason = ''\n      this.showRejectModal = true\n    },\n\n    // 确认拒绝\n    async confirmReject() {\n      if (!this.rejectReason.trim()) {\n        this.$message.warning('请填写拒绝原因')\n        return\n      }\n\n      try {\n        this.rejecting = true\n\n        const response = await this.$http.post('/api/usercenter/admin/rejectWithdrawal', {\n          id: this.currentRecord.id,\n          reason: this.rejectReason.trim()\n        })\n\n        // 根据实际返回的数据结构处理\n        const data = response.data || response\n\n        if (data.success) {\n          this.$message.success('审核拒绝成功')\n          this.showRejectModal = false\n          this.loadData()\n        } else {\n          this.$message.error(data.message || '审核失败')\n        }\n      } catch (error) {\n        console.error('审核拒绝失败:', error)\n        this.$message.error('审核失败，请重试')\n      } finally {\n        this.rejecting = false\n      }\n    },\n\n    // 查看详情\n    handleViewDetail(record) {\n      this.currentRecord = record\n      this.showDetailModal = true\n    },\n\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        1: 'orange',      // 待审核 - 橙色\n        2: 'green',       // 已发放 - 绿色\n        3: 'red',         // 审核拒绝 - 红色\n        4: 'gray' // 已取消 - 灰色\n      }\n      return colorMap[status] || 'volcano' // 未知状态用火山红色\n    },\n\n    // 获取状态文本\n    getStatusText(status, reviewRemark) {\n      const textMap = {\n        1: '待审核',\n        2: '已发放',\n        3: '审核拒绝',\n        4: '已取消'\n      }\n      let statusText = textMap[status] || '未知状态'\n\n      // 如果是审核拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n\n    // 格式化数字\n    formatNumber(number) {\n      if (!number) return '0.00'\n      return parseFloat(number).toFixed(2)\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n\n      try {\n        const date = new Date(dateString)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return '-'\n      }\n    }\n  }\n}\n", null]}