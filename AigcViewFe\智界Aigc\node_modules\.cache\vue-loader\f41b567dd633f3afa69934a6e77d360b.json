{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue?vue&type=template&id=063cf8f4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue", "mtime": 1753692759803}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"smart-not-found\">\n  <!-- 加载状态 -->\n  <div v-if=\"loading\" class=\"loading-container\">\n    <div class=\"loading-content\">\n      <a-spin size=\"large\" />\n      <p class=\"loading-text\">正在检查页面权限...</p>\n    </div>\n  </div>\n\n  <!-- 404页面 -->\n  <div v-else>\n    <!-- 复用官网404页面 -->\n    <WebsiteNotFound />\n  </div>\n</div>\n", null]}