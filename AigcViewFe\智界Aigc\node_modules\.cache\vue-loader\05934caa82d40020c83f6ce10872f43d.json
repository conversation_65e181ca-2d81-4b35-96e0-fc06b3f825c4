{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=template&id=b236e548&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"withdrawal-management\">\n  <!-- 页面标题 -->\n  <div class=\"page-header\">\n    <h2>提现管理</h2>\n    <p>管理用户提现申请，审核通过或拒绝申请</p>\n  </div>\n\n  <!-- 搜索筛选区域 -->\n  <div class=\"search-section\">\n    <a-card :bordered=\"false\">\n      <a-form layout=\"inline\" :model=\"searchForm\" @submit=\"handleSearch\">\n        <a-form-item label=\"申请状态\">\n          <a-select v-model=\"searchForm.status\" placeholder=\"请选择状态\" style=\"width: 120px\" allowClear>\n            <a-select-option :value=\"1\">待审核</a-select-option>\n            <a-select-option :value=\"2\">已发放</a-select-option>\n            <a-select-option :value=\"3\">审核拒绝</a-select-option>\n            <a-select-option :value=\"4\">已取消</a-select-option>\n          </a-select>\n        </a-form-item>\n        <a-form-item label=\"申请时间\">\n          <a-range-picker\n            v-model=\"searchForm.dateRange\"\n            format=\"YYYY-MM-DD\"\n            :placeholder=\"['开始时间', '结束时间']\"\n          />\n        </a-form-item>\n        <a-form-item label=\"用户名\">\n          <a-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" style=\"width: 150px\" />\n        </a-form-item>\n\n        <a-form-item label=\"支付宝信息\">\n          <a-input v-model=\"searchForm.alipayInfo\" placeholder=\"支付宝账号或姓名\" style=\"width: 150px\" />\n        </a-form-item>\n\n        <a-form-item>\n          <a-button type=\"primary\" @click=\"handleSearch\" :loading=\"loading\">\n            <a-icon type=\"search\" />\n            搜索\n          </a-button>\n          <a-button @click=\"handleReset\" style=\"margin-left: 8px\">\n            <a-icon type=\"reload\" />\n            重置\n          </a-button>\n        </a-form-item>\n      </a-form>\n    </a-card>\n  </div>\n\n  <!-- 数据表格 -->\n  <div class=\"table-section\">\n    <a-card :bordered=\"false\">\n      <!-- 表格 -->\n      <a-table\n        :columns=\"columns\"\n        :data-source=\"dataSource\"\n        :loading=\"loading\"\n        :pagination=\"pagination\"\n        row-key=\"id\"\n        @change=\"handleTableChange\"\n        :scroll=\"{ x: 1200 }\"\n      >\n        <!-- 用户信息列 -->\n        <template slot=\"userInfo\" slot-scope=\"text, record\">\n          <div class=\"user-info\" v-if=\"record\">\n            <div class=\"username\">{{ record.username || '-' }}</div>\n            <div class=\"user-id\">ID: {{ record.user_id || '-' }}</div>\n          </div>\n          <span v-else>-</span>\n        </template>\n\n        <!-- 提现金额列 -->\n        <template slot=\"amount\" slot-scope=\"text, record\">\n          <div class=\"amount-info\" v-if=\"record\">\n            <div class=\"amount\">¥{{ formatNumber(record.withdrawal_amount) }}</div>\n          </div>\n          <span v-else>-</span>\n        </template>\n\n        <!-- 支付宝信息列 -->\n        <template slot=\"alipayInfo\" slot-scope=\"text, record\">\n          <div class=\"alipay-info\" v-if=\"record\">\n            <div class=\"name\">{{ record.alipay_name || '-' }}</div>\n            <div class=\"account\">{{ record.alipay_account || '-' }}</div>\n          </div>\n          <span v-else>-</span>\n        </template>\n\n        <!-- 状态列 -->\n        <template slot=\"status\" slot-scope=\"text, record\">\n          <a-tag :color=\"getStatusColor(record && record.status)\" v-if=\"record\">\n            {{ getStatusText(record.status, record.review_remark) }}\n          </a-tag>\n          <span v-else>-</span>\n        </template>\n\n        <!-- 申请时间列 -->\n        <template slot=\"applyTime\" slot-scope=\"text, record\">\n          <span>{{ record && record.apply_time ? formatDateTime(record.apply_time) : '-' }}</span>\n        </template>\n\n        <!-- 审核时间列 -->\n        <template slot=\"reviewTime\" slot-scope=\"text, record\">\n          <span>{{ record && record.review_time ? formatDateTime(record.review_time) : '-' }}</span>\n        </template>\n\n        <!-- 操作列 -->\n        <template slot=\"action\" slot-scope=\"text, record\">\n          <div class=\"action-buttons\" v-if=\"record\">\n            <a-button\n              v-if=\"record.status === 1\"\n              type=\"primary\"\n              size=\"small\"\n              @click=\"handleApprove(record)\"\n              :loading=\"record.approving\"\n            >\n              审核通过\n            </a-button>\n\n            <a-button\n              v-if=\"record.status === 1\"\n              type=\"danger\"\n              size=\"small\"\n              @click=\"handleReject(record)\"\n              :loading=\"record.rejecting\"\n              style=\"margin-left: 8px\"\n            >\n              审核拒绝\n            </a-button>\n\n            <a-button\n              size=\"small\"\n              @click=\"handleViewDetail(record)\"\n              style=\"margin-left: 8px\"\n            >\n              查看详情\n            </a-button>\n          </div>\n          <span v-else>-</span>\n        </template>\n      </a-table>\n    </a-card>\n  </div>\n\n  <!-- 审核拒绝原因弹窗 -->\n  <a-modal\n    v-model=\"showRejectModal\"\n    title=\"审核拒绝\"\n    :footer=\"null\"\n    width=\"500px\"\n  >\n    <div class=\"reject-modal\">\n      <a-alert \n        message=\"请填写拒绝原因\" \n        type=\"warning\" \n        show-icon \n        style=\"margin-bottom: 20px\"\n      />\n      \n      <a-form layout=\"vertical\">\n        <a-form-item label=\"拒绝原因\" required>\n          <a-textarea \n            v-model=\"rejectReason\" \n            placeholder=\"请输入拒绝原因\"\n            :rows=\"4\"\n            :maxLength=\"200\"\n          />\n        </a-form-item>\n      </a-form>\n      \n      <div class=\"modal-actions\">\n        <a-button @click=\"showRejectModal = false\">\n          取消\n        </a-button>\n        <a-button \n          type=\"danger\" \n          @click=\"confirmReject\"\n          :loading=\"rejecting\"\n          :disabled=\"!rejectReason.trim()\"\n          style=\"margin-left: 10px\"\n        >\n          确认拒绝\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n\n  <!-- 详情弹窗 -->\n  <a-modal\n    v-model=\"showDetailModal\"\n    title=\"提现申请详情\"\n    :footer=\"null\"\n    width=\"600px\"\n  >\n    <div class=\"detail-modal\" v-if=\"currentRecord\">\n      <a-descriptions :column=\"2\" bordered>\n        <a-descriptions-item label=\"申请ID\">\n          {{ currentRecord.id }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"用户名\">\n          {{ currentRecord.username }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"提现金额\">\n          <span class=\"amount-text\">¥{{ formatNumber(currentRecord.withdrawal_amount) }}</span>\n        </a-descriptions-item>\n        <a-descriptions-item label=\"申请状态\">\n          <a-tag :color=\"getStatusColor(currentRecord.status)\">\n            {{ getStatusText(currentRecord.status, currentRecord.review_remark) }}\n          </a-tag>\n        </a-descriptions-item>\n        <a-descriptions-item label=\"真实姓名\">\n          {{ currentRecord.alipay_name }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"支付宝账号\">\n          {{ currentRecord.alipay_account }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"申请时间\">\n          {{ currentRecord.apply_time ? formatDateTime(currentRecord.apply_time) : '-' }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"审核时间\">\n          {{ currentRecord.review_time ? formatDateTime(currentRecord.review_time) : '-' }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"审核人\" v-if=\"currentRecord.review_by\">\n          {{ currentRecord.review_by }}\n        </a-descriptions-item>\n        <a-descriptions-item label=\"审核备注\" v-if=\"currentRecord.review_remark\">\n          {{ currentRecord.review_remark }}\n        </a-descriptions-item>\n      </a-descriptions>\n    </div>\n  </a-modal>\n</div>\n", null]}