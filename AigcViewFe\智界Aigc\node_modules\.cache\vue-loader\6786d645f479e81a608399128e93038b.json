{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue?vue&type=template&id=605e5410&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Credits.vue", "mtime": 1753771276470}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"credits-page\" },\n    [\n      _vm._m(0),\n      _c(\n        \"div\",\n        { staticClass: \"credits-content\" },\n        [\n          _c(\"div\", { staticClass: \"balance-overview\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"balance-cards\" },\n              [\n                _c(\"StatsCard\", {\n                  attrs: {\n                    value: _vm.balanceData.currentBalance,\n                    unit: \"元\",\n                    label: \"当前余额\",\n                    icon: \"anticon anticon-wallet\",\n                    \"icon-color\": \"#10b981\",\n                    trend: _vm.balanceTrend,\n                    loading: _vm.loading\n                  },\n                  on: { click: _vm.handleQuickRecharge }\n                }),\n                _c(\"StatsCard\", {\n                  attrs: {\n                    value: _vm.balanceData.totalRecharge,\n                    unit: \"元\",\n                    label: \"累计充值\",\n                    icon: \"anticon anticon-plus-circle\",\n                    \"icon-color\": \"#7c8aed\",\n                    loading: _vm.loading\n                  }\n                }),\n                _c(\"StatsCard\", {\n                  attrs: {\n                    value: _vm.balanceData.totalConsumption,\n                    unit: \"元\",\n                    label: \"累计消费\",\n                    icon: \"anticon anticon-minus-circle\",\n                    \"icon-color\": \"#ef4444\",\n                    loading: _vm.loading\n                  }\n                }),\n                _c(\"StatsCard\", {\n                  attrs: {\n                    value: _vm.balanceData.monthlyConsumption,\n                    unit: \"元\",\n                    label: \"本月消费\",\n                    icon: \"anticon anticon-bar-chart\",\n                    \"icon-color\": \"#f59e0b\",\n                    trend: _vm.monthlyTrend,\n                    loading: _vm.loading\n                  }\n                })\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"quick-recharge\" }, [\n              _c(\"h3\", { staticClass: \"section-title\" }, [_vm._v(\"快速充值\")]),\n              _c(\n                \"div\",\n                { staticClass: \"recharge-options\" },\n                _vm._l(_vm.rechargeOptions, function(option) {\n                  return _c(\n                    \"div\",\n                    {\n                      key: option.amount,\n                      staticClass: \"recharge-option\",\n                      class: { selected: _vm.selectedAmount === option.amount },\n                      on: {\n                        click: function($event) {\n                          return _vm.selectRechargeAmount(option.amount)\n                        }\n                      }\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"option-amount\" }, [\n                        _vm._v(\"¥\" + _vm._s(option.amount))\n                      ]),\n                      _c(\"div\", { staticClass: \"option-label\" }, [\n                        _vm._v(_vm._s(option.label))\n                      ])\n                    ]\n                  )\n                }),\n                0\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"custom-amount\" },\n                [\n                  _c(\"a-input-number\", {\n                    staticStyle: { width: \"200px\" },\n                    attrs: {\n                      min: 1,\n                      max: 10000,\n                      step: 1,\n                      placeholder: \"自定义金额\",\n                      size: \"large\"\n                    },\n                    model: {\n                      value: _vm.customAmount,\n                      callback: function($$v) {\n                        _vm.customAmount = $$v\n                      },\n                      expression: \"customAmount\"\n                    }\n                  }),\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        size: \"large\",\n                        loading: _vm.rechargeLoading\n                      },\n                      on: { click: _vm.handleRecharge }\n                    },\n                    [_vm._v(\"\\n            立即充值\\n          \")]\n                  )\n                ],\n                1\n              )\n            ])\n          ]),\n          _c(\"DataTable\", {\n            ref: \"transactionTable\",\n            attrs: {\n              title: \"交易记录\",\n              \"data-source\": _vm.transactionList,\n              columns: _vm.transactionColumns,\n              loading: _vm.transactionLoading,\n              pagination: _vm.pagination,\n              \"show-action-column\": false,\n              \"type-options\": _vm.transactionTypeOptions,\n              \"status-options\": [],\n              \"show-search\": true,\n              \"type-filter-placeholder\": \"交易类型\",\n              \"status-filter-placeholder\": \"交易状态\",\n              \"search-placeholder\": \"搜索交易描述\",\n              \"date-filter-placeholder\": [\"交易时间\", \"交易时间\"]\n            },\n            on: {\n              \"filter-change\": _vm.handleFilterChange,\n              \"table-change\": _vm.handleTableChange,\n              refresh: _vm.loadTransactionData\n            },\n            scopedSlots: _vm._u([\n              {\n                key: \"actions\",\n                fn: function() {\n                  return [\n                    _c(\n                      \"a-button\",\n                      {\n                        staticStyle: {\n                          \"margin-right\": \"8px\",\n                          background:\n                            \"linear-gradient(135deg, #64748b 0%, #475569 100%)\",\n                          border: \"none\",\n                          \"border-radius\": \"8px\",\n                          \"box-shadow\": \"0 4px 12px rgba(100, 116, 139, 0.3)\",\n                          color: \"white\"\n                        },\n                        on: { click: _vm.handleResetFilters }\n                      },\n                      [\n                        _c(\"a-icon\", {\n                          staticStyle: { \"margin-right\": \"6px\" },\n                          attrs: { type: \"reload\" }\n                        }),\n                        _vm._v(\"\\n          重置\\n        \")\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"a-button\",\n                      {\n                        staticStyle: {\n                          background:\n                            \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n                          border: \"none\",\n                          \"border-radius\": \"8px\",\n                          \"box-shadow\": \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                        },\n                        attrs: { type: \"primary\" },\n                        on: { click: _vm.handleExportTransactions }\n                      },\n                      [\n                        _c(\"a-icon\", {\n                          staticStyle: { \"margin-right\": \"6px\" },\n                          attrs: { type: \"download\" }\n                        }),\n                        _vm._v(\"\\n          导出交易记录\\n        \")\n                      ],\n                      1\n                    )\n                  ]\n                },\n                proxy: true\n              }\n            ])\n          })\n        ],\n        1\n      ),\n      _c(\n        \"a-modal\",\n        {\n          attrs: { title: \"确认充值\", footer: null, width: \"500px\" },\n          model: {\n            value: _vm.showRechargeModal,\n            callback: function($$v) {\n              _vm.showRechargeModal = $$v\n            },\n            expression: \"showRechargeModal\"\n          }\n        },\n        [\n          _c(\"div\", { staticClass: \"recharge-confirm\" }, [\n            _c(\"div\", { staticClass: \"confirm-info\" }, [\n              _c(\"div\", { staticClass: \"info-row\" }, [\n                _c(\"span\", { staticClass: \"info-label\" }, [\n                  _vm._v(\"充值金额：\")\n                ]),\n                _c(\"span\", { staticClass: \"info-value\" }, [\n                  _vm._v(\"¥\" + _vm._s(_vm.finalRechargeAmount))\n                ])\n              ]),\n              _c(\"div\", { staticClass: \"info-row total\" }, [\n                _c(\"span\", { staticClass: \"info-label\" }, [\n                  _vm._v(\"到账金额：\")\n                ]),\n                _c(\"span\", { staticClass: \"info-value\" }, [\n                  _vm._v(\"¥\" + _vm._s(_vm.finalRechargeAmount))\n                ])\n              ])\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"payment-methods\" },\n              [\n                _c(\"h4\", [_vm._v(\"选择支付方式\")]),\n                _c(\n                  \"a-radio-group\",\n                  {\n                    model: {\n                      value: _vm.selectedPaymentMethod,\n                      callback: function($$v) {\n                        _vm.selectedPaymentMethod = $$v\n                      },\n                      expression: \"selectedPaymentMethod\"\n                    }\n                  },\n                  [\n                    _c(\"a-radio\", { attrs: { value: \"alipay\" } }, [\n                      _c(\"span\", { staticClass: \"payment-option\" }, [\n                        _c(\"i\", { staticClass: \"payment-icon alipay\" }),\n                        _vm._v(\"\\n              支付宝\\n            \")\n                      ])\n                    ]),\n                    _c(\"a-radio\", { attrs: { value: \"wechat\" } }, [\n                      _c(\"span\", { staticClass: \"payment-option\" }, [\n                        _c(\"i\", { staticClass: \"payment-icon wechat\" }),\n                        _vm._v(\"\\n              微信支付\\n            \")\n                      ])\n                    ]),\n                    _c(\"a-radio\", { attrs: { value: \"bank\" } }, [\n                      _c(\"span\", { staticClass: \"payment-option\" }, [\n                        _c(\"i\", { staticClass: \"payment-icon bank\" }),\n                        _vm._v(\"\\n              银行卡\\n            \")\n                      ])\n                    ])\n                  ],\n                  1\n                )\n              ],\n              1\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"modal-actions\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    on: {\n                      click: function($event) {\n                        _vm.showRechargeModal = false\n                      }\n                    }\n                  },\n                  [_vm._v(\"取消\")]\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { type: \"primary\", loading: _vm.paymentLoading },\n                    on: { click: _vm.handleConfirmRecharge }\n                  },\n                  [_vm._v(\"\\n          确认支付\\n        \")]\n                )\n              ],\n              1\n            )\n          ])\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          attrs: { title: \"交易详情\", footer: null, width: \"600px\" },\n          model: {\n            value: _vm.showTransactionDetail,\n            callback: function($$v) {\n              _vm.showTransactionDetail = $$v\n            },\n            expression: \"showTransactionDetail\"\n          }\n        },\n        [\n          _vm.selectedTransaction\n            ? _c(\"div\", { staticClass: \"transaction-detail\" }, [\n                _c(\"div\", { staticClass: \"detail-header\" }, [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"transaction-type\",\n                      class: _vm.getTransactionTypeClass(\n                        _vm.selectedTransaction.transactionType\n                      )\n                    },\n                    [\n                      _c(\"i\", {\n                        class: _vm.getTransactionTypeIcon(\n                          _vm.selectedTransaction.transactionType\n                        )\n                      }),\n                      _vm._v(\n                        \"\\n          \" +\n                          _vm._s(\n                            _vm.getTransactionTypeText(\n                              _vm.selectedTransaction.transactionType\n                            )\n                          ) +\n                          \"\\n        \"\n                      )\n                    ]\n                  ),\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"transaction-amount\",\n                      class: _vm.getAmountClass(\n                        _vm.selectedTransaction.transactionType\n                      )\n                    },\n                    [\n                      _vm._v(\n                        \"\\n          \" +\n                          _vm._s(\n                            _vm.formatAmount(\n                              _vm.selectedTransaction.amount,\n                              _vm.selectedTransaction.transactionType\n                            )\n                          ) +\n                          \"\\n        \"\n                      )\n                    ]\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"detail-content\" }, [\n                  _c(\"div\", { staticClass: \"detail-row\" }, [\n                    _c(\"span\", { staticClass: \"detail-label\" }, [\n                      _vm._v(\"交易单号：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"detail-value\" }, [\n                      _vm._v(_vm._s(_vm.selectedTransaction.id))\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"detail-row\" }, [\n                    _c(\"span\", { staticClass: \"detail-label\" }, [\n                      _vm._v(\"交易时间：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"detail-value\" }, [\n                      _vm._v(\n                        _vm._s(\n                          _vm.formatDateTime(\n                            _vm.selectedTransaction.transactionTime\n                          )\n                        )\n                      )\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"detail-row\" }, [\n                    _c(\"span\", { staticClass: \"detail-label\" }, [\n                      _vm._v(\"交易描述：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"detail-value\" }, [\n                      _vm._v(_vm._s(_vm.selectedTransaction.description))\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"detail-row\" }, [\n                    _c(\"span\", { staticClass: \"detail-label\" }, [\n                      _vm._v(\"交易前余额：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"detail-value\" }, [\n                      _vm._v(\n                        \"¥\" +\n                          _vm._s(\n                            _vm.formatNumber(\n                              _vm.selectedTransaction.balanceBefore\n                            )\n                          )\n                      )\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"detail-row\" }, [\n                    _c(\"span\", { staticClass: \"detail-label\" }, [\n                      _vm._v(\"交易后余额：\")\n                    ]),\n                    _c(\"span\", { staticClass: \"detail-value\" }, [\n                      _vm._v(\n                        \"¥\" +\n                          _vm._s(\n                            _vm.formatNumber(\n                              _vm.selectedTransaction.balanceAfter\n                            )\n                          )\n                      )\n                    ])\n                  ]),\n                  _vm.selectedTransaction.relatedOrderId\n                    ? _c(\"div\", { staticClass: \"detail-row\" }, [\n                        _c(\"span\", { staticClass: \"detail-label\" }, [\n                          _vm._v(\"关联订单：\")\n                        ]),\n                        _c(\"span\", { staticClass: \"detail-value\" }, [\n                          _vm._v(_vm._s(_vm.selectedTransaction.relatedOrderId))\n                        ])\n                      ])\n                    : _vm._e()\n                ])\n              ])\n            : _vm._e()\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"page-header\" }, [\n      _c(\"h1\", { staticClass: \"page-title\" }, [_vm._v(\"账户管理\")]),\n      _c(\"p\", { staticClass: \"page-description\" }, [\n        _vm._v(\"管理您的账户余额、查看交易记录和充值\")\n      ])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}