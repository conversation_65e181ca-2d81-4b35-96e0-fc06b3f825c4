{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue?vue&type=style&index=0&id=350f49e2&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue", "mtime": 1753702910988}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.referral-page {\n  padding: 2rem;\n}\n\n.page-header {\n  margin-bottom: 3rem;\n}\n\n.page-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #334155;\n  margin: 0 0 0.5rem 0;\n}\n\n.page-description {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n.referral-content {\n  display: flex;\n  flex-direction: column;\n  gap: 3rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 2rem 0;\n}\n\n/* 推荐统计 */\n.referral-stats {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 2rem;\n}\n\n/* 推荐链接生成 */\n.referral-link-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.link-generator {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 3rem;\n}\n\n.link-display {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.link-input {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.link-actions {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n\n.referral-tips {\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 16px;\n  padding: 2rem;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.referral-tips h4 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 1rem 0;\n}\n\n.referral-tips ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.referral-tips li {\n  margin-bottom: 0.75rem;\n  color: #64748b;\n  line-height: 1.5;\n  position: relative;\n  padding-left: 1.5rem;\n}\n\n.referral-tips li::before {\n  content: '•';\n  color: #7c8aed;\n  font-weight: bold;\n  position: absolute;\n  left: 0;\n}\n\n.referral-tips strong {\n  color: #7c8aed;\n  font-weight: 600;\n}\n\n/* 推荐记录 */\n.referral-records {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.records-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n}\n\n.records-filters {\n  display: flex;\n  gap: 1rem;\n}\n\n.friend-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.friend-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n.friend-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.friend-details {\n  flex: 1;\n}\n\n.friend-name {\n  font-weight: 600;\n  color: #334155;\n  margin-bottom: 0.25rem;\n}\n\n.friend-email {\n  font-size: 0.9rem;\n  color: #64748b;\n  font-family: 'Courier New', monospace;\n}\n\n.reward-amount {\n  font-weight: 600;\n  color: #10b981;\n  font-family: 'Courier New', monospace;\n}\n\n.record-status {\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.status-pending {\n  background: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n}\n\n.status-confirmed {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n}\n\n.status-rewarded {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.record-time {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n/* 奖励提现 */\n.withdrawal-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.withdrawal-card {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 3rem;\n}\n\n.withdrawal-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.balance-display {\n  display: flex;\n  gap: 2rem;\n  padding: 2rem;\n  background: linear-gradient(135deg, rgba(124, 138, 237, 0.1) 0%, rgba(139, 95, 191, 0.1) 100%);\n  border-radius: 16px;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.balance-item {\n  flex: 1;\n  text-align: center;\n}\n\n.balance-label {\n  font-size: 1rem;\n  color: #64748b;\n  margin-bottom: 1rem;\n}\n\n.balance-amount {\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-family: 'Courier New', monospace;\n}\n\n.balance-amount.frozen {\n  background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.withdrawal-status-tip {\n  margin-bottom: 1.5rem;\n}\n\n.withdrawal-disabled {\n  text-align: center;\n  padding: 2rem;\n}\n\n.withdrawal-rules {\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 16px;\n  padding: 1.5rem;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.withdrawal-rules h4 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 1rem 0;\n}\n\n.withdrawal-rules ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.withdrawal-rules li {\n  margin-bottom: 0.5rem;\n  color: #64748b;\n  font-size: 0.9rem;\n  position: relative;\n  padding-left: 1.5rem;\n}\n\n.withdrawal-rules li::before {\n  content: '•';\n  color: #7c8aed;\n  font-weight: bold;\n  position: absolute;\n  left: 0;\n}\n\n.withdrawal-form {\n  background: rgba(124, 138, 237, 0.02);\n  border-radius: 16px;\n  padding: 2rem;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.amount-tips {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 0.5rem;\n  font-size: 0.9rem;\n}\n\n.amount-tips span {\n  color: #64748b;\n}\n\n.amount-tips a {\n  color: #7c8aed;\n  cursor: pointer;\n}\n\n.amount-tips a:hover {\n  text-decoration: underline;\n}\n\n.withdrawal-summary {\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.summary-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.summary-row:last-child {\n  margin-bottom: 0;\n}\n\n.summary-row.total {\n  padding-top: 1rem;\n  border-top: 1px solid rgba(124, 138, 237, 0.1);\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.summary-row span:first-child {\n  color: #64748b;\n}\n\n.summary-row span:last-child {\n  color: #334155;\n  font-weight: 500;\n}\n\n/* 提现记录 */\n.withdrawal-history {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.withdrawal-amount-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.withdrawal-amount-info .amount {\n  font-weight: 600;\n  color: #334155;\n  font-family: 'Courier New', monospace;\n}\n\n.withdrawal-amount-info .fee {\n  font-size: 0.8rem;\n  color: #64748b;\n}\n\n.withdrawal-status {\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.status-processing {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n}\n\n.status-completed {\n  background: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n}\n\n.status-rejected {\n  background: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n}\n\n.withdrawal-time {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n/* 提现确认弹窗 */\n.withdrawal-confirm .confirm-info {\n  margin-bottom: 1.5rem;\n}\n\n.withdrawal-confirm .info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 0;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.withdrawal-confirm .info-row:last-child {\n  border-bottom: none;\n}\n\n.withdrawal-confirm .label {\n  font-weight: 500;\n  color: #64748b;\n}\n\n.withdrawal-confirm .value {\n  font-weight: 600;\n  color: #334155;\n}\n\n.withdrawal-confirm .value.highlight {\n  color: #10b981;\n  font-size: 1.1rem;\n}\n\n.withdrawal-confirm .confirm-checkbox {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.withdrawal-confirm .confirm-actions {\n  text-align: center;\n}\n\n/* 二维码模态框 */\n.qr-modal {\n  text-align: center;\n  padding: 1rem 0;\n}\n\n.qr-code {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  background: rgba(124, 138, 237, 0.05);\n  border-radius: 12px;\n  margin-bottom: 2rem;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.qr-tips {\n  margin-bottom: 2rem;\n}\n\n.qr-tips p {\n  color: #64748b;\n  margin: 0.5rem 0;\n}\n\n.qr-actions {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .referral-stats {\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  .link-generator {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n\n  .withdrawal-card {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .referral-page {\n    padding: 1rem;\n  }\n\n  .referral-stats {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .link-input {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .link-actions {\n    justify-content: center;\n  }\n\n  .records-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n\n  .records-filters {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .balance-amount {\n    font-size: 2rem;\n  }\n\n  .withdrawal-form {\n    padding: 1.5rem;\n  }\n}\n", null]}