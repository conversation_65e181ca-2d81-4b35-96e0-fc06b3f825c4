{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue?vue&type=template&id=033d3257&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue", "mtime": 1753756420429}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"payment-failure-page\">\n  <div class=\"failure-container\">\n    <!-- 失败图标 -->\n    <div class=\"failure-icon\">\n      <a-icon type=\"close-circle\" theme=\"filled\" />\n    </div>\n    \n    <!-- 失败标题 -->\n    <h1 class=\"failure-title\">支付失败</h1>\n    \n    <!-- 失败原因 -->\n    <div class=\"failure-message\">\n      <p>很抱歉，您的支付未能成功完成。</p>\n      <p>可能的原因：</p>\n      <ul class=\"reason-list\">\n        <li>支付过程中网络连接中断</li>\n        <li>支付信息验证失败</li>\n        <li>支付宝账户余额不足</li>\n        <li>银行卡限额或状态异常</li>\n      </ul>\n    </div>\n    \n    <!-- 订单信息 -->\n    <div class=\"order-info\" v-if=\"orderInfo\">\n      <div class=\"info-item\">\n        <span class=\"label\">订单号：</span>\n        <span class=\"value\">{{ orderInfo.orderId }}</span>\n      </div>\n      <div class=\"info-item\" v-if=\"orderInfo.amount\">\n        <span class=\"label\">订单金额：</span>\n        <span class=\"value amount\">¥{{ orderInfo.amount }}</span>\n      </div>\n      <div class=\"info-item\">\n        <span class=\"label\">失败时间：</span>\n        <span class=\"value\">{{ formatTime(new Date()) }}</span>\n      </div>\n    </div>\n    \n    <!-- 操作按钮 -->\n    <div class=\"action-buttons\">\n      <a-button type=\"primary\" size=\"large\" @click=\"retryPayment\">\n        重新支付\n      </a-button>\n      <a-button size=\"large\" @click=\"goToUserCenter\" style=\"margin-left: 16px\">\n        查看订单\n      </a-button>\n      <a-button size=\"large\" @click=\"goHome\" style=\"margin-left: 16px\">\n        返回首页\n      </a-button>\n    </div>\n    \n    <!-- 帮助信息 -->\n    <div class=\"help-info\">\n      <a-alert\n        message=\"需要帮助？\"\n        description=\"如果问题持续存在，请联系客服：400-123-4567 或发送邮件至 <EMAIL>\"\n        type=\"warning\"\n        show-icon\n      />\n    </div>\n  </div>\n</div>\n", null]}