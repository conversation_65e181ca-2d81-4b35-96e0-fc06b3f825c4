<template>
  <div class="payment-failure-page">
    <div class="failure-container">
      <!-- 失败图标 -->
      <div class="failure-icon">
        <a-icon type="close-circle" theme="filled" />
      </div>
      
      <!-- 失败标题 -->
      <h1 class="failure-title">支付失败</h1>
      
      <!-- 失败原因 -->
      <div class="failure-message">
        <p>很抱歉，您的支付未能成功完成。</p>
        <p>可能的原因：</p>
        <ul class="reason-list">
          <li>支付过程中网络连接中断</li>
          <li>支付信息验证失败</li>
          <li>支付宝账户余额不足</li>
          <li>银行卡限额或状态异常</li>
        </ul>
      </div>
      
      <!-- 订单信息 -->
      <div class="order-info" v-if="orderInfo">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderInfo.orderId }}</span>
        </div>
        <div class="info-item" v-if="orderInfo.amount">
          <span class="label">订单金额：</span>
          <span class="value amount">¥{{ orderInfo.amount }}</span>
        </div>
        <div class="info-item">
          <span class="label">失败时间：</span>
          <span class="value">{{ formatTime(new Date()) }}</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button type="primary" size="large" @click="retryPayment">
          重新支付
        </a-button>
        <a-button size="large" @click="goToUserCenter" style="margin-left: 16px">
          查看订单
        </a-button>
        <a-button size="large" @click="goHome" style="margin-left: 16px">
          返回首页
        </a-button>
      </div>
      
      <!-- 帮助信息 -->
      <div class="help-info">
        <a-alert
          message="需要帮助？"
          description="如果问题持续存在，请联系客服：400-123-4567 或发送邮件至 <EMAIL>"
          type="warning"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentFailure',
  data() {
    return {
      orderInfo: null
    }
  },
  
  mounted() {
    this.loadOrderInfo()
  },
  
  methods: {
    loadOrderInfo() {
      // 从URL参数获取订单信息
      const orderId = this.$route.query.orderId
      if (orderId) {
        this.orderInfo = {
          orderId: orderId,
          amount: this.$route.query.amount || null
        }
      }
    },
    
    formatTime(date) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    retryPayment() {
      // 重新发起支付
      this.$router.push('/usercenter/credits')
    },
    
    goToUserCenter() {
      this.$router.push('/usercenter/orders')
    },
    
    goHome() {
      this.$router.push('/home')
    }
  }
}
</script>

<style scoped>
.payment-failure-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.failure-container {
  background: white;
  border-radius: 16px;
  padding: 48px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.failure-icon {
  font-size: 80px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

.failure-title {
  font-size: 32px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32px;
}

.failure-message {
  margin-bottom: 32px;
  color: #595959;
  line-height: 1.6;
  text-align: left;
}

.reason-list {
  margin: 16px 0;
  padding-left: 20px;
}

.reason-list li {
  margin-bottom: 8px;
  color: #8c8c8c;
}

.order-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #8c8c8c;
  font-size: 14px;
}

.value {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.value.amount {
  color: #f5222d;
  font-size: 18px;
  font-weight: 600;
}

.action-buttons {
  margin-bottom: 32px;
}

.help-info {
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .failure-container {
    padding: 32px 24px;
  }
  
  .failure-title {
    font-size: 24px;
  }
  
  .failure-icon {
    font-size: 60px;
  }
  
  .action-buttons .ant-btn {
    margin: 8px 4px !important;
  }
}
</style>
