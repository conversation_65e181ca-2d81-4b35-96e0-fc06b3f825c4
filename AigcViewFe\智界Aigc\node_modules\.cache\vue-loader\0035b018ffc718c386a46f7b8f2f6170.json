{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue?vue&type=template&id=1e174e22&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue", "mtime": 1753687927441}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"usercenter-sidebar\" :style=\"{ top: sidebarTop + 'px' }\">\n  <!-- 用户信息卡片 -->\n  <div class=\"user-info-card\">\n    <div class=\"user-avatar\">\n      <img :src=\"avatarUrl\" :alt=\"(userInfo && userInfo.nickname) || '用户头像'\" />\n      <div class=\"avatar-status\" :class=\"{ online: isOnline }\"></div>\n    </div>\n    <div class=\"user-details\">\n      <h3 class=\"user-name\">{{ (userInfo && userInfo.nickname) || '智界用户' }}</h3>\n      <div class=\"user-level\">\n        <span class=\"level-badge\" :class=\"memberLevelClass\">\n          {{ memberLevelText }}\n        </span>\n      </div>\n      <!-- 账户余额显示 -->\n      <div class=\"user-balance\">\n        <div class=\"balance-label\">账户余额</div>\n        <div class=\"balance-amount\">¥{{ formatBalance((userInfo && userInfo.accountBalance) || 0) }}</div>\n        <button class=\"balance-btn-mini\" @click=\"handleRecharge\">充值</button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 导航菜单 -->\n  <nav class=\"sidebar-nav\">\n    <ul class=\"nav-menu\">\n      <li \n        v-for=\"item in menuItems\" \n        :key=\"item.key\"\n        class=\"nav-item\"\n      >\n        <a \n          href=\"#\"\n          class=\"nav-link sidebar-menu-item\"\n          :class=\"{ active: currentPage === item.key }\"\n          @click.prevent=\"handleMenuClick(item)\"\n        >\n          <i class=\"nav-icon\" :class=\"item.icon\"></i>\n          <span class=\"nav-text\">{{ item.title }}</span>\n          <span v-if=\"item.badge\" class=\"nav-badge\">{{ item.badge }}</span>\n        </a>\n      </li>\n    </ul>\n  </nav>\n\n\n\n\n</div>\n", null]}