{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue?vue&type=template&id=350f49e2&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\views\\Referral.vue", "mtime": 1753702910988}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"referral-page\"},[_vm._m(0),_c('div',{staticClass:\"referral-content\"},[_c('div',{staticClass:\"referral-stats\"},[_c('StatsCard',{attrs:{\"value\":_vm.referralStats.totalReferrals,\"label\":\"推荐人数\",\"icon\":\"anticon anticon-team\",\"icon-color\":\"#7c8aed\",\"loading\":_vm.loading}}),_c('StatsCard',{attrs:{\"value\":_vm.referralStats.totalRewards,\"unit\":\"元\",\"label\":\"累计奖励\",\"icon\":\"anticon anticon-gift\",\"icon-color\":\"#10b981\",\"loading\":_vm.loading}}),_c('StatsCard',{attrs:{\"value\":_vm.referralStats.availableRewards,\"unit\":\"元\",\"label\":\"可提现金额\",\"icon\":\"anticon anticon-wallet\",\"icon-color\":\"#f59e0b\",\"loading\":_vm.loading}}),_c('StatsCard',{attrs:{\"value\":_vm.referralStats.monthlyReferrals,\"label\":\"本月推荐\",\"icon\":\"anticon anticon-calendar\",\"icon-color\":\"#ef4444\",\"trend\":_vm.monthlyTrend,\"loading\":_vm.loading}})],1),_c('div',{staticClass:\"referral-link-section\"},[_c('h3',{staticClass:\"section-title\"},[_vm._v(\"我的推荐链接\")]),_c('div',{staticClass:\"link-generator\"},[_c('div',{staticClass:\"link-display\"},[_c('div',{staticClass:\"link-input\"},[_c('a-input',{attrs:{\"value\":_vm.referralLink,\"readonly\":\"\",\"size\":\"large\",\"placeholder\":\"点击生成推荐链接\"}}),_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"loading\":_vm.linkLoading},on:{\"click\":_vm.handleCopyLink}},[_c('i',{staticClass:\"anticon anticon-copy\"}),_vm._v(\"\\n              复制链接\\n            \")])],1),_c('div',{staticClass:\"link-actions\"},[_c('a-button',{attrs:{\"loading\":_vm.linkLoading},on:{\"click\":_vm.handleGenerateLink}},[_c('i',{staticClass:\"anticon anticon-reload\"}),_vm._v(\"\\n              重新生成\\n            \")]),_c('a-button',{on:{\"click\":_vm.handleGenerateQRCode}},[_c('i',{staticClass:\"anticon anticon-qrcode\"}),_vm._v(\"\\n              生成二维码\\n            \")]),_c('a-button',{on:{\"click\":_vm.handleShareToSocial}},[_c('i',{staticClass:\"anticon anticon-share-alt\"}),_vm._v(\"\\n              分享到社交媒体\\n            \")])],1)]),_c('div',{staticClass:\"referral-tips\"},[_c('h4',[_vm._v(\"推荐奖励规则\")]),_c('ul',[_c('li',[_vm._v(\"好友通过您的链接注册并订阅会员，您可获得其订阅金额的 \"),_c('strong',[_vm._v(_vm._s(_vm.currentCommissionRate)+\"%\")]),_vm._v(\" 佣金\")]),_c('li',[_vm._v(\"普通用户：\"+_vm._s(_vm.normalRate)+\"% 基础佣金，邀请10人升至\"+_vm._s(_vm.normalHighRate)+\"%，邀请30人升至\"+_vm._s(_vm.normalTopRate)+\"%\")]),_c('li',[_vm._v(\"VIP会员：\"+_vm._s(_vm.vipRate)+\"% 基础佣金，邀请10人升至\"+_vm._s(_vm.vipHighRate)+\"%，邀请30人升至\"+_vm._s(_vm.vipTopRate)+\"%\")]),_c('li',[_vm._v(\"SVIP会员：直接享受 \"+_vm._s(_vm.svipRate)+\"% 最高佣金\")]),_c('li',[_vm._v(\"佣金将在好友完成订阅后24小时内到账\")]),_c('li',[_vm._v(\"累计佣金满100元即可申请提现\")])])])])]),_c('div',{staticClass:\"referral-records\"},[_c('div',{staticClass:\"records-header\"},[_c('h3',{staticClass:\"section-title\"},[_vm._v(\"推荐记录\")]),_c('div',{staticClass:\"records-filters\"},[_c('a-select',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"状态筛选\"},on:{\"change\":_vm.handleRecordFilterChange},model:{value:(_vm.recordFilters.status),callback:function ($$v) {_vm.$set(_vm.recordFilters, \"status\", $$v)},expression:\"recordFilters.status\"}},[_c('a-select-option',{attrs:{\"value\":\"\"}},[_vm._v(\"全部状态\")]),_c('a-select-option',{attrs:{\"value\":\"pending\"}},[_vm._v(\"待确认\")]),_c('a-select-option',{attrs:{\"value\":\"confirmed\"}},[_vm._v(\"已确认\")]),_c('a-select-option',{attrs:{\"value\":\"rewarded\"}},[_vm._v(\"已奖励\")])],1),_c('a-range-picker',{staticStyle:{\"width\":\"240px\"},on:{\"change\":_vm.handleRecordFilterChange},model:{value:(_vm.recordFilters.dateRange),callback:function ($$v) {_vm.$set(_vm.recordFilters, \"dateRange\", $$v)},expression:\"recordFilters.dateRange\"}})],1)]),_c('a-table',{attrs:{\"columns\":_vm.recordColumns,\"data-source\":_vm.referralRecords,\"loading\":_vm.recordLoading,\"pagination\":_vm.recordPagination,\"row-key\":\"id\"},on:{\"change\":_vm.handleRecordTableChange},scopedSlots:_vm._u([{key:\"friendInfo\",fn:function(ref){\nvar record = ref.record;\nreturn [_c('div',{staticClass:\"friend-info\"},[_c('div',{staticClass:\"friend-avatar\"},[_c('img',{attrs:{\"src\":record.friendAvatar || _vm.defaultAvatar,\"alt\":record.friendNickname}})]),_c('div',{staticClass:\"friend-details\"},[_c('div',{staticClass:\"friend-name\"},[_vm._v(_vm._s(record.friendNickname || '新用户'))]),_c('div',{staticClass:\"friend-email\"},[_vm._v(_vm._s(_vm.maskEmail(record.friendEmail)))])])])]}},{key:\"rewardAmount\",fn:function(ref){\nvar text = ref.text;\nreturn [_c('span',{staticClass:\"reward-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(text)))])]}},{key:\"status\",fn:function(ref){\nvar text = ref.text;\nreturn [_c('span',{staticClass:\"record-status\",class:_vm.getRecordStatusClass(text)},[_vm._v(\"\\n            \"+_vm._s(_vm.getRecordStatusText(text))+\"\\n          \")])]}},{key:\"time\",fn:function(ref){\nvar text = ref.text;\nreturn [_c('span',{staticClass:\"record-time\"},[_vm._v(_vm._s(_vm.formatDateTime(text)))])]}}])})],1),_c('div',{staticClass:\"withdrawal-section\"},[_c('h3',{staticClass:\"section-title\"},[_vm._v(\"奖励提现\")]),_c('div',{staticClass:\"withdrawal-card\"},[_c('div',{staticClass:\"withdrawal-info\"},[_c('div',{staticClass:\"balance-display\"},[_c('div',{staticClass:\"balance-item\"},[_c('div',{staticClass:\"balance-label\"},[_vm._v(\"可提现余额\")]),_c('div',{staticClass:\"balance-amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalInfo.availableAmount || 0)))])]),_c('div',{staticClass:\"balance-item\"},[_c('div',{staticClass:\"balance-label\"},[_vm._v(\"冻结金额\")]),_c('div',{staticClass:\"balance-amount frozen\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalInfo.frozenAmount || 0)))])])]),(_vm.withdrawalInfo.hasPendingRequest)?_c('div',{staticClass:\"withdrawal-status-tip\"},[_c('a-alert',{attrs:{\"message\":\"您有提现申请正在审核中\",\"description\":(\"申请金额：¥\" + (_vm.formatNumber(_vm.withdrawalInfo.pendingAmount)) + \"，申请时间：\" + (_vm.formatDateTime(_vm.withdrawalInfo.pendingTime))),\"type\":\"info\",\"show-icon\":\"\"}})],1):_vm._e(),_vm._m(1)]),(_vm.withdrawalInfo.canWithdraw)?_c('div',{staticClass:\"withdrawal-form\"},[_c('a-form',{attrs:{\"layout\":\"vertical\"}},[_c('a-form-item',{attrs:{\"label\":\"提现金额\"}},[_c('a-input-number',{staticStyle:{\"width\":\"100%\"},attrs:{\"min\":50,\"max\":_vm.withdrawalInfo.availableAmount,\"step\":10,\"placeholder\":\"请输入提现金额\",\"size\":\"large\"},model:{value:(_vm.withdrawalAmount),callback:function ($$v) {_vm.withdrawalAmount=$$v},expression:\"withdrawalAmount\"}}),_c('div',{staticClass:\"amount-tips\"},[_c('span',[_vm._v(\"最低50元\")]),_c('a',{on:{\"click\":_vm.setMaxAmount}},[_vm._v(\"全部提现\")])])],1),_c('a-form-item',{attrs:{\"label\":\"真实姓名\"}},[_c('a-input',{attrs:{\"placeholder\":\"请输入真实姓名\",\"size\":\"large\"},model:{value:(_vm.realName),callback:function ($$v) {_vm.realName=$$v},expression:\"realName\"}})],1),_c('a-form-item',{attrs:{\"label\":\"支付宝账号\"}},[_c('a-input',{attrs:{\"placeholder\":\"请输入支付宝账号（手机号）\",\"size\":\"large\"},model:{value:(_vm.alipayAccount),callback:function ($$v) {_vm.alipayAccount=$$v},expression:\"alipayAccount\"}})],1),_c('div',{staticClass:\"withdrawal-summary\"},[_c('div',{staticClass:\"summary-row\"},[_c('span',[_vm._v(\"提现金额：\")]),_c('span',[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalAmount)))])]),_c('div',{staticClass:\"summary-row total\"},[_c('span',[_vm._v(\"实际到账：\")]),_c('span',[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalAmount)))])])]),_c('a-button',{attrs:{\"type\":\"primary\",\"size\":\"large\",\"disabled\":!_vm.isFormValid,\"loading\":_vm.withdrawalLoading,\"block\":\"\"},on:{\"click\":_vm.showConfirmModal}},[_vm._v(\"\\n              申请提现\\n            \")])],1)],1):_c('div',{staticClass:\"withdrawal-disabled\"},[_c('a-alert',{attrs:{\"message\":_vm.withdrawalInfo.message,\"type\":\"warning\",\"show-icon\":\"\"}})],1)])]),_c('div',{staticClass:\"withdrawal-history\"},[_c('h3',{staticClass:\"section-title\"},[_vm._v(\"提现记录\")]),_c('a-table',{attrs:{\"columns\":_vm.withdrawalColumns,\"data-source\":_vm.withdrawalHistory,\"loading\":_vm.withdrawalHistoryLoading,\"pagination\":_vm.withdrawalPagination,\"row-key\":\"id\"},on:{\"change\":_vm.handleWithdrawalTableChange},scopedSlots:_vm._u([{key:\"amount\",fn:function(text, record){return [_c('span',{staticClass:\"amount\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(record.withdrawal_amount)))])]}},{key:\"status\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getWithdrawalStatusColor(record.status)}},[_vm._v(\"\\n            \"+_vm._s(record.statusText || _vm.getWithdrawalStatusText(record.status, record.review_remark))+\"\\n          \")])]}},{key:\"time\",fn:function(text){return [_c('span',{staticClass:\"time-text\"},[_vm._v(_vm._s(_vm.formatDateTime(text)))])]}}])})],1)]),_c('a-modal',{attrs:{\"title\":\"确认提现信息\",\"footer\":null,\"width\":\"500px\"},model:{value:(_vm.showConfirmWithdrawal),callback:function ($$v) {_vm.showConfirmWithdrawal=$$v},expression:\"showConfirmWithdrawal\"}},[_c('div',{staticClass:\"withdrawal-confirm\"},[_c('a-alert',{staticStyle:{\"margin-bottom\":\"20px\"},attrs:{\"message\":\"请仔细核对提现信息，提交后无法修改\",\"type\":\"warning\",\"show-icon\":\"\"}}),_c('div',{staticClass:\"confirm-info\"},[_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"提现金额：\")]),_c('span',{staticClass:\"value\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalAmount)))])]),_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"真实姓名：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.realName))])]),_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"支付宝账号：\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.alipayAccount))])]),_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"实际到账：\")]),_c('span',{staticClass:\"value highlight\"},[_vm._v(\"¥\"+_vm._s(_vm.formatNumber(_vm.withdrawalAmount)))])])]),_c('div',{staticClass:\"confirm-checkbox\"},[_c('a-checkbox',{model:{value:(_vm.confirmChecked),callback:function ($$v) {_vm.confirmChecked=$$v},expression:\"confirmChecked\"}},[_vm._v(\"\\n          我已仔细核对以上信息，确认无误\\n        \")])],1),_c('div',{staticClass:\"confirm-actions\"},[_c('a-button',{staticStyle:{\"margin-right\":\"10px\"},on:{\"click\":function($event){_vm.showConfirmWithdrawal = false}}},[_vm._v(\"\\n          取消\\n        \")]),_c('a-button',{attrs:{\"type\":\"primary\",\"disabled\":!_vm.confirmChecked,\"loading\":_vm.withdrawalLoading},on:{\"click\":_vm.handleConfirmWithdrawal}},[_vm._v(\"\\n          确认提交\\n        \")])],1)],1)]),_c('a-modal',{attrs:{\"title\":\"推荐二维码\",\"footer\":null,\"width\":\"400px\"},model:{value:(_vm.showQRModal),callback:function ($$v) {_vm.showQRModal=$$v},expression:\"showQRModal\"}},[_c('div',{staticClass:\"qr-modal\"},[_c('div',{ref:\"qrCodeContainer\",staticClass:\"qr-code\"}),_c('div',{staticClass:\"qr-tips\"},[_c('p',[_vm._v(\"扫描二维码或分享链接给好友\")]),_c('p',[_vm._v(\"好友注册并充值后您将获得奖励\")])]),_c('div',{staticClass:\"qr-actions\"},[_c('a-button',{on:{\"click\":_vm.handleDownloadQR}},[_vm._v(\"下载二维码\")]),_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleCopyLink}},[_vm._v(\"复制链接\")])],1)])])],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"page-header\"},[_c('h1',{staticClass:\"page-title\"},[_vm._v(\"推荐奖励\")]),_c('p',{staticClass:\"page-description\"},[_vm._v(\"邀请好友注册，获得丰厚奖励\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"withdrawal-rules\"},[_c('h4',[_vm._v(\"提现规则\")]),_c('ul',[_c('li',[_vm._v(\"最低提现金额：50元\")]),_c('li',[_vm._v(\"支付宝账号：仅支持手机号\")]),_c('li',[_vm._v(\"无手续费\")]),_c('li',[_vm._v(\"到账时间：1-3个工作日\")]),_c('li',[_vm._v(\"同一时间只能有一个待审核申请\")])])])}]\n\nexport { render, staticRenderFns }"]}