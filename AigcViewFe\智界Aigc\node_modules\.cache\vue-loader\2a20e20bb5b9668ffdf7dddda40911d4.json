{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\layout\\Header.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\layout\\Header.vue", "mtime": 1753672716010}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { gsap } from 'gsap'\nimport LogoImage from '@/components/common/LogoImage.vue'\n\nexport default {\n  name: 'Header',\n  components: {\n    LogoImage\n  },\n  data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [\n        { name: '首页', href: '#home', icon: 'home' },\n        { name: '商城', href: '#market', icon: 'shop' },\n        { name: '客户案例', href: '#cases', icon: 'trophy' },\n        { name: '教程中心', href: '#tutorials', icon: 'book' },\n        { name: '签到奖励', href: '#signin', icon: 'gift' },\n        { name: '订阅会员', href: '#membership', icon: 'crown' },\n        { name: '邀请奖励', href: '#affiliate', icon: 'team' },\n        { name: '个人中心', href: '#usercenter', icon: 'user' }\n      ]\n    }\n  },\n  mounted() {\n    this.initScrollListener()\n    this.initNavbarAnimations()\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  methods: {\n    initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll)\n    },\n    handleScroll() {\n      this.isScrolled = window.scrollY > 50\n    },\n    toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen\n    },\n    initNavbarAnimations() {\n      // 导航栏快速入场动画 - 同时出现\n      gsap.from([this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions], {\n        duration: 0.4,\n        y: -20,\n        opacity: 0,\n        ease: \"power2.out\",\n        stagger: 0.05 // 几乎同时出现\n      })\n    }\n  }\n}\n", null]}