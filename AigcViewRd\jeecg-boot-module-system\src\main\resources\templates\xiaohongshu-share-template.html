<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{{TITLE}} - 小红书内容预览</title>
    <!-- 🔥 引入本地二维码生成库，提升生成速度 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            /* 禁用双击缩放 */
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            /* 禁用触摸缩放 */
            touch-action: manipulation;
        }

        body {
            font-family: '思源雅黑', 'Source <PERSON> CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
            /* 额外的禁用缩放样式 */
            overflow-x: hidden;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        .container {
            width: 98vw;
            max-width: 800px;
            height: 95vh;
            margin: 0 auto;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15), 0 10px 20px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            backdrop-filter: blur(10px);
        }

        /* 头部区域 */
        .header {
            text-align: center;
            padding: 16px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            flex-shrink: 0;
        }

        .website-url {
            font-size: 16px;
            font-weight: 700;
            color: #ff2442;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border: 1px solid #ffcccc;
        }

        .website-url:hover {
            color: #ff1a3d;
            background: linear-gradient(135deg, #ffe6e6 0%, #ffd6d6 100%);
            border-color: #ff9999;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 36, 66, 0.2);
        }

        .brand-title {
            font-size: 20px;
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 8px;
            letter-spacing: 1px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 内容区域 */
        .content-area {
            display: flex;
            flex-direction: column;
            flex: 1;
            overflow: hidden;
        }

        /* 媒体容器 */
        .media-container {
            flex: 0 0 45%;
            margin-bottom: 20px;
            position: relative;
        }

        .images-carousel {
            position: relative;
            width: calc(100% - 40px);
            height: 100%;
            border-radius: 16px;
            overflow: hidden;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            margin: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .carousel-slides {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .image-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-slide.active {
            opacity: 1;
        }

        .image-slide img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            display: block;
            margin: 0 auto;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-slide img:hover {
            transform: scale(1.02);
        }

        /* 轮播控制按钮 */
        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.9);
            color: #333;
            border: none;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10;
        }

        .carousel-nav:hover {
            background: rgba(255,255,255,1);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .carousel-nav:active {
            transform: translateY(-50%) scale(0.95);
        }

        .carousel-nav.prev {
            left: 15px;
        }

        .carousel-nav.next {
            right: 15px;
        }

        /* 轮播指示器 */
        .carousel-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin: 10px 20px;
            flex-shrink: 0;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #d0d0d0;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .carousel-dot.active {
            background: #ff2442;
            transform: scale(1.2);
        }

        /* 滑动提示 */
        .swipe-hint {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0.8;
            animation: fadeInOut 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        /* 可滚动的文字内容区域 */
        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px 20px;
            min-height: 0;
        }

        .note-title {
            font-size: 18px;
            font-weight: 700;
            color: #1a202c;
            margin-top: 20px;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        .note-content {
            font-size: 15px;
            color: #4a5568;
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 元信息 */
        .meta-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #6c757d;
            flex-shrink: 0;
        }

        /* 操作区域 */
        .action-area {
            padding: 20px;
            text-align: center;
            flex-shrink: 0;
            background: white;
        }

        /* 让 expire-notice 在父容器中居中 */
        .action-area .expire-notice {
            margin-left: auto;
            margin-right: auto;
        }

        /* 图片放大模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background: rgba(0, 0, 0, 0.9);
        }

        .image-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .modal-close {
            position: absolute;
            top: -50px;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: white;
            transform: scale(1.1);
        }

        .modal-image-container {
            position: relative;
            max-width: 100%;
            max-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10001;
        }

        .modal-nav:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
        }

        .modal-nav.prev {
            left: -70px;
        }

        .modal-nav.next {
            right: -70px;
        }

        .modal-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
        }

        .modal-dots .carousel-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .modal-dots .carousel-dot.active {
            background: white;
            transform: scale(1.2);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .modal-close {
                top: -40px;
                width: 35px;
                height: 35px;
                font-size: 20px;
            }

            .modal-nav {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .modal-nav.prev {
                left: -50px;
            }

            .modal-nav.next {
                right: -50px;
            }

            .modal-content {
                max-width: 95%;
                max-height: 95%;
            }
        }

        /* 🔥 分享次数提示样式 */
        .share-attempts-info {
            margin-top: 8px;
            margin-bottom: 12px;
            text-align: center;
        }

        .attempts-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        /* 不同状态的文字颜色 */
        .share-attempts-info.success .attempts-text {
            color: #28a745;
        }

        .share-attempts-info.warning .attempts-text {
            color: #ffc107;
        }

        .share-attempts-info.danger .attempts-text {
            color: #dc3545;
        }

        .share-btn {
            width: 280px;
            height: 48px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 36, 66, 0.3);
            margin-bottom: 12px;
        }

        .share-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 36, 66, 0.4);
        }

        .device-tip {
            font-size: 12px;
            color: #999;
            margin-bottom: 16px;
        }

        .footer-watermark {
            font-size: 11px;
            color: #bbb;
            font-weight: 500;
        }

        .expire-notice {
            font-size: 12px;
            color: #ff6b6b;
            margin-top: 8px;
            padding: 6px 12px;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 107, 107, 0.2);
            display: inline-block;
            font-weight: 500;
            margin-bottom: 12px;
            width: auto;
            clear: both;
        }

        /* 二维码弹窗 */
        .qr-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
        }

        .qr-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .qr-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: relative;
            transform: scale(0.9);
            transition: transform 0.2s ease;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .qr-modal.show .qr-content {
            transform: scale(1);
        }

        .qr-close {
            position: absolute;
            top: 12px;
            right: 12px;
            background: #f8f9fa;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .qr-close:hover {
            background: #e9ecef;
            color: #333;
            transform: scale(1.1);
        }

        .qr-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            margin-top: 12px;
        }

        .qr-subtitle {
            font-size: 15px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .qr-code {
            width: 320px;
            height: 320px;
            background: #fafafa;
            border-radius: 16px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
        }

        .qr-tips {
            font-size: 13px;
            color: #999;
            line-height: 1.4;
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 10px;
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="brand-title">智界AIGC</div>
            <a href="https://www.aigcview.com" target="_blank" class="website-url">👆 点击访问 智界AIGC</a>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 媒体容器 -->
            <div class="media-container" id="mediaContainer">
                <div class="images-carousel" id="imagesCarousel" {{IMAGE_CONTAINER_STYLE}}>
                    <div class="carousel-slides" id="carouselSlides">
                        {{IMAGES_HTML}}
                    </div>
                    <!-- 轮播控制按钮 -->
                    <button class="carousel-nav prev" onclick="prevImage()">‹</button>
                    <button class="carousel-nav next" onclick="nextImage()">›</button>
                    <div class="swipe-hint" id="swipeHint">👈 滑动查看更多图片 👉</div>
                </div>
            </div>

            <!-- 轮播指示器 -->
            <div class="carousel-dots" id="carouselDots"></div>

            <!-- 元信息 -->
            <div class="meta-info">
                <span>图文笔记</span>
                <span>{{IMAGE_COUNT}}张图片</span>
            </div>

            <!-- 可滚动的文字内容区域 -->
            <div class="scrollable-content">
                <div class="note-title">{{TITLE}}</div>
                <div class="note-content">{{CONTENT}}</div>
            </div>
        </div>

        <!-- 操作区域 -->
        <div class="action-area">
            <button class="share-btn" id="shareBtn" onclick="publishToXiaohongshu()" onmouseenter="handleButtonHover()">
                🚀 发布到小红书
            </button>
            <br>
            <!-- 🔥 分享次数提示区域 -->
            <div class="expire-notice" id="combinedNotice">
                ⏰ 本页面将在24小时后失效 | <span id="attemptsText">检查分享次数中...</span>
            </div>


        </div>
    </div>

    <!-- 图片放大模态框 -->
    <div class="image-modal" id="imageModal">
        <div class="modal-backdrop" onclick="closeImageModal()"></div>
        <div class="modal-content">
            <button class="modal-close" onclick="closeImageModal()">×</button>
            <div class="modal-image-container">
                <img class="modal-image" id="modalImage" src="" alt="放大图片">
                <button class="modal-nav prev" onclick="prevModalImage()">‹</button>
                <button class="modal-nav next" onclick="nextModalImage()">›</button>
            </div>
            <div class="modal-dots" id="modalDots"></div>
        </div>
    </div>

    <!-- 二维码弹窗 -->
    <div class="qr-modal" id="qrModal">
        <div class="qr-content">
            <button class="qr-close" onclick="closeQrModal()">×</button>
            <div class="qr-title">📱 手机扫码发布</div>
            <div class="qr-subtitle">
                请使用手机扫描下方二维码<br>
                在手机端完成小红书发布
            </div>
            <div class="qr-code" id="qrCodeContainer">
                <div style="color: #999;">正在生成二维码...</div>
            </div>
            <div class="qr-tips">
                💡 扫码后将自动跳转到小红书APP<br>
                如未安装APP，将打开小红书网页版
            </div>
        </div>
    </div>

    <!-- 引入小红书官方JS SDK -->
    <script src="https://fe-static.xhscdn.com/biz-static/goten/xhs-1.0.1.js"></script>



    <script>
        // 全局变量
        let currentImageIndex = 0;
        let totalImages = {{TOTAL_IMAGES}};

        // 🔥 分享次数管理（从后端获取真实状态）
        let shareAttempts = {{CURRENT_ATTEMPTS}};           // 当前已尝试次数（真实值）
        let maxShareAttempts = {{MAX_ATTEMPTS}};            // 最大允许尝试次数（真实值）
        let isPageShared = {{IS_SHARED}};                   // 页面是否已成功分享（真实值）

        // 分享配置 - 从服务端传递
        const shareConfig = {
            type: 'normal',
            pageId: '{{PAGE_ID}}',
            title: {{TITLE_JSON}},
            content: {{CONTENT_JSON}},
            images: {{IMAGES_JSON}}
        };

        // 设备检测函数
        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
            return mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   window.innerWidth <= 768;
        }

        // 🔥 安卓微信环境检测函数（增强版）
        function isAndroidWechat() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isAndroid = userAgent.includes('android');
            const isWechat = userAgent.includes('micromessenger');
            const isQQ = userAgent.includes('qq/') && userAgent.includes('android');

            // 记录检测结果
            if (isAndroid && isWechat) {
                console.log('🤖 检测到安卓微信环境');
                return true;
            } else if (isAndroid && isQQ) {
                console.log('🤖 检测到安卓QQ环境');
                return true; // QQ环境也需要特殊处理
            }

            return false;
        }

        // 🔥 检测是否为iOS环境
        function isIOSDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /iphone|ipad|ipod/.test(userAgent);
        }

        // 🔥 检测是否为微信环境（不区分平台）
        function isWechatBrowser() {
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('micromessenger');
        }

        // 图片轮播控制
        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + totalImages) % totalImages;
            updateCarousel();
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % totalImages;
            updateCarousel();
        }

        function goToImage(index) {
            currentImageIndex = index;
            updateCarousel();
        }

        function updateCarousel() {
            const slides = document.querySelectorAll('.image-slide');
            const dots = document.querySelectorAll('.carousel-dot');

            slides.forEach((slide, index) => {
                slide.classList.toggle('active', index === currentImageIndex);
            });

            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentImageIndex);
            });
        }

        // 发布到小红书主函数
        async function publishToXiaohongshu() {
            // 🔥 检查分享次数限制
            if (shareAttempts >= maxShareAttempts) {
                alert(`❌ 分享次数已用完（${maxShareAttempts}次），请联系客服或刷新页面重试`);
                return;
            }

            if (isPageShared) {
                alert('✅ 此内容已经分享成功，无需重复分享');
                return;
            }

            console.log(`🚀 开始第${shareAttempts + 1}次分享尝试...`);

            const shareTitle = shareConfig.title || '来自COZE插件的分享';
            const shareContent = shareConfig.content || '精彩内容分享';
            const images = shareConfig.images || [];

            console.log('📝 准备分享内容:', { title: shareTitle, content: shareContent, images });

            // 设备检测和分流处理
            if (isMobileDevice()) {
                if (isAndroidWechat()) {
                    console.log('🤖 检测到安卓微信环境，显示浏览器切换提示');
                    showAndroidWechatTip();
                } else {
                    console.log('📱 检测到移动设备，使用官方SDK直接分享');
                    await shareWithOfficialSDK(shareTitle, shareContent, images);
                }
            } else {
                console.log('💻 检测到桌面设备，显示二维码引导扫码');
                showQrModal();
            }
        }

        // 图片放大功能
        function openImageModal(imageIndex) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const slides = document.querySelectorAll('.image-slide img');

            if (slides[imageIndex]) {
                currentImageIndex = imageIndex;
                modalImage.src = slides[imageIndex].src;
                modal.classList.add('show');
                document.body.style.overflow = 'hidden'; // 防止背景滚动

                // 初始化模态框指示器
                initModalDots();
                updateModalDots();
            }
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
            document.body.style.overflow = ''; // 恢复滚动
        }

        function prevModalImage() {
            currentImageIndex = (currentImageIndex - 1 + totalImages) % totalImages;
            updateModalImage();
        }

        function nextModalImage() {
            currentImageIndex = (currentImageIndex + 1) % totalImages;
            updateModalImage();
        }

        function updateModalImage() {
            const modalImage = document.getElementById('modalImage');
            const slides = document.querySelectorAll('.image-slide img');

            if (slides[currentImageIndex]) {
                modalImage.src = slides[currentImageIndex].src;
                updateModalDots();
                // 同步更新主轮播图
                updateCarousel();
            }
        }

        function initModalDots() {
            const modalDotsContainer = document.getElementById('modalDots');
            if (!modalDotsContainer || totalImages <= 1) return;

            modalDotsContainer.innerHTML = '';
            for (let i = 0; i < totalImages; i++) {
                const dot = document.createElement('div');
                dot.className = 'carousel-dot' + (i === currentImageIndex ? ' active' : '');
                dot.onclick = () => goToModalImage(i);
                modalDotsContainer.appendChild(dot);
            }
        }

        function updateModalDots() {
            const modalDots = document.querySelectorAll('#modalDots .carousel-dot');
            modalDots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentImageIndex);
            });
        }

        function goToModalImage(index) {
            currentImageIndex = index;
            updateModalImage();
        }

        // 键盘事件支持
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (modal.classList.contains('show')) {
                switch(e.key) {
                    case 'Escape':
                        closeImageModal();
                        break;
                    case 'ArrowLeft':
                        prevModalImage();
                        break;
                    case 'ArrowRight':
                        nextModalImage();
                        break;
                }
            }
        });

        // 移动端：使用官方SDK分享
        async function shareWithOfficialSDK(title, content, images) {
            try {
                console.log('🚀 开始分享流程');

                if (typeof xhs === 'undefined') {
                    console.log('❌ 小红书JS SDK未加载');
                    alert('小红书SDK加载失败，请刷新页面重试');
                    return;
                }
                console.log('✅ 小红书SDK已加载');

                // 🔥 增加尝试次数
                shareAttempts++;
                updateShareButtonText();

                // 获取服务端生成的签名
                console.log(`🔐 正在获取第${shareAttempts}次分享签名...`);
                const signatureResponse = await fetch('/jeecg-boot/api/aigc/xiaohongshu/get-fresh-signature', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pageId: shareConfig.pageId
                    })
                });

                if (!signatureResponse.ok) {
                    console.log(`❌ 签名请求失败: ${signatureResponse.status}`);
                    if (signatureResponse.status === 403) {
                        showExpiredPage('page_expired', '页面已失效，无法重复使用');
                        return;
                    }
                    if (signatureResponse.status === 429) {
                        alert('❌ 分享次数已达上限，请联系客服');
                        disableShareButton();
                        return;
                    }
                    throw new Error('签名获取失败: ' + signatureResponse.status);
                }

                const signatureData = await signatureResponse.json();
                console.log('✅ 签名获取成功', signatureData.result);

                // 🔥 更新次数信息
                if (signatureData.result.currentAttempts) {
                    shareAttempts = signatureData.result.currentAttempts;
                    maxShareAttempts = signatureData.result.maxAttempts || 3;
                }

                // � 不再立即禁用按钮，等待分享结果

                // 调用小红书官方JS SDK
                console.log('📱 调用小红书官方SDK...');
                const shareInfo = {
                    type: 'normal',
                    title: title,
                    content: content,
                    images: images
                };
                console.log('📝 分享内容', shareInfo);
                console.log('🔐 验证配置', signatureData.result);

                xhs.share({
                    shareInfo: shareInfo,
                    verifyConfig: signatureData.result,
                    success: (result) => {
                        console.log(`🎉 第${shareAttempts}次分享成功！`, result);

                        // 🔥 分享成功，立即禁用并确认
                        isPageShared = true;
                        updateShareButtonText();
                        disableShareButton();

                        // 🔥 调用后端确认分享成功
                        confirmShareSuccess();

                        alert('🎉 分享成功！内容已发布到小红书');
                    },
                    fail: (e) => {
                        console.log(`❌ 第${shareAttempts}次分享失败`, e);

                        // 🔥 分享失败，检查剩余次数
                        const remainingAttempts = maxShareAttempts - shareAttempts;

                        if (remainingAttempts > 0) {
                            alert(`❌ 分享失败: ${e.message || '未知错误'}\n\n您还有 ${remainingAttempts} 次尝试机会`);
                            updateShareButtonText();
                        } else {
                            alert(`❌ 分享失败: ${e.message || '未知错误'}\n\n已达到最大尝试次数（${maxShareAttempts}次），请联系客服`);
                            disableShareButton();
                        }
                    }
                });
            } catch (error) {
                console.log(`❌ 第${shareAttempts}次分享过程出错`, error);

                const remainingAttempts = maxShareAttempts - shareAttempts;

                if (remainingAttempts > 0) {
                    alert(`❌ 发布失败: ${error.message}\n\n您还有 ${remainingAttempts} 次尝试机会`);
                    updateShareButtonText();
                } else {
                    alert(`❌ 发布失败: ${error.message}\n\n已达到最大尝试次数（${maxShareAttempts}次），请联系客服`);
                    disableShareButton();
                }
            }
        }

        // 🔥 更新分享按钮文本和提示信息
        function updateShareButtonText() {
            const shareBtn = document.getElementById('shareBtn');
            const attemptsText = document.getElementById('attemptsText');

            if (!shareBtn || !attemptsText) return;

            const remainingAttempts = maxShareAttempts - shareAttempts;

            // 简单显示次数
            if (isPageShared) {
                attemptsText.textContent = '已成功分享';
                shareBtn.textContent = '✅ 已成功分享';
                shareBtn.disabled = true;
                shareBtn.style.opacity = '0.5';
                shareBtn.style.cursor = 'not-allowed';
            } else if (remainingAttempts > 0) {
                attemptsText.textContent = `剩余 ${remainingAttempts} 次分享机会`;
                shareBtn.textContent = `🚀 发布到小红书`;
                shareBtn.disabled = false;
                shareBtn.style.opacity = '1';
                shareBtn.style.cursor = 'pointer';
            } else {
                attemptsText.textContent = '分享次数已用完';
                shareBtn.textContent = '❌ 分享次数已用完';
                shareBtn.disabled = true;
                shareBtn.style.opacity = '0.5';
                shareBtn.style.cursor = 'not-allowed';
            }
        }

        // 🔥 禁用分享按钮
        function disableShareButton() {
            const shareBtn = document.getElementById('shareBtn');
            if (shareBtn) {
                shareBtn.disabled = true;
                shareBtn.style.opacity = '0.5';
                shareBtn.style.cursor = 'not-allowed';

                if (isPageShared) {
                    shareBtn.textContent = '✅ 已成功分享';
                } else {
                    shareBtn.textContent = '❌ 分享次数已用完';
                }
            }
        }



        // 🔥 确认分享成功
        async function confirmShareSuccess() {
            try {
                const response = await fetch('/jeecg-boot/api/aigc/xiaohongshu/confirm-share-success', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pageId: shareConfig.pageId,
                        timestamp: Date.now()
                    })
                });

                if (response.ok) {
                    console.log('✅ 后端已确认分享成功');
                } else {
                    console.log('⚠️ 后端确认失败，但分享可能已成功');
                }
            } catch (error) {
                console.log('⚠️ 后端确认失败，但分享可能已成功', error);
            }
        }

        // 显示二维码弹窗
        function showQrModal() {
            const modal = document.getElementById('qrModal');
            modal.classList.add('show');

            // 只在第一次显示或预加载失败时才生成二维码
            const qrContainer = document.getElementById('qrCodeContainer');
            if (qrContainer.innerHTML.includes('正在生成二维码...')) {
                generateQrCode();
            }
        }

        // 关闭二维码弹窗
        function closeQrModal() {
            const modal = document.getElementById('qrModal');
            modal.classList.remove('show');
        }

        // 全局变量存储预生成的二维码
        let preloadedQrCode = null;
        let isPreloading = false;

        // 🔥 预加载生成二维码（本地库优化版）
        function preloadQrCode() {
            if (isPreloading || preloadedQrCode) return; // 避免重复预加载

            isPreloading = true;
            console.log('🔄 开始预加载二维码...');
            const baseUrl = window.location.href.split('?')[0];
            const mobileUrl = baseUrl + '?mobile=1&auto_share=1';

            // 🔥 优先使用本地库预加载
            if (typeof QRCode !== 'undefined') {
                // 创建临时canvas生成二维码
                const tempCanvas = document.createElement('canvas');

                QRCode.toCanvas(tempCanvas, mobileUrl, {
                    width: 280,
                    height: 280,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error) {
                    isPreloading = false;
                    if (error) {
                        console.warn('❌ 本地库预加载失败，尝试外部API:', error);
                        // 降级到外部API预加载
                        preloadQrCodeFallback(mobileUrl);
                    } else {
                        preloadedQrCode = tempCanvas.toDataURL();
                        console.log('✅ 本地库二维码预加载完成（瞬间完成）');
                    }
                });
            } else {
                console.warn('⚠️ QRCode库未加载，使用外部API预加载');
                preloadQrCodeFallback(mobileUrl);
            }
        }

        // 🔥 降级预加载：使用外部API
        function preloadQrCodeFallback(mobileUrl) {
            const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=' + encodeURIComponent(mobileUrl);

            // 创建图片对象预加载
            const img = new Image();
            img.onload = function() {
                preloadedQrCode = this.src;
                isPreloading = false;
                console.log('✅ 外部API二维码预加载完成');
            };
            img.onerror = function() {
                isPreloading = false;
                console.warn('❌ 外部API二维码预加载失败，将使用实时生成');
            };
            img.src = qrUrl;
        }

        // 处理按钮悬停事件
        function handleButtonHover() {
            // 如果是桌面端且还没有预加载，立即开始预加载
            if (!isMobileDevice() && !preloadedQrCode && !isPreloading && !isPageShared) {
                console.log('🖱️ 检测到按钮悬停，立即预加载二维码');
                preloadQrCode();
            }
        }

        // 🔥 异步刷新分享状态（兜底检查，确保状态最新）
        async function checkShareStatusOnLoad() {
            try {
                console.log('🔍 异步刷新分享状态（兜底检查）...');
                const response = await fetch('/jeecg-boot/api/aigc/xiaohongshu/check-share-status/' + shareConfig.pageId);

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.result) {
                        const result = data.result;

                        // 🔥 对比并更新状态（如果有变化）
                        const oldAttempts = shareAttempts;
                        const oldShared = isPageShared;

                        isPageShared = result.isShared || false;
                        shareAttempts = result.currentAttempts || 0;
                        maxShareAttempts = result.maxAttempts || 3;

                        // 🔥 如果状态有变化，记录日志并更新显示
                        if (oldAttempts !== shareAttempts || oldShared !== isPageShared) {
                            console.log(`📊 状态已更新: 尝试次数 ${oldAttempts}→${shareAttempts}, 已分享 ${oldShared}→${isPageShared}`);
                            updateShareButtonText();

                            if (isPageShared || shareAttempts >= maxShareAttempts) {
                                disableShareButton();
                                console.log('🚫 状态更新后禁用按钮');
                            }
                        } else {
                            console.log(`✅ 状态无变化: 已分享=${isPageShared}, 尝试次数=${shareAttempts}/${maxShareAttempts}`);
                        }
                    } else {
                        console.log('📝 后端返回数据格式异常，保持当前状态');
                    }
                } else {
                    console.warn('⚠️ 异步状态检查失败，状态码:', response.status, '保持当前状态');
                }
            } catch (error) {
                console.warn('⚠️ 异步状态检查异常:', error.message, '保持当前状态');
            }
        }

        // 🔥 显示加载状态（如果需要）
        function showLoadingState() {
            const shareBtn = document.getElementById('shareBtn');
            const attemptsText = document.getElementById('attemptsText');

            if (shareBtn) {
                shareBtn.textContent = '🔄 检查状态中...';
                shareBtn.disabled = true;
                shareBtn.style.opacity = '0.7';
            }

            if (attemptsText) {
                attemptsText.textContent = '正在检查分享状态...';
            }

            console.log('🔄 显示加载状态');
        }

        // 🔥 显示安全状态（网络错误时）
        function showSafeState() {
            const shareBtn = document.getElementById('shareBtn');
            const attemptsText = document.getElementById('attemptsText');

            if (shareBtn) {
                shareBtn.textContent = '⚠️ 状态检查失败';
                shareBtn.disabled = true;
                shareBtn.style.opacity = '0.5';
            }

            if (attemptsText) {
                attemptsText.textContent = '请刷新页面重试';
            }

            console.log('⚠️ 显示安全状态');
        }

        // 🔥 禁用分享按钮
        function disableShareButton() {
            const shareBtn = document.querySelector('.share-btn');
            if (shareBtn) {
                shareBtn.disabled = true;
                shareBtn.innerHTML = '✅ 该笔记已被分享';
                shareBtn.style.background = 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)';
                shareBtn.style.cursor = 'not-allowed';
                shareBtn.style.opacity = '0.7';
                shareBtn.onclick = function() {
                    alert('该笔记已被分享，无法重复分享');
                    return false;
                };
            }
        }

        // 🔥 生成二维码（本地库优化版 - 瞬间生成）
        function generateQrCode() {
            const qrContainer = document.getElementById('qrCodeContainer');

            if (preloadedQrCode) {
                // 使用预加载的二维码，立即显示
                console.log('⚡ 使用预加载的二维码');
                qrContainer.innerHTML = '<img src="' + preloadedQrCode + '" style="width: 280px; height: 280px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" alt="二维码" />';
            } else {
                // 🔥 使用本地库实时生成（瞬间完成）
                console.log('⚡ 使用本地库生成二维码...');
                qrContainer.innerHTML = '<div style="color: #999; display: flex; align-items: center; justify-content: center; height: 280px;">正在生成二维码...</div>';

                const baseUrl = window.location.href.split('?')[0];
                const mobileUrl = baseUrl + '?mobile=1&auto_share=1';

                // 🔥 使用本地qrcode.js库，无需网络请求
                if (typeof QRCode !== 'undefined') {
                    // 清空容器
                    qrContainer.innerHTML = '';

                    // 创建canvas容器
                    const canvas = document.createElement('canvas');
                    canvas.style.cssText = 'border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);';
                    qrContainer.appendChild(canvas);

                    // 本地生成二维码（瞬间完成）
                    QRCode.toCanvas(canvas, mobileUrl, {
                        width: 280,
                        height: 280,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    }, function (error) {
                        if (error) {
                            console.error('❌ 本地二维码生成失败:', error);
                            // 降级到外部API
                            generateQrCodeFallback(qrContainer, mobileUrl);
                        } else {
                            console.log('✅ 本地二维码生成成功（瞬间完成）');
                        }
                    });
                } else {
                    console.warn('⚠️ QRCode库未加载，使用降级方案');
                    // 降级到外部API
                    generateQrCodeFallback(qrContainer, mobileUrl);
                }
            }
        }

        // 🔥 降级方案：使用外部API生成二维码
        function generateQrCodeFallback(qrContainer, mobileUrl) {
            console.log('🔄 使用外部API生成二维码（降级方案）...');
            const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=280x280&data=' + encodeURIComponent(mobileUrl);

            const img = new Image();
            img.onload = function() {
                qrContainer.innerHTML = '<img src="' + this.src + '" style="width: 280px; height: 280px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" alt="二维码" />';
                console.log('✅ 外部API二维码生成成功');
            };
            img.onerror = function() {
                qrContainer.innerHTML = '<div style="color: #ff6b6b; text-align: center; padding: 20px; height: 280px; display: flex; flex-direction: column; align-items: center; justify-content: center;"><div style="font-size: 48px; margin-bottom: 16px;">😞</div><div>二维码生成失败</div><small style="margin-top: 8px; color: #999;">请刷新页面重试</small></div>';
                console.error('❌ 外部API二维码生成失败');
            };
            img.src = qrUrl;
        }

        // 🔥 显示安卓微信环境提示（增强版）
        function showAndroidWechatTip() {
            const tipModal = document.createElement('div');
            tipModal.id = 'androidWechatTip';
            tipModal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10000; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;';

            const currentUrl = window.location.href;

            tipModal.innerHTML = `
                <div style="background: white; border-radius: 16px; padding: 24px; margin: 20px; max-width: 360px; text-align: center; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
                    <div style="font-size: 48px; margin-bottom: 16px;">📱</div>
                    <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px; font-weight: 600;">检测到微信环境</h3>
                    <p style="margin: 0 0 20px 0; color: #666; font-size: 14px; line-height: 1.5;">
                        为了正常使用小红书分享功能，请选择以下方式之一：
                    </p>

                    <div style="text-align: left; margin: 20px 0;">
                        <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 8px; background: #f8f9fa; border-radius: 8px;">
                            <span style="font-size: 20px; margin-right: 8px;">🌐</span>
                            <span style="font-size: 13px; color: #333;">点击右上角 <strong>···</strong> → <strong>在浏览器中打开</strong></span>
                        </div>

                        <div style="display: flex; align-items: center; margin-bottom: 12px; padding: 8px; background: #f8f9fa; border-radius: 8px;">
                            <span style="font-size: 20px; margin-right: 8px;">📋</span>
                            <span style="font-size: 13px; color: #333;">复制链接到浏览器打开</span>
                        </div>

                        <div style="display: flex; align-items: center; padding: 8px; background: #f8f9fa; border-radius: 8px;">
                            <span style="font-size: 20px; margin-right: 8px;">📱</span>
                            <span style="font-size: 13px; color: #333;">扫描下方二维码</span>
                        </div>
                    </div>

                    <div style="margin: 16px 0;">
                        <button onclick="copyUrlToClipboard()" style="background: #007AFF; color: white; border: none; border-radius: 8px; padding: 10px 16px; font-size: 13px; font-weight: 600; cursor: pointer; margin-right: 8px;">
                            📋 复制链接
                        </button>
                        <button onclick="showQrForAndroid()" style="background: #34C759; color: white; border: none; border-radius: 8px; padding: 10px 16px; font-size: 13px; font-weight: 600; cursor: pointer;">
                            📱 显示二维码
                        </button>
                    </div>

                    <div id="androidQrContainer" style="margin: 16px 0; display: none;"></div>

                    <button onclick="closeAndroidWechatTip()" style="background: #ff2442; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 600; cursor: pointer; width: 100%; margin-top: 8px;">
                        我知道了
                    </button>
                </div>
            `;

            document.body.appendChild(tipModal);
        }

        // 🔥 复制链接到剪贴板
        function copyUrlToClipboard() {
            const currentUrl = window.location.href;

            if (navigator.clipboard && window.isSecureContext) {
                // 现代浏览器
                navigator.clipboard.writeText(currentUrl).then(() => {
                    showToast('✅ 链接已复制到剪贴板');
                }).catch(() => {
                    fallbackCopyTextToClipboard(currentUrl);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(currentUrl);
            }
        }

        // 🔥 降级复制方案
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast('✅ 链接已复制到剪贴板');
            } catch (err) {
                showToast('❌ 复制失败，请手动复制链接');
                // 显示链接让用户手动复制
                prompt('请复制以下链接:', text);
            }

            document.body.removeChild(textArea);
        }

        // 🔥 为安卓用户显示二维码
        function showQrForAndroid() {
            const qrContainer = document.getElementById('androidQrContainer');
            qrContainer.style.display = 'block';
            qrContainer.innerHTML = '<div style="color: #999; padding: 20px;">正在生成二维码...</div>';

            const currentUrl = window.location.href;

            // 使用本地库生成二维码
            if (typeof QRCode !== 'undefined') {
                qrContainer.innerHTML = '';
                const canvas = document.createElement('canvas');
                canvas.style.cssText = 'border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);';
                qrContainer.appendChild(canvas);

                QRCode.toCanvas(canvas, currentUrl, {
                    width: 200,
                    height: 200,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error) {
                    if (error) {
                        console.error('❌ 安卓二维码生成失败:', error);
                        qrContainer.innerHTML = '<div style="color: #ff6b6b; padding: 20px; font-size: 12px;">二维码生成失败</div>';
                    } else {
                        console.log('✅ 安卓二维码生成成功');
                        // 添加提示文字
                        const tip = document.createElement('div');
                        tip.style.cssText = 'font-size: 12px; color: #666; margin-top: 8px;';
                        tip.textContent = '用其他设备扫描此二维码';
                        qrContainer.appendChild(tip);
                    }
                });
            } else {
                // 降级到外部API
                const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(currentUrl);
                qrContainer.innerHTML = `<img src="${qrUrl}" style="width: 200px; height: 200px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" alt="二维码" onload="console.log('✅ 安卓外部API二维码加载成功')" onerror="this.parentElement.innerHTML='<div style=\\"color: #ff6b6b; padding: 20px; font-size: 12px;\\">二维码生成失败</div>'">`;
            }
        }

        // 🔥 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.8); color: white; padding: 12px 20px; border-radius: 8px; font-size: 14px; z-index: 10001; pointer-events: none;';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                document.body.removeChild(toast);
            }, 2000);
        }

        // 关闭安卓微信环境提示
        function closeAndroidWechatTip() {
            const tipModal = document.getElementById('androidWechatTip');
            if (tipModal) {
                tipModal.remove();
            }
        }

        // 显示页面失效提示
        function showExpiredPage(reason, message) {
            const container = document.querySelector('.container');
            if (container) {
                container.style.display = 'none';
            }

            const expiredDiv = document.createElement('div');
            expiredDiv.innerHTML = '<div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #f8f9fa; display: flex; align-items: center; justify-content: center; font-family: -apple-system, BlinkMacSystemFont, \\"Segoe UI\\", Roboto, sans-serif;"><div style="text-align: center; padding: 40px 20px; max-width: 400px; background: white; border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);"><div style="font-size: 64px; margin-bottom: 20px;">🔒</div><h2 style="margin: 0 0 16px 0; color: #333; font-size: 20px; font-weight: 600;">此分享页面已失效</h2><p style="margin: 0 0 20px 0; color: #666; font-size: 15px; line-height: 1.5;">为了保护内容质量，每个分享页面只能使用一次</p><p style="margin: 0 0 24px 0; color: #999; font-size: 13px;">如需重新分享，请返回COZE重新生成</p><button onclick="window.close()" style="background: #ff2442; color: white; border: none; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;">关闭页面</button></div></div>';

            document.body.appendChild(expiredDiv);
        }

        // 触摸滑动功能
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;
        let isDragging = false;

        function handleTouchStart(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isDragging = false;
            console.log('👆 触摸开始:', touchStartX);
        }

        function handleTouchMove(e) {
            if (!touchStartX) return;

            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;

            const deltaX = Math.abs(touchCurrentX - touchStartX);
            const deltaY = Math.abs(touchCurrentY - touchStartY);

            // 如果水平滑动距离大于垂直滑动距离，阻止默认滚动
            if (deltaX > deltaY && deltaX > 10) {
                e.preventDefault();
                isDragging = true;
            }
        }

        function handleTouchEnd(e) {
            if (!touchStartX || !isDragging) return;

            touchEndX = e.changedTouches[0].clientX;
            touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = Math.abs(touchEndY - touchStartY);

            console.log('👆 触摸结束，滑动距离:', deltaX);

            // 只有在水平滑动距离足够大且垂直滑动距离较小时才切换图片
            if (Math.abs(deltaX) > 50 && deltaY < 100) {
                if (deltaX > 0) {
                    // 向右滑动，显示上一张
                    console.log('👈 向右滑动，显示上一张图片');
                    prevImage();
                } else {
                    // 向左滑动，显示下一张
                    console.log('👉 向左滑动，显示下一张图片');
                    nextImage();
                }
            }

            // 重置触摸状态
            touchStartX = 0;
            touchEndX = 0;
            touchStartY = 0;
            touchEndY = 0;
            isDragging = false;
        }

        // 初始化轮播指示器
        function initCarouselDots() {
            const dotsContainer = document.getElementById('carouselDots');
            if (!dotsContainer || totalImages <= 1) return;

            dotsContainer.innerHTML = '';
            for (let i = 0; i < totalImages; i++) {
                const dot = document.createElement('div');
                dot.className = 'carousel-dot' + (i === 0 ? ' active' : '');
                dot.onclick = () => goToImage(i);  // 使用现有的函数
                dotsContainer.appendChild(dot);
            }
        }

        // 🔥 禁用双击缩放功能
        function disableDoubleClickZoom() {
            console.log('🚫 禁用双击缩放功能');

            // 阻止双击事件
            document.addEventListener('dblclick', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, { passive: false });

            // 阻止触摸缩放手势
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
            }, { passive: false });

            // 阻止鼠标滚轮缩放（Ctrl+滚轮）
            document.addEventListener('wheel', function(e) {
                if (e.ctrlKey) {
                    e.preventDefault();
                }
            }, { passive: false });

            // 阻止键盘缩放（Ctrl + +/-）
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '=' || e.key === '0')) {
                    e.preventDefault();
                }
            }, { passive: false });

            // 阻止手势缩放
            document.addEventListener('gesturestart', function(e) {
                e.preventDefault();
            }, { passive: false });

            document.addEventListener('gesturechange', function(e) {
                e.preventDefault();
            }, { passive: false });

            document.addEventListener('gestureend', function(e) {
                e.preventDefault();
            }, { passive: false });

            console.log('✅ 双击缩放功能已禁用');
        }



        // 页面初始化
        window.onload = function() {
            totalImages = document.querySelectorAll('.image-slide').length;
            console.log('📸 初始化图片轮播，共', totalImages, '张图片');

            // 🔥 显示从后端获取的真实状态
            console.log(`🔍 页面加载时的真实状态: 已分享=${isPageShared}, 尝试次数=${shareAttempts}/${maxShareAttempts}`);

            // 生成轮播指示器
            initCarouselDots();

            // 🔥 基于真实状态初始化按钮（不再使用默认值）
            updateShareButtonText();

            // 🔥 如果已分享或次数用完，立即禁用按钮
            if (isPageShared || shareAttempts >= maxShareAttempts) {
                disableShareButton();
                console.log('🚫 按钮已禁用 - 已分享或次数用完');
            }

            // 🔥 禁用双击缩放
            disableDoubleClickZoom();

            // 🔥 异步刷新状态（兜底检查，确保状态最新）
            checkShareStatusOnLoad();

            // 🔥 PC端立即开始预加载二维码（提升用户体验）
            if (!isMobileDevice() && !isPageShared && shareAttempts < maxShareAttempts) {
                console.log('💻 PC端检测，立即预加载二维码');
                setTimeout(() => {
                    preloadQrCode();
                }, 500); // 500ms后开始预加载，避免阻塞页面初始化
            }

            // 如果只有一张图片，隐藏导航按钮和滑动提示
            if (totalImages <= 1) {
                const prevBtn = document.querySelector('.carousel-nav.prev');
                const nextBtn = document.querySelector('.carousel-nav.next');
                const dots = document.getElementById('carouselDots');
                const swipeHint = document.getElementById('swipeHint');

                if (prevBtn) prevBtn.style.display = 'none';
                if (nextBtn) nextBtn.style.display = 'none';
                if (dots) dots.style.display = 'none';
                if (swipeHint) swipeHint.style.display = 'none';
            } else {
                // 多张图片时，3秒后隐藏滑动提示
                setTimeout(() => {
                    const swipeHint = document.getElementById('swipeHint');
                    if (swipeHint) {
                        swipeHint.style.opacity = '0';
                        setTimeout(() => swipeHint.style.display = 'none', 500);
                    }
                }, 3000);
            }

            // 添加触摸事件监听器
            const imageContainer = document.getElementById('imagesCarousel');
            if (imageContainer) {
                imageContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
                imageContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
                imageContainer.addEventListener('touchend', handleTouchEnd, { passive: false });
                console.log('👆 触摸滑动功能已启用');
            }

            // 🚀 预加载二维码（延迟2秒，避免影响页面初始化）
            setTimeout(() => {
                preloadQrCode();
            }, 2000);
        };
    </script>
</body>
</html>
