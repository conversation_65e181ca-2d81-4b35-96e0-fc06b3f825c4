(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~325c93f9"],{"24ec":function(e,t,r){"use strict";var a=r("c2dc"),s=r.n(a);s.a},ac8d:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"website-login"},[e._m(0),r("WebsiteHeader"),r("div",{staticClass:"login-main"},[r("div",{ref:"loginInfo",staticClass:"login-info"},[r("div",{staticClass:"info-content"},[r("div",{staticClass:"brand-showcase"},[r("div",{staticClass:"brand-logo-large"},[r("LogoImage",{attrs:{size:"large",hover:!1,"container-class":"login-logo-container","image-class":"login-logo-image","fallback-class":"login-logo-fallback"}}),r("h1",{staticClass:"brand-title"},[e._v("智界AIGC")])],1),r("p",{staticClass:"brand-slogan"},[e._v("AI驱动的内容生成平台")])]),r("div",{staticClass:"feature-highlights"},e._l(e.features,(function(t,a){return r("div",{key:a,staticClass:"feature-item"},[r("div",{staticClass:"feature-icon"},[r("a-icon",{attrs:{type:t.icon}})],1),r("div",{staticClass:"feature-text"},[r("h3",[e._v(e._s(t.title))]),r("p",[e._v(e._s(t.description))])])])})),0)])]),r("div",{ref:"loginContainer",staticClass:"login-container"},[r("div",{staticClass:"login-card"},[r("div",{staticClass:"login-header"},[r("h2",{staticClass:"login-title"},[e._v("欢迎使用智界AIGC")]),r("p",{staticClass:"login-subtitle"},[e._v(e._s(e.inviteCodeFromUrl?"您正在通过邀请链接登录":"选择您的登录方式，开启AI创作之旅"))])]),r("div",{staticClass:"auth-tabs"},[r("div",{staticClass:"tab-buttons"},[r("button",{class:["tab-btn",{active:"phone"===e.loginType}],on:{click:function(t){return e.switchLoginType("phone")}}},[r("a-icon",{attrs:{type:"mobile"}}),r("span",{staticClass:"tab-text"},[e._v("手机号")])],1),r("button",{class:["tab-btn",{active:"email"===e.loginType}],on:{click:function(t){return e.switchLoginType("email")}}},[r("a-icon",{attrs:{type:"mail"}}),r("span",{staticClass:"tab-text"},[e._v("邮箱")])],1),r("button",{class:["tab-btn",{active:"password"===e.loginType}],on:{click:function(t){return e.switchLoginType("password")}}},[r("a-icon",{attrs:{type:"lock"}}),r("span",{staticClass:"tab-text"},[e._v("密码登录")])],1)])]),r("div",{staticClass:"login-form"},["password"===e.loginType?r("div",{staticClass:"login-content"},[r("a-form",{staticClass:"account-login-form",attrs:{form:e.form},on:{submit:e.handleSubmit}},[r("div",{staticClass:"input-group"},[r("a-form-item",[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["username",{rules:[{required:!0,message:"请输入用户名或邮箱"}]}],expression:"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]"}],staticClass:"clean-input",attrs:{size:"large",placeholder:"用户名或邮箱"}},[r("a-icon",{attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1)],1),r("div",{staticClass:"input-group"},[r("a-form-item",[r("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{required:!0,message:"请输入密码"}]}],expression:"['password', { rules: [{ required: true, message: '请输入密码' }] }]"}],staticClass:"clean-input",attrs:{size:"large",placeholder:"密码"}},[r("a-icon",{attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1)],1),r("div",{staticClass:"input-group"},[r("a-form-item",[r("div",{staticClass:"captcha-row"},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["inputCode",{rules:[{required:!0,message:"请输入验证码"}]}],expression:"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]"}],staticClass:"clean-input captcha-input",attrs:{size:"large",placeholder:"验证码"}},[r("a-icon",{attrs:{slot:"prefix",type:"safety-certificate"},slot:"prefix"})],1),r("div",{staticClass:"captcha-image-container",on:{click:e.handleChangeCheckCode}},[r("img",{staticClass:"captcha-image",attrs:{src:e.randCodeImage,alt:"验证码"}}),r("div",{staticClass:"captcha-refresh-overlay"},[r("a-icon",{attrs:{type:"reload"}})],1)])],1)])],1),r("div",{staticClass:"login-options"},[r("a-checkbox",{staticClass:"remember-me",model:{value:e.rememberMe,callback:function(t){e.rememberMe=t},expression:"rememberMe"}},[e._v("\n                记住我\n              ")]),r("a",{staticClass:"forgot-link",on:{click:e.handleForgotPassword}},[e._v("\n                忘记密码？\n              ")])],1),r("a-form-item",{staticClass:"login-button-item"},[r("a-button",{staticClass:"login-submit-button",attrs:{type:"primary","html-type":"submit",size:"large",loading:e.loginLoading,block:""}},[e.loginLoading?r("span",[e._v("登录中...")]):r("span",[e._v("登录")])])],1)],1)],1):e._e(),"phone"===e.loginType?r("div",{staticClass:"login-content"},[r("a-form",{staticClass:"phone-login-form",attrs:{form:e.phoneLoginForm},on:{submit:e.handlePhoneLogin}},[r("div",{staticClass:"input-group"},[r("a-form-item",[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["phone",{rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}]}],expression:"['phone', { rules: [\n                      { required: true, message: '请输入手机号' },\n                      { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确' }\n                    ] }]"}],staticClass:"clean-input",attrs:{size:"large",placeholder:"请输入手机号"}},[r("a-icon",{attrs:{slot:"prefix",type:"mobile"},slot:"prefix"})],1)],1)],1),r("div",{staticClass:"input-group"},[r("a-form-item",[r("div",{staticClass:"verify-code-row"},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["smsCode",{rules:[{required:!0,message:"请输入验证码"}]}],expression:"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]"}],staticClass:"clean-input verify-code-input",attrs:{size:"large",placeholder:"请输入短信验证码"}},[r("a-icon",{attrs:{slot:"prefix",type:"safety-certificate"},slot:"prefix"})],1),r("a-button",{staticClass:"send-code-btn",attrs:{disabled:e.smsCodeSending||e.smsCountdown>0,size:"large"},on:{click:e.sendLoginSmsCode}},[e._v("\n                      "+e._s(e.smsCountdown>0?e.smsCountdown+"s后重发":"发送验证码")+"\n                    ")])],1)])],1),r("a-form-item",{staticClass:"login-button-item"},[r("a-button",{staticClass:"login-submit-button",attrs:{type:"primary","html-type":"submit",size:"large",loading:e.phoneLoginLoading,block:""}},[e.phoneLoginLoading?r("span",[e._v("登录中...")]):r("span",[e._v("登录")])])],1),r("div",{staticClass:"phone-login-tip"},[r("a-alert",{attrs:{message:"手机号登录说明",description:"首次使用手机号登录将自动为您创建账户，无需设置密码",type:"info","show-icon":""}})],1)],1)],1):e._e(),"email"===e.loginType?r("div",{staticClass:"login-content"},[r("a-form",{staticClass:"email-login-form",attrs:{form:e.emailLoginForm},on:{submit:e.handleEmailLogin}},[r("div",{staticClass:"input-group"},[r("a-form-item",[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["email",{rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"邮箱格式不正确"}]}],expression:"['email', { rules: [\n                      { required: true, message: '请输入邮箱' },\n                      { type: 'email', message: '邮箱格式不正确' }\n                    ] }]"}],staticClass:"clean-input",attrs:{size:"large",placeholder:"请输入邮箱"}},[r("a-icon",{attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),r("div",{staticClass:"input-group"},[r("a-form-item",[r("div",{staticClass:"verify-code-row"},[r("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["emailCode",{rules:[{required:!0,message:"请输入验证码"}]}],expression:"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]"}],staticClass:"clean-input verify-code-input",attrs:{size:"large",placeholder:"请输入邮箱验证码"}},[r("a-icon",{attrs:{slot:"prefix",type:"safety-certificate"},slot:"prefix"})],1),r("a-button",{staticClass:"send-code-btn",attrs:{disabled:e.emailCodeSending||e.emailCountdown>0,size:"large"},on:{click:e.sendLoginEmailCode}},[e._v("\n                      "+e._s(e.emailCountdown>0?e.emailCountdown+"s后重发":"发送验证码")+"\n                    ")])],1)])],1),r("a-form-item",{staticClass:"login-button-item"},[r("a-button",{staticClass:"login-submit-button",attrs:{type:"primary","html-type":"submit",size:"large",loading:e.emailLoginLoading,block:""}},[e.emailLoginLoading?r("span",[e._v("登录中...")]):r("span",[e._v("登录")])])],1),r("div",{staticClass:"email-login-tip"},[r("a-alert",{attrs:{message:"邮箱登录说明",description:"首次使用邮箱登录将自动为您创建账户，无需设置密码",type:"info","show-icon":""}})],1)],1)],1):e._e(),"wechat"===e.loginType?r("div",{staticClass:"login-content"},[r("div",{staticClass:"wechat-login-container"},[r("div",{staticClass:"wechat-qr-section"},[r("div",{staticClass:"qr-code-container"},[e.wechatLoginQrCode?r("img",{staticClass:"qr-code-image",attrs:{src:e.wechatLoginQrCode,alt:"微信登录二维码"}}):r("div",{staticClass:"qr-loading"},[r("a-spin",{attrs:{size:"large"}}),r("p",[e._v("正在生成二维码...")])],1)]),r("div",{staticClass:"qr-instructions"},[r("h4",[e._v("使用微信扫码登录")]),r("p",[e._v("1. 打开微信扫一扫")]),r("p",[e._v("2. 扫描上方二维码")]),r("p",[e._v("3. 确认登录")]),e.inviteCodeFromUrl?r("p",{staticClass:"invite-tip"},[e._v("* 您正在通过邀请链接登录")]):e._e()])])])]):e._e()])])])])],1)},s=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"login-background"},[r("div",{staticClass:"bg-animated-grid"}),r("div",{staticClass:"bg-floating-elements"}),r("div",{staticClass:"bg-gradient-overlay"})])}],n=r("a34a"),i=r.n(n),o=r("7ded"),c=r("9da4"),u=r("0fea"),l=r("cffa"),p=r("ccb3"),d=r("8bd7"),h=r("9fb0"),m=r("e0a5"),g=r("2b0e"),f=r("1eec");function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){C(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function C(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x(e,t,r,a,s,n,i){try{var o=e[n](i),c=o.value}catch(u){return void r(u)}o.done?t(c):Promise.resolve(c).then(a,s)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(a,s){var n=e.apply(t,r);function i(e){x(n,a,s,i,o,"next",e)}function o(e){x(n,a,s,i,o,"throw",e)}i(void 0)}))}}var y={name:"WebsiteLogin",components:{WebsiteHeader:p["default"],LogoImage:d["default"]},data:function(){return{form:this.$form.createForm(this),phoneLoginForm:this.$form.createForm(this),emailLoginForm:this.$form.createForm(this),loginLoading:!1,phoneLoginLoading:!1,emailLoginLoading:!1,rememberMe:!1,randCodeImage:"",currdatetime:(new Date).getTime(),encryptedString:"",loginType:"phone",smsCodeSending:!1,smsCountdown:0,emailCodeSending:!1,emailCountdown:0,inviteCodeFromUrl:"",wechatLoginQrCode:"",features:[{icon:"robot",title:"AI智能创作",description:"强大的AI算法，助您快速生成高质量内容"},{icon:"thunderbolt",title:"极速响应",description:"毫秒级响应速度，让创作灵感不再等待"},{icon:"safety-certificate",title:"安全可靠",description:"企业级安全保障，保护您的创作成果"},{icon:"global",title:"全球服务",description:"覆盖全球的CDN网络，随时随地畅享服务"}]}},mounted:function(){this.getEncrypte(),this.handleChangeCheckCode(),this.initAnimations(),this.checkInviteCode()},methods:{getEncrypte:function(){var e=this;Object(c["b"])().then((function(t){e.encryptedString=t}))},handleChangeCheckCode:function(){var e=this;this.currdatetime=(new Date).getTime(),Object(u["c"])("/sys/randomImage/".concat(this.currdatetime)).then((function(t){t.success?e.randCodeImage=t.result:e.$message.error(t.message)})).catch((function(){e.$message.error("验证码加载失败")}))},handleSubmit:function(e){var t=this;e.preventDefault(),this.form.validateFields((function(e,r){if(!e){t.loginLoading=!0;var a=Object(c["a"])(r.username,t.encryptedString.key,t.encryptedString.iv),s=Object(c["a"])(r.password,t.encryptedString.key,t.encryptedString.iv),n={username:a,password:s,captcha:r.inputCode,checkKey:t.currdatetime,remember_me:t.rememberMe,loginType:"website"};Object(o["c"])(n).then(function(){var e=w(i.a.mark((function e(r){var a,s,n,o,c,l,p,d;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.loginLoading=!1,200!==r.code&&"200"!==r.code){e.next=25;break}return t.$notification.success({message:"登录成功",description:"欢迎回来！正在跳转到个人中心...",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),a=r.result,s=a.userInfo,g["default"].ls.set(h["a"],a.token,6048e5),g["default"].ls.set(h["v"],s.username,6048e5),g["default"].ls.set(h["u"],s,6048e5),g["default"].ls.set(h["s"],a.sysAllDictItems,6048e5),e.prev=11,e.next=14,Object(u["c"])("/sys/user/getCurrentUserDeparts");case 14:n=e.sent,n.success?(o=n.result.role,c=n.result.departId,localStorage.setItem("userRole",o||""),localStorage.setItem("departId",c||""),l=t.$route.query.redirect,l?t.$router.push(l):t.isAdminRole(o)?t.$router.push("/dashboard/analysis"):t.$router.push("/usercenter")):(p=t.$route.query.redirect,p?t.$router.push(p):t.$router.push("/usercenter")),e.next=23;break;case 18:e.prev=18,e.t0=e["catch"](11),d=t.$route.query.redirect,d?t.$router.push(d):t.$router.push("/usercenter");case 23:e.next=27;break;case 25:t.$notification.error({message:"登录失败",description:r.message||"用户名或密码错误，请检查后重试",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.handleChangeCheckCode();case 27:case"end":return e.stop()}}),e,null,[[11,18]])})));return function(t){return e.apply(this,arguments)}}()).catch(function(){var e=w(i.a.mark((function e(r){var a,s,c,u,l,p;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.loginLoading=!1,!r.response||!r.response.data||4002!==r.response.data.code){e.next=36;break}return a=r.response.data.result,s=function(){var e=w(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=b(b({},n),{},{loginType:"force"}),e.next=4,Object(o["c"])(t);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),e.prev=5,e.next=8,Object(f["a"])(a,s);case 8:if(c=e.sent,!c||200!==c.code&&"200"!==c.code){e.next=21;break}t.$notification.success({message:"登录成功",description:"欢迎回来！正在跳转到个人中心...",placement:"topRight",duration:3}),u=c.result,l=u.userInfo,g["default"].ls.set(h["a"],u.token,6048e5),g["default"].ls.set(h["v"],l.username,6048e5),g["default"].ls.set(h["u"],l,6048e5),g["default"].ls.set(h["s"],u.sysAllDictItems,6048e5),p=t.$route.query.redirect,p?t.$router.push(p):t.$router.push("/usercenter"),e.next=22;break;case 21:throw new Error(c&&c.message||"强制登录失败");case 22:e.next=34;break;case 24:if(e.prev=24,e.t0=e["catch"](5),"USER_CANCELLED"!==e.t0.message){e.next=32;break}return t.handleChangeCheckCode(),e.abrupt("return");case 32:t.$notification.error({message:"登录失败",description:e.t0.message||"强制登录失败",placement:"topRight",duration:4}),t.handleChangeCheckCode();case 34:e.next=38;break;case 36:t.$notification.error({message:"登录失败",description:r.message||"网络连接异常，请检查网络后重试",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}),t.handleChangeCheckCode();case 38:case"end":return e.stop()}}),e,null,[[5,24]])})));return function(t){return e.apply(this,arguments)}}())}}))},handleForgotPassword:function(){this.$notification.info({message:"忘记密码",description:"忘记密码功能正在开发中，敬请期待...",placement:"topRight",duration:3,style:{width:"350px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})},handleSocialLogin:function(e){var t={wechat:"微信",qq:"QQ",alipay:"支付宝"};this.$message.info("".concat(t[e],"登录功能开发中..."))},checkInviteCode:function(){var e=this.$route.query.ref,t=this.$route.query.invite,r=e||t;r&&(this.inviteCodeFromUrl=r)},switchLoginType:function(e){this.loginType=e,this.smsCountdown=0,this.emailCountdown=0,"wechat"===e&&this.generateWechatLoginQrCode()},handlePhoneLogin:function(e){var t=this;e.preventDefault(),this.phoneLoginForm.validateFields(function(){var e=w(i.a.mark((function e(r,a){var s;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r){e.next=19;break}return t.phoneLoginLoading=!0,e.prev=2,e.next=5,Object(m["a"])(a.phone,"phone");case 5:if(s=e.sent,!s.success){e.next=11;break}return e.next=9,t.autoRegisterAndLogin("phone",a);case 9:e.next=13;break;case 11:return e.next=13,t.loginWithSmsCode(a);case 13:e.next=19;break;case 15:e.prev=15,e.t0=e["catch"](2),t.phoneLoginLoading=!1,t.$notification.error({message:"登录失败",description:e.t0.message||"登录过程中发生错误",placement:"topRight",duration:4});case 19:case"end":return e.stop()}}),e,null,[[2,15]])})));return function(t,r){return e.apply(this,arguments)}}())},handleEmailLogin:function(e){var t=this;e.preventDefault(),this.emailLoginForm.validateFields(function(){var e=w(i.a.mark((function e(r,a){var s;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r){e.next=19;break}return t.emailLoginLoading=!0,e.prev=2,e.next=5,Object(m["a"])(a.email,"email");case 5:if(s=e.sent,!s.success){e.next=11;break}return e.next=9,t.autoRegisterAndLogin("email",a);case 9:e.next=13;break;case 11:return e.next=13,t.loginWithEmailCode(a);case 13:e.next=19;break;case 15:e.prev=15,e.t0=e["catch"](2),t.emailLoginLoading=!1,t.$notification.error({message:"登录失败",description:e.t0.message||"登录过程中发生错误",placement:"topRight",duration:4});case 19:case"end":return e.stop()}}),e,null,[[2,15]])})));return function(t,r){return e.apply(this,arguments)}}())},autoRegisterAndLogin:function(){var e=w(i.a.mark((function e(t,r){var a,s,n,o;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,s=this.generateSecurePassword(),a={type:t},C(a,t,r[t]),C(a,"verifyCode",r["phone"===t?"smsCode":"emailCode"]),C(a,"password",s),C(a,"confirmPassword",s),C(a,"inviteCode",this.inviteCodeFromUrl),C(a,"inviteSource",this.inviteCodeFromUrl?"link":null),n=a,e.next=6,Object(m["c"])(n);case 6:if(o=e.sent,!o.success){e.next=13;break}return e.next=11,this.performAutoLogin(t,r,s);case 11:e.next=14;break;case 13:throw new Error(o.message||"注册失败");case 14:e.next=19;break;case 16:throw e.prev=16,e.t0=e["catch"](0),e.t0;case 19:return e.prev=19,this.phoneLoginLoading=!1,this.emailLoginLoading=!1,e.finish(19);case 23:case"end":return e.stop()}}),e,this,[[0,16,19,23]])})));function t(t,r){return e.apply(this,arguments)}return t}(),loginWithSmsCode:function(){var e=w(i.a.mark((function e(t){var r,a,s,n,c;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r={mobile:t.phone,captcha:t.smsCode,loginType:"website"},e.next=5,Object(o["e"])(r);case 5:if(a=e.sent,!a.success){e.next=11;break}return e.next=9,this.handleLoginSuccess(a.result);case 9:e.next=38;break;case 11:if(4002!==a.code){e.next=37;break}return s=a.result,n=function(){var e=w(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=b(b({},r),{},{loginType:"force"}),e.next=4,Object(o["e"])(t);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),e.prev=15,e.next=18,Object(f["a"])(s,n);case 18:if(c=e.sent,!c||!c.success){e.next=24;break}return e.next=22,this.handleLoginSuccess(c.result);case 22:e.next=25;break;case 24:throw new Error(c&&c.message||"强制登录失败");case 25:e.next=35;break;case 27:if(e.prev=27,e.t0=e["catch"](15),"USER_CANCELLED"!==e.t0.message){e.next=34;break}return e.abrupt("return");case 34:throw e.t0;case 35:e.next=38;break;case 37:throw new Error(a.message||"登录失败");case 38:e.next=43;break;case 40:throw e.prev=40,e.t1=e["catch"](0),e.t1;case 43:return e.prev=43,this.phoneLoginLoading=!1,e.finish(43);case 46:case"end":return e.stop()}}),e,this,[[0,40,43,46],[15,27]])})));function t(t){return e.apply(this,arguments)}return t}(),loginWithEmailCode:function(){var e=w(i.a.mark((function e(t){var r,a,s,n,c;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r={email:t.email,emailCode:t.emailCode,loginType:"website"},e.next=5,Object(o["a"])(r);case 5:if(a=e.sent,!a.success){e.next=11;break}return e.next=9,this.handleLoginSuccess(a.result);case 9:e.next=38;break;case 11:if(4002!==a.code){e.next=37;break}return s=a.result,n=function(){var e=w(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=b(b({},r),{},{loginType:"force"}),e.next=4,Object(o["a"])(t);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),e.prev=15,e.next=18,Object(f["a"])(s,n);case 18:if(c=e.sent,!c||!c.success){e.next=24;break}return e.next=22,this.handleLoginSuccess(c.result);case 22:e.next=25;break;case 24:throw new Error(c&&c.message||"强制登录失败");case 25:e.next=35;break;case 27:if(e.prev=27,e.t0=e["catch"](15),"USER_CANCELLED"!==e.t0.message){e.next=34;break}return e.abrupt("return");case 34:throw e.t0;case 35:e.next=38;break;case 37:throw new Error(a.message||"登录失败");case 38:e.next=43;break;case 40:throw e.prev=40,e.t1=e["catch"](0),e.t1;case 43:return e.prev=43,this.emailLoginLoading=!1,e.finish(43);case 46:case"end":return e.stop()}}),e,this,[[0,40,43,46],[15,27]])})));function t(t){return e.apply(this,arguments)}return t}(),handleLoginSuccess:function(){var e=w(i.a.mark((function e(t){var r;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.prev=0,g["default"].ls.set(h["a"],t.token,6048e5),g["default"].ls.set(h["v"],t.userInfo.username,6048e5),g["default"].ls.set(h["u"],t.userInfo,6048e5),g["default"].ls.set(h["s"],t.sysAllDictItems,6048e5),this.$notification.success({message:"登录成功",description:"欢迎回来，".concat(t.userInfo.realname||t.userInfo.username,"！"),placement:"topRight",duration:3}),r=this.$route.query.redirect||"/",this.$router.push(r),e.next=14;break;case 10:throw e.prev=10,e.t0=e["catch"](0),new Error("登录后处理失败");case 14:case"end":return e.stop()}}),e,this,[[0,10]])})));function t(t){return e.apply(this,arguments)}return t}(),sendLoginSmsCode:function(){var e=w(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.phoneLoginForm.getFieldValue("phone"),t){e.next=4;break}return this.$message.error("请先输入手机号"),e.abrupt("return");case 4:if(/^1[3-9]\d{9}$/.test(t)){e.next=7;break}return this.$message.error("手机号格式不正确"),e.abrupt("return");case 7:return this.smsCodeSending=!0,e.prev=8,e.next=11,Object(m["e"])(t,"register");case 11:r=e.sent,r.success?(this.$message.success("验证码发送成功，请查收短信"),this.startSmsCountdown()):this.$message.error(r.message||"验证码发送失败"),e.next=19;break;case 15:e.prev=15,e.t0=e["catch"](8),this.$message.error("验证码发送失败，请稍后重试");case 19:return e.prev=19,this.smsCodeSending=!1,e.finish(19);case 22:case"end":return e.stop()}}),e,this,[[8,15,19,22]])})));function t(){return e.apply(this,arguments)}return t}(),sendLoginEmailCode:function(){var e=w(i.a.mark((function e(){var t,r;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.emailLoginForm.getFieldValue("email"),t){e.next=4;break}return this.$message.error("请先输入邮箱"),e.abrupt("return");case 4:if(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)){e.next=7;break}return this.$message.error("邮箱格式不正确"),e.abrupt("return");case 7:return this.emailCodeSending=!0,e.prev=8,e.next=11,Object(m["d"])(t,"register");case 11:r=e.sent,r.success?(this.$message.success("验证码发送成功，请查收邮件"),this.startEmailCountdown()):this.$message.error(r.message||"验证码发送失败"),e.next=19;break;case 15:e.prev=15,e.t0=e["catch"](8),this.$message.error("验证码发送失败，请稍后重试");case 19:return e.prev=19,this.emailCodeSending=!1,e.finish(19);case 22:case"end":return e.stop()}}),e,this,[[8,15,19,22]])})));function t(){return e.apply(this,arguments)}return t}(),startSmsCountdown:function(){var e=this;this.smsCountdown=60;var t=setInterval((function(){e.smsCountdown--,e.smsCountdown<=0&&clearInterval(t)}),1e3)},startEmailCountdown:function(){var e=this;this.emailCountdown=60;var t=setInterval((function(){e.emailCountdown--,e.emailCountdown<=0&&clearInterval(t)}),1e3)},generateWechatLoginQrCode:function(){var e=w(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(m["b"])("login",this.inviteCodeFromUrl);case 3:t=e.sent,t.success?this.wechatLoginQrCode=t.result.qrCodeUrl:this.$message.error("生成微信二维码失败"),e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),this.$message.error("生成微信二维码失败");case 11:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),generateSecurePassword:function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",t="0123456789",r=e+t,a="";a+=e.charAt(Math.floor(Math.random()*e.length)),a+=t.charAt(Math.floor(Math.random()*t.length));for(var s=0;s<10;s++)a+=r.charAt(Math.floor(Math.random()*r.length));return a.split("").sort((function(){return Math.random()-.5})).join("")},performAutoLogin:function(){var e=w(i.a.mark((function e(t,r,a){var s,n,u,l,p,d,m,f=this;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.handleChangeCheckCode(),s=r[t],n=Object(c["a"])(s,this.encryptedString.key,this.encryptedString.iv),u=Object(c["a"])(a,this.encryptedString.key,this.encryptedString.iv),l={username:n,password:u,captcha:"AUTO_LOGIN_2025",checkKey:this.currdatetime,remember_me:!0,loginType:"website"},e.next=9,Object(o["c"])(l);case 9:if(p=e.sent,200!==p.code&&"200"!==p.code){e.next=21;break}this.$notification.success({message:"欢迎加入智界AIGC！",description:"您已成功注册并登录，账户已创建为无密码模式，今后可直接使用".concat("phone"===t?"手机号":"邮箱","验证码登录！"),placement:"topRight",duration:6}),d=p.result,m=d.userInfo,g["default"].ls.set(h["a"],d.token,6048e5),g["default"].ls.set(h["v"],m.username,6048e5),g["default"].ls.set(h["u"],m,6048e5),g["default"].ls.set(h["s"],d.sysAllDictItems,6048e5),setTimeout((function(){f.$router.push("/usercenter")}),1500),e.next=22;break;case 21:throw new Error(p.message||"自动登录失败");case 22:e.next=28;break;case 24:e.prev=24,e.t0=e["catch"](0),this.$notification.error({message:"注册成功，但自动登录失败",description:"请手动使用验证码登录",placement:"topRight",duration:4});case 28:case"end":return e.stop()}}),e,this,[[0,24]])})));function t(t,r,a){return e.apply(this,arguments)}return t}(),initAnimations:function(){var e=l["a"].timeline();e.to(this.$refs.loginInfo,{duration:.8,x:0,opacity:1,ease:"power3.out"}),e.to(this.$refs.loginContainer,{duration:.8,x:0,opacity:1,ease:"power3.out"},"-=0.6"),e.to(".feature-item",{duration:.5,y:0,opacity:1,stagger:.08,ease:"power2.out"},"-=0.4")}}},k=y,L=(r("24ec"),r("2877")),$=Object(L["a"])(k,a,s,!1,null,"7ef8aeb4",null);t["default"]=$.exports},c2dc:function(e,t,r){}}]);