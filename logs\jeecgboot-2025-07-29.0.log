2025-07-29 00:04:56.757 [SpringContextShutdownHook] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:0
2025-07-29 00:04:56.876 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-29 00:04:56.881 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-29 00:04:56.881 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 shutting down.
2025-07-29 00:04:56.881 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 paused.
2025-07-29 00:04:56.882 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753717258951 shutdown complete.
2025-07-29 00:04:56.897 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-29 00:04:56.901 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-29 00:04:56.905 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-29 00:04:56.906 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-29 00:05:00.775 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-29 00:05:00.802 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 32192 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-29 00:05:00.802 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-29 00:05:01.119 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-29 00:05:02.125 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 00:05:02.126 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 00:05:02.252 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 117ms. Found 0 Redis repository interfaces.
2025-07-29 00:05:02.373 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-29 00:05:02.373 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-29 00:05:02.374 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-29 00:05:02.438 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-29 00:05:02.439 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-29 00:05:02.439 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-29 00:05:02.439 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-29 00:05:02.439 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-29 00:05:02.440 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-29 00:05:02.440 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-29 00:05:02.440 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-29 00:05:02.440 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-29 00:05:02.440 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-29 00:05:02.593 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.596 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.598 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.598 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.599 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.599 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.600 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.602 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.603 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.604 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.605 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.605 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.606 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.606 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.607 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.607 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.608 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#50598a1a#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.608 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.627 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.630 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.682 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.730 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$f5d56bbd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:02.766 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.127 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-29 00:05:03.128 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-29 00:05:03.131 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.133 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.302 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$279495] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.309 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.318 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$13377bed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.345 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$a9b20174] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 00:05:03.547 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-29 00:05:03.553 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 00:05:03.554 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-29 00:05:03.554 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-29 00:05:03.661 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 00:05:03.661 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 2826 ms
2025-07-29 00:05:04.120 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-29 00:05:04.121 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-29 00:05:04.121 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 00:05:05.332 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-29 00:05:06.062 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-29 00:05:06.063 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 00:05:06.154 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-29 00:05:06.156 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 00:05:06.157 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-29 00:05:06.157 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-29 00:05:06.841 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-29 00:05:06.842 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 00:05:06.843 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-29 00:05:06.844 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 00:05:06.844 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-29 00:05:06.844 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-29 00:05:07.142 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-29 00:05:07.393 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-29 00:05:07.918 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 00:05:07.928 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 00:05:07.969 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-29 00:05:08.016 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 00:05:08.024 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 00:05:08.648 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-29 00:05:08.650 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-29 00:05:08.661 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 00:05:08.661 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 00:05:08.666 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-29 00:05:08.668 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-29 00:05:08.669 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753718708649'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-29 00:05:08.669 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-29 00:05:08.669 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-29 00:05:08.669 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@20706e70
2025-07-29 00:05:11.640 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-29 00:05:12.810 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-29 00:05:13.071 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-29 00:05:13.107 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 00:05:13.223 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-29 00:05:13.224 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-29 00:05:13.226 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-29 00:05:14.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 00:05:14.684 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-29 00:05:14.686 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-29 00:05:14.690 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-29 00:05:15.051 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-29 00:05:15.308 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-29 00:05:15.320 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-29 00:05:15.330 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-29 00:05:15.353 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-29 00:05:15.358 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-29 00:05:15.360 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-29 00:05:15.362 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-29 00:05:15.364 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-29 00:05:15.370 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-29 00:05:15.385 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-29 00:05:15.402 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-29 00:05:15.406 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-29 00:05:15.408 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-29 00:05:15.410 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-29 00:05:15.412 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-29 00:05:15.425 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-29 00:05:15.433 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-29 00:05:15.458 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-29 00:05:15.463 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-29 00:05:15.465 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-29 00:05:15.468 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-29 00:05:15.472 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-29 00:05:15.480 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-29 00:05:15.495 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-29 00:05:15.497 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-29 00:05:15.499 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-29 00:05:15.501 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-29 00:05:15.503 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-29 00:05:15.510 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-29 00:05:15.520 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-29 00:05:15.526 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-29 00:05:15.528 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-29 00:05:15.532 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-29 00:05:15.540 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-29 00:05:15.546 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-29 00:05:15.558 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-29 00:05:15.564 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-29 00:05:15.566 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-29 00:05:15.568 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-29 00:05:15.570 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-29 00:05:15.575 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-29 00:05:15.579 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-29 00:05:15.594 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-29 00:05:15.611 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-29 00:05:15.613 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-29 00:05:15.615 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-29 00:05:15.618 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-29 00:05:15.620 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-29 00:05:15.625 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-29 00:05:15.637 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-29 00:05:15.639 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-29 00:05:15.641 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-29 00:05:15.644 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-29 00:05:15.647 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-29 00:05:15.653 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-29 00:05:15.673 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-29 00:05:15.681 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-29 00:05:15.682 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-29 00:05:15.683 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-29 00:05:15.691 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-29 00:05:15.697 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-29 00:05:15.715 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-29 00:05:15.722 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-29 00:05:15.724 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-29 00:05:15.726 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-29 00:05:15.737 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-29 00:05:15.743 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-29 00:05:15.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-29 00:05:15.754 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-29 00:05:15.757 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-29 00:05:15.759 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-29 00:05:15.761 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-29 00:05:15.766 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-29 00:05:15.777 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-29 00:05:15.778 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-29 00:05:15.780 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-29 00:05:15.781 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-29 00:05:15.784 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-29 00:05:15.790 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-29 00:05:15.801 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-29 00:05:15.803 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-29 00:05:15.805 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-29 00:05:15.807 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-29 00:05:15.808 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-29 00:05:15.817 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-29 00:05:15.826 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-29 00:05:15.828 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-29 00:05:15.830 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-29 00:05:15.832 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-29 00:05:15.834 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-29 00:05:15.839 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-29 00:05:16.013 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-29 00:05:16.021 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-29 00:05:16.026 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-29 00:05:16.031 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-29 00:05:16.034 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-29 00:05:16.048 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-29 00:05:16.129 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-29 00:05:16.132 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-29 00:05:16.136 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-29 00:05:16.141 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-29 00:05:16.143 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-29 00:05:16.148 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-29 00:05:16.155 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-29 00:05:16.157 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-29 00:05:16.159 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-29 00:05:16.161 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-29 00:05:16.162 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-29 00:05:16.167 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-29 00:05:16.172 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-29 00:05:16.175 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-29 00:05:16.178 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-29 00:05:16.181 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-29 00:05:16.184 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-29 00:05:16.191 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-29 00:05:16.199 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-29 00:05:16.201 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-29 00:05:16.202 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-29 00:05:16.205 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-29 00:05:16.207 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-29 00:05:16.212 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-29 00:05:16.224 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-29 00:05:16.226 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-29 00:05:16.228 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-29 00:05:16.230 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-29 00:05:16.233 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-29 00:05:16.241 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-29 00:05:16.258 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-29 00:05:16.260 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-29 00:05:16.268 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-29 00:05:16.271 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-29 00:05:16.273 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-29 00:05:16.280 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-29 00:05:16.299 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-29 00:05:16.304 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-29 00:05:16.306 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-29 00:05:16.308 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-29 00:05:16.310 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-29 00:05:16.336 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-29 00:05:16.400 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-29 00:05:16.447 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-29 00:05:16.449 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-29 00:05:16.454 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-29 00:05:18.324 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 17.983 seconds (JVM running for 18.878)
2025-07-29 00:05:18.330 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-29 00:05:18.417 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 00:05:18.417 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-29 00:05:18.434 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 17 ms
2025-07-29 00:05:18.603 [http-nio-8080-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:1
2025-07-29 00:06:20.958 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:06:20.959 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:20.983 [http-nio-8080-exec-9] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:06:20.985 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:06:20.985 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:06:20.992 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:06:20.992 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:20.999 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:21.003 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:21.006 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:06:21.007 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:21.008 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:06:21.008 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:21.596 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:21.596 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:21.628 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:06:21.629 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:06:21.764 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:06:21.765 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:06:21.765 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:29.571 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:29.571 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:31.503 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:31.503 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:32.609 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:32.609 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:33.288 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:33.289 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:34.465 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:34.465 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:35.791 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:35.791 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:36.814 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:36.815 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:06:37.327 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:06:37.327 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.801 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:09.802 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.809 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:09.810 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.831 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:09.832 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.831 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:09.833 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.836 [http-nio-8080-exec-9] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:08:09.837 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:08:09.837 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:08:09.838 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:09.838 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.842 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:09.842 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:09.971 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:08:09.972 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:08:10.136 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:08:10.138 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:08:10.138 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.172 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:15.172 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.182 [http-nio-8080-exec-7] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:08:15.184 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:08:15.185 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:08:15.185 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:15.185 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.248 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:15.249 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:15.250 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:08:15.250 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.250 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:15.250 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.250 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.248 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:15.369 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:08:15.370 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:08:15.427 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:08:15.427 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:08:15.428 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:30.147 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:30.148 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:30.573 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:30.574 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:30.950 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:30.951 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:31.278 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:31.279 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:31.615 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:31.615 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:31.993 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:31.994 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:32.305 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:32.305 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:32.849 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:32.850 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:08:33.323 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:08:33.323 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:11.137 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:11.137 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:11.617 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:11.618 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:12.045 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:12.045 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:12.629 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:12.629 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:13.113 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:13.114 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:13.560 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:13.560 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:18.197 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:18.197 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:18.666 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:18.667 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:24.807 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:24.807 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:25.412 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:25.413 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.219 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:09:26.219 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.226 [http-nio-8080-exec-1] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:09:26.226 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:09:26.227 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:09:26.228 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:26.231 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.232 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:09:26.232 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.241 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:09:26.241 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.244 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:09:26.244 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.244 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:09:26.245 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:09:26.328 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:09:26.329 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:09:26.412 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:09:26.412 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:09:26.413 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:49.337 [http-nio-8080-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:0
2025-07-29 00:26:51.419 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:26:51.419 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.427 [http-nio-8080-exec-2] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:26:51.428 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:26:51.428 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:26:51.429 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:26:51.429 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:26:51.429 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:26:51.429 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.429 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.429 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.432 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:26:51.433 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.437 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:26:51.438 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:51.594 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:26:51.595 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:26:55.690 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:26:55.691 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:26:55.691 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:59.278 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 00:26:59.278 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 00:26:59.278 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 00:26:59.282 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 00:26:59.282 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 00:26:59.282 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 00:26:59.385 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-29 00:26:59.385 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:59.385 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-29 00:26:59.386 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:26:59.386 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-29 00:26:59.388 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:27:02.998 [http-nio-8080-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:58 - 【websocket消息】有新的连接，总数为:1
2025-07-29 00:27:03.150 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1194 - 管理员查询提现申请列表 - 页码: 1, 大小: 10, 状态: null, 用户名: null, 支付宝信息: null
2025-07-29 00:30:08.396 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.hH4xmfKDAEW5ipQA-R5VtPcDrGDIxwT9TaM-kbF2PrQ
2025-07-29 00:30:08.396 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: ***********, 分类: null, 关键词: null
2025-07-29 00:30:08.396 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: ***********
2025-07-29 00:30:08.404 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: ***********, 订单类型: null, 状态: null
2025-07-29 00:30:08.406 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1943747722066554881, userId=1943747721907171330, username=***********, nickname=智界用户qO6NG6, phone=***********, email=null, avatar=null, accountBalance=0.00, frozenBalance=0.00, apiKey=ak_4387511df6614bb3af1723ff44c15c2a, passwordChanged=0, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=null, myInviteCode=ZJ194EB9, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=1, totalCommission=1029.70, availableCommission=50.00, registerSource=phone, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=1, status=1, createBy=null, createTime=Sat Jul 12 03:02:25 CST 2025, updateBy=***********, updateTime=Mon Jul 28 22:48:34 CST 2025, sysOrgCode=null)
2025-07-29 00:30:08.410 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=0, accountBalance=0.00, totalRecharge=0.00}
2025-07-29 00:30:08.412 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 00:30:08.412 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:08.413 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-29 00:30:08.416 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 00:30:08.416 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:08.416 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-29 00:30:08.418 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:08.418 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:08.421 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:08.421 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:08.421 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************.hH4xmfKDAEW5ipQA-R5VtPcDrGDIxwT9TaM-kbF2PrQ
2025-07-29 00:30:08.422 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: ***********
2025-07-29 00:30:08.430 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=0.00, user_create_time=2025-07-12 03:02:25.0, password_changed=0, avatar=null, total_recharge=0.00, member_expire_time=null, realname=智界用户qO6NG6, current_role=普通用户, total_consumption=0.00, user_id=1943747721907171330, phone=***********, api_key=ak_4387511df6614bb3af1723ff44c15c2a, nickname=智界用户qO6NG6, email=null, username=***********}
2025-07-29 00:30:08.433 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:08.433 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.082 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:09.083 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.084 [http-nio-8080-exec-5] INFO  o.j.m.s.s.impl.InviteCodeGeneratorServiceImpl:55 - 用户 1943747721907171330 已有邀请码: ZJ194EB9
2025-07-29 00:30:09.084 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1496 - 为用户 *********** 使用新服务获取/生成邀请码: ZJ194EB9
2025-07-29 00:30:09.084 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:1518 - 生成邀请链接成功 - 用户: ***********, 链接: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:30:09.085 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:30:09.086 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.086 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:09.086 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.087 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:09.087 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.088 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 00:30:09.088 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.094 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 00:30:09.094 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:09.213 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3724 - 开始生成邀请二维码，URL: http://localhost:8080/login?ref=ZJ194EB9
2025-07-29 00:30:09.213 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3743 - 域名: localhost, 存储路径: qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:30:12.075 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3755 - 二维码已存在，直接返回CDN地址: https://cdn.aigcview.com/qrcode/localhost_8080/qr_1403769178.png
2025-07-29 00:30:12.075 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 7
2025-07-29 00:30:12.075 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 00:30:24.969 [http-nio-8080-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:68 - 【websocket消息】连接断开，总数为:0
2025-07-29 00:30:32.732 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-29 00:30:32.735 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-29 00:30:32.736 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 shutting down.
2025-07-29 00:30:32.736 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 paused.
2025-07-29 00:30:32.736 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753718708649 shutdown complete.
2025-07-29 00:30:32.743 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-29 00:30:32.744 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-29 00:30:32.746 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-29 00:30:32.747 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-29 14:36:33.194 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-29 14:36:33.220 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 5880 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-29 14:36:33.220 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-29 14:36:33.569 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-29 14:36:34.791 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:36:34.796 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 14:36:34.939 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 136ms. Found 0 Redis repository interfaces.
2025-07-29 14:36:35.091 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-29 14:36:35.091 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-29 14:36:35.096 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-29 14:36:35.172 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-29 14:36:35.177 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-29 14:36:35.338 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.343 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.350 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.350 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#3b0d3a63#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.373 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.374 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.431 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.479 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.485 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$8b0fb481] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.944 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-29 14:36:35.947 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-29 14:36:35.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:35.979 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.125 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.128 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9561dd59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.137 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.148 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$a871c4b1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3eec4a38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:36:36.415 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-29 14:36:36.422 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 14:36:36.425 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-29 14:36:36.425 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-29 14:36:36.576 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 14:36:36.577 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3322 ms
2025-07-29 14:36:37.177 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-29 14:36:37.180 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-29 14:36:37.180 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 14:36:38.457 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-29 14:36:39.226 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-29 14:36:39.226 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 14:36:39.335 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-29 14:36:39.339 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 14:36:39.340 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-29 14:36:39.340 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-29 14:36:40.422 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-29 14:36:40.422 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 14:36:40.425 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-29 14:36:40.428 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 14:36:40.428 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-29 14:36:40.428 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-29 14:36:40.684 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-29 14:36:41.039 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-29 14:36:41.703 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 14:36:41.714 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 14:36:41.754 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-29 14:36:41.804 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 14:36:41.813 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 14:36:42.394 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-29 14:36:42.397 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-29 14:36:42.407 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:36:42.407 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 14:36:42.413 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-29 14:36:42.416 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-29 14:36:42.416 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753771002395'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-29 14:36:42.418 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-29 14:36:42.418 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-29 14:36:42.418 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d2a4598
2025-07-29 14:36:45.148 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-29 14:36:46.294 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-29 14:36:46.459 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-29 14:36:46.492 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 14:36:46.589 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-29 14:36:46.589 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-29 14:36:46.595 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-29 14:36:47.731 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 14:36:47.767 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-29 14:36:47.767 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-29 14:36:47.772 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-29 14:36:48.011 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-29 14:36:48.211 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-29 14:36:48.220 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-29 14:36:48.237 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-29 14:36:48.255 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-29 14:36:48.258 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-29 14:36:48.260 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-29 14:36:48.262 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-29 14:36:48.262 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-29 14:36:48.267 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-29 14:36:48.273 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-29 14:36:48.284 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-29 14:36:48.289 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-29 14:36:48.290 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-29 14:36:48.290 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-29 14:36:48.295 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-29 14:36:48.302 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-29 14:36:48.308 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-29 14:36:48.327 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-29 14:36:48.332 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-29 14:36:48.334 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-29 14:36:48.337 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-29 14:36:48.337 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-29 14:36:48.348 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-29 14:36:48.361 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-29 14:36:48.361 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-29 14:36:48.361 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-29 14:36:48.361 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-29 14:36:48.367 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-29 14:36:48.373 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-29 14:36:48.381 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-29 14:36:48.385 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-29 14:36:48.387 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-29 14:36:48.389 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-29 14:36:48.396 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-29 14:36:48.402 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-29 14:36:48.414 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-29 14:36:48.418 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-29 14:36:48.420 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-29 14:36:48.420 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-29 14:36:48.420 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-29 14:36:48.428 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-29 14:36:48.432 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-29 14:36:48.444 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-29 14:36:48.461 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-29 14:36:48.465 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-29 14:36:48.467 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-29 14:36:48.467 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-29 14:36:48.467 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-29 14:36:48.473 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-29 14:36:48.485 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-29 14:36:48.487 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-29 14:36:48.487 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-29 14:36:48.490 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-29 14:36:48.491 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-29 14:36:48.497 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-29 14:36:48.514 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-29 14:36:48.521 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-29 14:36:48.524 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-29 14:36:48.526 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-29 14:36:48.532 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-29 14:36:48.538 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-29 14:36:48.557 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-29 14:36:48.563 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-29 14:36:48.565 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-29 14:36:48.567 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-29 14:36:48.573 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-29 14:36:48.579 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-29 14:36:48.589 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-29 14:36:48.590 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-29 14:36:48.590 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-29 14:36:48.590 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-29 14:36:48.595 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-29 14:36:48.612 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-29 14:36:48.626 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-29 14:36:48.627 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-29 14:36:48.627 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-29 14:36:48.630 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-29 14:36:48.631 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-29 14:36:48.636 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-29 14:36:48.643 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-29 14:36:48.645 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-29 14:36:48.645 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-29 14:36:48.648 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-29 14:36:48.649 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-29 14:36:48.655 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-29 14:36:48.662 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-29 14:36:48.662 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-29 14:36:48.665 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-29 14:36:48.667 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-29 14:36:48.669 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-29 14:36:48.673 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-29 14:36:48.812 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-29 14:36:48.820 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-29 14:36:48.823 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-29 14:36:48.827 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-29 14:36:48.827 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-29 14:36:48.837 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-29 14:36:48.914 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-29 14:36:48.918 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-29 14:36:48.920 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-29 14:36:48.922 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-29 14:36:48.922 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-29 14:36:48.927 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-29 14:36:48.932 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-29 14:36:48.936 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-29 14:36:48.938 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-29 14:36:48.942 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-29 14:36:48.943 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-29 14:36:48.949 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-29 14:36:48.954 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-29 14:36:48.955 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-29 14:36:48.957 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-29 14:36:48.957 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-29 14:36:48.960 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-29 14:36:48.962 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-29 14:36:48.971 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-29 14:36:48.973 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-29 14:36:48.973 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-29 14:36:48.973 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-29 14:36:48.978 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-29 14:36:48.979 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-29 14:36:48.990 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-29 14:36:48.990 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-29 14:36:48.996 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-29 14:36:48.997 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-29 14:36:48.997 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-29 14:36:49.002 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-29 14:36:49.014 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-29 14:36:49.018 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-29 14:36:49.024 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-29 14:36:49.027 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-29 14:36:49.027 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-29 14:36:49.037 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-29 14:36:49.053 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-29 14:36:49.057 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-29 14:36:49.059 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-29 14:36:49.061 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-29 14:36:49.061 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-29 14:36:49.083 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-29 14:36:49.137 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-29 14:36:49.185 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-29 14:36:49.185 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-29 14:36:49.190 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-29 14:36:50.973 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 18.269 seconds (JVM running for 19.345)
2025-07-29 14:36:50.980 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-29 14:36:51.637 [http-nio-8080-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:36:51.637 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-29 14:36:51.648 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 11 ms
2025-07-29 14:36:51.783 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 14:36:51.783 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 14:36:51.783 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-29 14:36:51.789 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 14:36:51.789 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 14:36:51.789 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-29 14:36:52.494 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-29 14:36:52.494 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-29 14:36:52.494 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-29 14:36:52.495 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:52.495 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:52.495 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:54.457 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-29 14:36:54.457 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-29 14:36:54.457 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-29 14:36:54.458 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-29 14:36:54.477 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-29 14:36:54.492 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=155, accountBalance=100000.00, totalRecharge=0.00}
2025-07-29 14:36:54.493 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-29 14:36:54.493 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-29 14:36:54.493 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0
2025-07-29 14:36:54.494 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-29 14:36:54.494 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:36:54.494 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 14:36:54.494 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:54.494 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:54.495 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:36:54.495 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 14:36:54.495 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:54.495 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:54.501 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-29 14:36:54.504 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:36:54.505 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:57.113 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-29 14:36:57.123 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:3388 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-29 14:36:57.125 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:36:57.125 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:36:57.125 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-29 14:36:57.127 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:36:57.127 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:37:03.387 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 1, 支付方式: alipay
2025-07-29 14:37:03.400 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:1832 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753771023390_e9ca23d6
2025-07-29 14:37:03.406 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 14:37:03.406 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:37:03.420 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:61 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753771023390_e9ca23d6, 金额: 1
2025-07-29 14:37:03.420 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:66 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753771023390_e9ca23d6, 金额: 1
2025-07-29 14:37:03.431 [http-nio-8080-exec-8] ERROR o.jeecg.modules.system.controller.AlipayController:73 - 💰 创建支付宝支付订单失败
java.lang.RuntimeException: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&biz_content={"out_trade_no":"RECHARGE_1753771023390_e9ca23d6","total_amount":"1","subject":"智界Aigc账户充值","body":"充值金额：¥1","product_code":"FAST_INSTANT_TRADE_PAY"}&charset=UTF-8&format=json&method=alipay.trade.page.pay&sign_type=RSA2&timestamp=2025-07-29 14:37:03&version=1.0，charset=UTF-8，privateKeySize=0
	at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:25)
	at com.alipay.api.AbstractAlipayClient.getRequestHolderWithSign(AbstractAlipayClient.java:836)
	at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:634)
	at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:629)
	at org.jeecg.modules.system.service.AlipayService.createPayOrder(AlipayService.java:84)
	at org.jeecg.modules.system.controller.AlipayController.createPayOrder(AlipayController.java:63)
	at org.jeecg.modules.system.controller.AlipayController$$FastClassBySpringCGLIB$$d413598a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:57)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at org.jeecg.modules.system.controller.AlipayController$$EnhancerBySpringCGLIB$$b92fe164.createPayOrder(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&biz_content={"out_trade_no":"RECHARGE_1753771023390_e9ca23d6","total_amount":"1","subject":"智界Aigc账户充值","body":"充值金额：¥1","product_code":"FAST_INSTANT_TRADE_PAY"}&charset=UTF-8&format=json&method=alipay.trade.page.pay&sign_type=RSA2&timestamp=2025-07-29 14:37:03&version=1.0，charset=UTF-8，privateKeySize=0
	at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:86)
	at com.alipay.api.internal.util.AlipaySignature.rsaSign(AlipaySignature.java:422)
	at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:23)
	... 120 common frames omitted
Caused by: com.alipay.api.AlipayApiException: 私钥[privateKey]不可为空
	at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:70)
	... 122 common frames omitted
2025-07-29 14:37:03.432 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-29 14:37:03.432 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:37:59.226 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-29 14:37:59.235 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-29 14:37:59.238 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:37:59.238 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:37:59.240 [http-nio-8080-exec-8] INFO  o.j.m.d.u.controller.UserCenterDataController:3388 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-29 14:37:59.244 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:37:59.244 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:41:19.434 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-29 14:41:19.450 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-29 14:41:19.452 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:41:19.453 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:41:19.454 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:3388 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-29 14:41:19.457 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:41:19.457 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:48:57.500 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-29 14:48:57.503 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-29 14:48:57.503 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 shutting down.
2025-07-29 14:48:57.503 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 paused.
2025-07-29 14:48:57.504 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771002395 shutdown complete.
2025-07-29 14:48:57.509 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-29 14:48:57.511 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-29 14:48:57.514 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-29 14:48:57.514 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-29 14:49:01.088 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-29 14:49:01.109 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 13836 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-29 14:49:01.110 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-29 14:49:01.415 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-29 14:49:02.543 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 14:49:02.545 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 14:49:02.679 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 124ms. Found 0 Redis repository interfaces.
2025-07-29 14:49:02.803 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-29 14:49:02.804 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-29 14:49:02.805 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-29 14:49:02.884 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-29 14:49:02.885 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-29 14:49:02.885 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-29 14:49:02.885 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-29 14:49:02.885 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-29 14:49:02.886 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-29 14:49:02.886 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-29 14:49:02.886 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-29 14:49:02.886 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-29 14:49:02.886 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-29 14:49:03.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.053 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.053 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.055 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.055 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.056 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.057 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.058 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.058 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.060 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.060 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.061 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.061 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.062 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.062 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#437ed416#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.062 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.080 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.083 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.139 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.194 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.196 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$ba91bd19] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.235 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.647 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-29 14:49:03.649 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-29 14:49:03.651 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.654 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.829 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c4e3e5f1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.835 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.845 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$d7f3cd49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$6e6e52d0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:03.877 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 14:49:04.106 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-29 14:49:04.114 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 14:49:04.114 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-29 14:49:04.114 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-29 14:49:04.258 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 14:49:04.258 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3120 ms
2025-07-29 14:49:04.765 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-29 14:49:04.766 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-29 14:49:04.766 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 14:49:06.036 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-29 14:49:06.757 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-29 14:49:06.757 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 14:49:06.850 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-29 14:49:06.853 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 14:49:06.853 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-29 14:49:06.853 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-29 14:49:07.726 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-29 14:49:07.726 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-29 14:49:07.729 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-29 14:49:07.730 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-29 14:49:07.731 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-29 14:49:07.731 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-29 14:49:08.258 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-29 14:49:08.618 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-29 14:49:09.095 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 14:49:09.105 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 14:49:09.142 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-29 14:49:09.185 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-29 14:49:09.194 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-29 14:49:09.766 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-29 14:49:09.769 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-29 14:49:09.778 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 14:49:09.778 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 14:49:09.782 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-29 14:49:09.784 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-29 14:49:09.784 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753771749768'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-29 14:49:09.785 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-29 14:49:09.785 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-29 14:49:09.785 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@628f0936
2025-07-29 14:49:12.910 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-29 14:49:13.920 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-29 14:49:14.069 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-29 14:49:14.099 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-29 14:49:14.197 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-29 14:49:14.198 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-29 14:49:14.200 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-29 14:49:15.117 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-29 14:49:15.145 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-29 14:49:15.147 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-29 14:49:15.151 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-29 14:49:15.420 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-29 14:49:15.649 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-29 14:49:15.662 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-29 14:49:15.673 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-29 14:49:15.698 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-29 14:49:15.702 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-29 14:49:15.705 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-29 14:49:15.708 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-29 14:49:15.710 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-29 14:49:15.716 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-29 14:49:15.723 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-29 14:49:15.739 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-29 14:49:15.744 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-29 14:49:15.747 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-29 14:49:15.749 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-29 14:49:15.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-29 14:49:15.760 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-29 14:49:15.767 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-29 14:49:15.789 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-29 14:49:15.792 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-29 14:49:15.794 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-29 14:49:15.796 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-29 14:49:15.800 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-29 14:49:15.807 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-29 14:49:15.820 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-29 14:49:15.822 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-29 14:49:15.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-29 14:49:15.825 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-29 14:49:15.827 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-29 14:49:15.832 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-29 14:49:15.840 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-29 14:49:15.844 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-29 14:49:15.846 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-29 14:49:15.848 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-29 14:49:15.854 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-29 14:49:15.860 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-29 14:49:15.873 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-29 14:49:15.879 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-29 14:49:15.880 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-29 14:49:15.882 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-29 14:49:15.883 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-29 14:49:15.889 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-29 14:49:15.894 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-29 14:49:15.911 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-29 14:49:15.928 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-29 14:49:15.930 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-29 14:49:15.932 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-29 14:49:15.935 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-29 14:49:15.937 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-29 14:49:15.943 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-29 14:49:15.954 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-29 14:49:15.955 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-29 14:49:15.958 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-29 14:49:15.960 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-29 14:49:15.961 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-29 14:49:15.968 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-29 14:49:15.989 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-29 14:49:15.997 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-29 14:49:15.999 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-29 14:49:16.002 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-29 14:49:16.009 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-29 14:49:16.015 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-29 14:49:16.033 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-29 14:49:16.040 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-29 14:49:16.042 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-29 14:49:16.043 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-29 14:49:16.055 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-29 14:49:16.061 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-29 14:49:16.072 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-29 14:49:16.074 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-29 14:49:16.076 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-29 14:49:16.077 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-29 14:49:16.082 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-29 14:49:16.092 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-29 14:49:16.102 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-29 14:49:16.103 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-29 14:49:16.104 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-29 14:49:16.105 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-29 14:49:16.107 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-29 14:49:16.112 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-29 14:49:16.119 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-29 14:49:16.121 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-29 14:49:16.122 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-29 14:49:16.124 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-29 14:49:16.126 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-29 14:49:16.131 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-29 14:49:16.137 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-29 14:49:16.139 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-29 14:49:16.141 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-29 14:49:16.142 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-29 14:49:16.144 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-29 14:49:16.148 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-29 14:49:16.312 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-29 14:49:16.319 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-29 14:49:16.324 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-29 14:49:16.328 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-29 14:49:16.331 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-29 14:49:16.345 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-29 14:49:16.428 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-29 14:49:16.434 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-29 14:49:16.436 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-29 14:49:16.439 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-29 14:49:16.441 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-29 14:49:16.447 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-29 14:49:16.453 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-29 14:49:16.455 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-29 14:49:16.456 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-29 14:49:16.458 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-29 14:49:16.459 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-29 14:49:16.465 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-29 14:49:16.470 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-29 14:49:16.471 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-29 14:49:16.472 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-29 14:49:16.474 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-29 14:49:16.477 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-29 14:49:16.481 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-29 14:49:16.487 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-29 14:49:16.489 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-29 14:49:16.491 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-29 14:49:16.493 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-29 14:49:16.495 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-29 14:49:16.500 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-29 14:49:16.513 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-29 14:49:16.515 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-29 14:49:16.518 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-29 14:49:16.521 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-29 14:49:16.523 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-29 14:49:16.528 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-29 14:49:16.544 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-29 14:49:16.546 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-29 14:49:16.555 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-29 14:49:16.557 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-29 14:49:16.560 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-29 14:49:16.584 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-29 14:49:16.604 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-29 14:49:16.608 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-29 14:49:16.610 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-29 14:49:16.612 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-29 14:49:16.614 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-29 14:49:16.633 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-29 14:49:16.688 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-29 14:49:16.725 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-29 14:49:16.727 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-29 14:49:16.730 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-29 14:49:18.595 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 17.851 seconds (JVM running for 18.767)
2025-07-29 14:49:18.602 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-29 14:49:19.595 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 14:49:19.596 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-29 14:49:19.614 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 18 ms
2025-07-29 14:55:51.926 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:137 - 🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-29 14:55:51.927 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:259 - 🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null
2025-07-29 14:55:51.927 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:475 - 🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null
2025-07-29 14:55:51.927 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:140 - 🔍 getUserCenterOverview - 解析出的用户名: admin
2025-07-29 14:55:51.955 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1573 - 🔍 获取交易记录 - 用户: admin, 类型: null, 开始日期: null, 结束日期: null, 关键词: null
2025-07-29 14:55:52.388 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:144 - 🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=ZJ19385F, usedInviteCode=null, inviterUserId=null, inviteCount=0, commissionLevel=1, validInviteCount=0, totalCommission=0.00, availableCommission=0.00, registerSource=manual, realNameVerified=0, realName=null, idCardNumber=null, deviceFingerprint=null, ipAddress=null, geoLocation=null, riskScore=0, accountStatus=1, emailVerified=0, phoneVerified=0, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=admin, updateTime=Mon Jul 28 15:11:54 CST 2025, sysOrgCode=A01)
2025-07-29 14:55:52.403 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:169 - 🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=155, accountBalance=100000.00, totalRecharge=0.00}
2025-07-29 14:55:52.406 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:366 - 🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0
2025-07-29 14:55:52.406 [http-nio-8080-exec-2] INFO  o.j.m.d.u.controller.UserCenterDataController:604 - 🎯 getUserOrderList - 查询结果: 总数=1, 当前页数据量=0
2025-07-29 14:55:52.406 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1657 - 🔍 获取交易记录成功 - 用户: admin, 总数: 0, 当前页: 1
2025-07-29 14:55:52.413 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 14:55:52.413 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:55:52.413 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:55:52.413 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 10
2025-07-29 14:55:52.413 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.414 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.414 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.414 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.426 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:186 - 🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************.5ySyBuaVf3vJIF90xxonVyXcD9P1hgAOH5koKtS3Zik
2025-07-29 14:55:52.427 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:189 - 🔍 getUserFullInfo - 解析出的用户名: admin
2025-07-29 14:55:52.434 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:55:52.434 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.437 [http-nio-8080-exec-9] INFO  o.j.m.d.u.controller.UserCenterDataController:193 - 🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}
2025-07-29 14:55:52.437 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:55:52.437 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:55:52.445 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:3388 - 💰 计算用户本月消费 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 本月消费: 0.00
2025-07-29 14:55:52.447 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-29 14:55:52.448 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:56:05.678 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1775 - 💰 创建充值订单 - 用户: admin, 金额: 0.01, 支付方式: alipay
2025-07-29 14:56:05.691 [http-nio-8080-exec-10] INFO  o.j.m.d.u.controller.UserCenterDataController:1832 - 💰 充值订单创建成功 - 订单号: RECHARGE_1753772165682_e9ca23d6
2025-07-29 14:56:05.732 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-29 14:56:05.732 [http-nio-8080-exec-10] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 14:56:05.745 [http-nio-8080-exec-8] INFO  o.jeecg.modules.system.controller.AlipayController:61 - 💰 创建支付宝支付订单请求 - 订单号: RECHARGE_1753772165682_e9ca23d6, 金额: 0.01
2025-07-29 14:56:05.746 [http-nio-8080-exec-8] INFO  org.jeecg.modules.system.service.AlipayService:66 - 💰 创建支付宝支付订单 - 订单号: RECHARGE_1753772165682_e9ca23d6, 金额: 0.01
2025-07-29 14:56:05.756 [http-nio-8080-exec-8] ERROR o.jeecg.modules.system.controller.AlipayController:73 - 💰 创建支付宝支付订单失败
java.lang.RuntimeException: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&biz_content={"out_trade_no":"RECHARGE_1753772165682_e9ca23d6","total_amount":"0.01","subject":"智界Aigc账户充值","body":"充值金额：¥0.01","product_code":"FAST_INSTANT_TRADE_PAY"}&charset=UTF-8&format=json&method=alipay.trade.page.pay&sign_type=RSA2&timestamp=2025-07-29 14:56:05&version=1.0，charset=UTF-8，privateKeySize=0
	at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:25)
	at com.alipay.api.AbstractAlipayClient.getRequestHolderWithSign(AbstractAlipayClient.java:836)
	at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:634)
	at com.alipay.api.AbstractAlipayClient.pageExecute(AbstractAlipayClient.java:629)
	at org.jeecg.modules.system.service.AlipayService.createPayOrder(AlipayService.java:84)
	at org.jeecg.modules.system.controller.AlipayController.createPayOrder(AlipayController.java:63)
	at org.jeecg.modules.system.controller.AlipayController$$FastClassBySpringCGLIB$$d413598a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:57)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at org.jeecg.modules.system.controller.AlipayController$$EnhancerBySpringCGLIB$$4b63550a.createPayOrder(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.alipay.api.AlipayApiException: RSA2签名遭遇异常，请检查私钥格式是否正确。私钥[privateKey]不可为空 content=alipay_sdk=alipay-sdk-java-4.38.200.ALL&biz_content={"out_trade_no":"RECHARGE_1753772165682_e9ca23d6","total_amount":"0.01","subject":"智界Aigc账户充值","body":"充值金额：¥0.01","product_code":"FAST_INSTANT_TRADE_PAY"}&charset=UTF-8&format=json&method=alipay.trade.page.pay&sign_type=RSA2&timestamp=2025-07-29 14:56:05&version=1.0，charset=UTF-8，privateKeySize=0
	at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:86)
	at com.alipay.api.internal.util.AlipaySignature.rsaSign(AlipaySignature.java:422)
	at com.alipay.api.DefaultSigner.sign(DefaultSigner.java:23)
	... 120 common frames omitted
Caused by: com.alipay.api.AlipayApiException: 私钥[privateKey]不可为空
	at com.alipay.api.internal.util.asymmetric.BaseAsymmetricEncryptor.sign(BaseAsymmetricEncryptor.java:70)
	... 122 common frames omitted
2025-07-29 14:56:05.758 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 9
2025-07-29 14:56:05.759 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-29 15:00:41.597 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-29 15:00:41.600 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-29 15:00:41.600 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771749768 shutting down.
2025-07-29 15:00:41.601 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771749768 paused.
2025-07-29 15:00:41.601 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753771749768 shutdown complete.
2025-07-29 15:00:41.606 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-29 15:00:41.608 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-29 15:00:41.614 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-29 15:00:41.614 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-29 15:00:45.160 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-29 15:00:45.192 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 38556 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-29 15:00:45.194 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-29 15:00:45.574 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-29 15:00:46.757 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-29 15:00:46.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 15:00:46.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 123ms. Found 0 Redis repository interfaces.
2025-07-29 15:00:47.009 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-29 15:00:47.011 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-29 15:00:47.011 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-29 15:00:47.089 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-29 15:00:47.089 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-29 15:00:47.089 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-29 15:00:47.090 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-29 15:00:47.251 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.254 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.255 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.255 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.256 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.257 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.257 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.261 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.262 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.262 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.263 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.263 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#6c0b51da#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.284 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.287 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.340 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.397 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$ae8f81fc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.435 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.826 [main] INFO  org.jeecg.config.shiro.ShiroConfig:221 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-29 15:00:47.827 [main] INFO  org.jeecg.config.shiro.ShiroConfig:239 - ===============(2)创建RedisManager,连接Redis..
2025-07-29 15:00:47.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.832 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:47.868 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.011 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.018 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b8e1aad4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.034 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$cbf1922c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.068 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$626c17b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.072 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 15:00:48.292 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-29 15:00:48.299 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-29 15:00:48.299 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-29 15:00:48.299 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-29 15:00:48.450 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 15:00:48.451 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3216 ms
