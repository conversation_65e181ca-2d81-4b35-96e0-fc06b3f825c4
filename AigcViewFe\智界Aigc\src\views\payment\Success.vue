<template>
  <div class="payment-success-page">
    <div class="success-container">
      <!-- 成功图标 -->
      <div class="success-icon">
        <a-icon type="check-circle" theme="filled" />
      </div>
      
      <!-- 成功标题 -->
      <h1 class="success-title">支付成功！</h1>
      
      <!-- 订单信息 -->
      <div class="order-info" v-if="orderInfo">
        <div class="info-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderInfo.orderId }}</span>
        </div>
        <div class="info-item" v-if="orderInfo.amount">
          <span class="label">支付金额：</span>
          <span class="value amount">¥{{ orderInfo.amount }}</span>
        </div>
        <div class="info-item">
          <span class="label">支付时间：</span>
          <span class="value">{{ formatTime(new Date()) }}</span>
        </div>
      </div>
      
      <!-- 成功消息 -->
      <div class="success-message">
        <p>您的充值已成功完成，余额将在几分钟内到账。</p>
        <p>感谢您对智界AIGC的支持！</p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button type="primary" size="large" @click="goToUserCenter">
          查看余额
        </a-button>
        <a-button size="large" @click="goToMarket" style="margin-left: 16px">
          去购买插件
        </a-button>
        <a-button size="large" @click="goHome" style="margin-left: 16px">
          返回首页
        </a-button>
      </div>
      
      <!-- 温馨提示 -->
      <div class="tips">
        <a-alert
          message="温馨提示"
          description="如果余额未及时到账，请联系客服或查看交易记录。"
          type="info"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentSuccess',
  data() {
    return {
      orderInfo: null
    }
  },
  
  mounted() {
    this.loadOrderInfo()
  },
  
  methods: {
    loadOrderInfo() {
      // 从URL参数获取订单信息
      const orderId = this.$route.query.orderId
      if (orderId) {
        this.orderInfo = {
          orderId: orderId,
          amount: this.$route.query.amount || null
        }
      }
    },
    
    formatTime(date) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    goToUserCenter() {
      this.$router.push('/usercenter/credits')
    },
    
    goToMarket() {
      this.$router.push('/market')
    },
    
    goHome() {
      this.$router.push('/home')
    }
  }
}
</script>

<style scoped>
.payment-success-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-container {
  background: white;
  border-radius: 16px;
  padding: 48px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.success-icon {
  font-size: 80px;
  color: #52c41a;
  margin-bottom: 24px;
}

.success-title {
  font-size: 32px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 32px;
}

.order-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #8c8c8c;
  font-size: 14px;
}

.value {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.value.amount {
  color: #f5222d;
  font-size: 18px;
  font-weight: 600;
}

.success-message {
  margin-bottom: 32px;
  color: #595959;
  line-height: 1.6;
}

.action-buttons {
  margin-bottom: 32px;
}

.tips {
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .success-container {
    padding: 32px 24px;
  }
  
  .success-title {
    font-size: 24px;
  }
  
  .success-icon {
    font-size: 60px;
  }
  
  .action-buttons .ant-btn {
    margin: 8px 4px !important;
  }
}
</style>
