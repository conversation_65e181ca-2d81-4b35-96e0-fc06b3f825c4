{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue?vue&type=template&id=214e74b0&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue", "mtime": 1753756307330}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"payment-success-page\" }, [\n    _c(\"div\", { staticClass: \"success-container\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"success-icon\" },\n        [_c(\"a-icon\", { attrs: { type: \"check-circle\", theme: \"filled\" } })],\n        1\n      ),\n      _c(\"h1\", { staticClass: \"success-title\" }, [_vm._v(\"支付成功！\")]),\n      _vm.orderInfo\n        ? _c(\"div\", { staticClass: \"order-info\" }, [\n            _c(\"div\", { staticClass: \"info-item\" }, [\n              _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"订单号：\")]),\n              _c(\"span\", { staticClass: \"value\" }, [\n                _vm._v(_vm._s(_vm.orderInfo.orderId))\n              ])\n            ]),\n            _vm.orderInfo.amount\n              ? _c(\"div\", { staticClass: \"info-item\" }, [\n                  _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"支付金额：\")]),\n                  _c(\"span\", { staticClass: \"value amount\" }, [\n                    _vm._v(\"¥\" + _vm._s(_vm.orderInfo.amount))\n                  ])\n                ])\n              : _vm._e(),\n            _c(\"div\", { staticClass: \"info-item\" }, [\n              _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"支付时间：\")]),\n              _c(\"span\", { staticClass: \"value\" }, [\n                _vm._v(_vm._s(_vm.formatTime(new Date())))\n              ])\n            ])\n          ])\n        : _vm._e(),\n      _vm._m(0),\n      _c(\n        \"div\",\n        { staticClass: \"action-buttons\" },\n        [\n          _c(\n            \"a-button\",\n            {\n              attrs: { type: \"primary\", size: \"large\" },\n              on: { click: _vm.goToUserCenter }\n            },\n            [_vm._v(\"\\n        查看余额\\n      \")]\n          ),\n          _c(\n            \"a-button\",\n            {\n              staticStyle: { \"margin-left\": \"16px\" },\n              attrs: { size: \"large\" },\n              on: { click: _vm.goToMarket }\n            },\n            [_vm._v(\"\\n        去购买插件\\n      \")]\n          ),\n          _c(\n            \"a-button\",\n            {\n              staticStyle: { \"margin-left\": \"16px\" },\n              attrs: { size: \"large\" },\n              on: { click: _vm.goHome }\n            },\n            [_vm._v(\"\\n        返回首页\\n      \")]\n          )\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"tips\" },\n        [\n          _c(\"a-alert\", {\n            attrs: {\n              message: \"温馨提示\",\n              description: \"如果余额未及时到账，请联系客服或查看交易记录。\",\n              type: \"info\",\n              \"show-icon\": \"\"\n            }\n          })\n        ],\n        1\n      )\n    ])\n  ])\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"success-message\" }, [\n      _c(\"p\", [_vm._v(\"您的充值已成功完成，余额将在几分钟内到账。\")]),\n      _c(\"p\", [_vm._v(\"感谢您对智界AIGC的支持！\")])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}