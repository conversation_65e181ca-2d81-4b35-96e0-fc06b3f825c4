{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue?vue&type=template&id=b236e548&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\vue\\AigcWithdrawalList.vue", "mtime": 1753713664234}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"withdrawal-management\" },\n    [\n      _vm._m(0),\n      _c(\n        \"div\",\n        { staticClass: \"search-section\" },\n        [\n          _c(\n            \"a-card\",\n            { attrs: { bordered: false } },\n            [\n              _c(\n                \"a-form\",\n                {\n                  attrs: { layout: \"inline\", model: _vm.searchForm },\n                  on: { submit: _vm.handleSearch }\n                },\n                [\n                  _c(\n                    \"a-form-item\",\n                    { attrs: { label: \"申请状态\" } },\n                    [\n                      _c(\n                        \"a-select\",\n                        {\n                          staticStyle: { width: \"120px\" },\n                          attrs: { placeholder: \"请选择状态\", allowClear: \"\" },\n                          model: {\n                            value: _vm.searchForm.status,\n                            callback: function($$v) {\n                              _vm.$set(_vm.searchForm, \"status\", $$v)\n                            },\n                            expression: \"searchForm.status\"\n                          }\n                        },\n                        [\n                          _c(\"a-select-option\", { attrs: { value: 1 } }, [\n                            _vm._v(\"待审核\")\n                          ]),\n                          _c(\"a-select-option\", { attrs: { value: 2 } }, [\n                            _vm._v(\"已发放\")\n                          ]),\n                          _c(\"a-select-option\", { attrs: { value: 3 } }, [\n                            _vm._v(\"审核拒绝\")\n                          ]),\n                          _c(\"a-select-option\", { attrs: { value: 4 } }, [\n                            _vm._v(\"已取消\")\n                          ])\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-item\",\n                    { attrs: { label: \"申请时间\" } },\n                    [\n                      _c(\"a-range-picker\", {\n                        attrs: {\n                          format: \"YYYY-MM-DD\",\n                          placeholder: [\"开始时间\", \"结束时间\"]\n                        },\n                        model: {\n                          value: _vm.searchForm.dateRange,\n                          callback: function($$v) {\n                            _vm.$set(_vm.searchForm, \"dateRange\", $$v)\n                          },\n                          expression: \"searchForm.dateRange\"\n                        }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-item\",\n                    { attrs: { label: \"用户名\" } },\n                    [\n                      _c(\"a-input\", {\n                        staticStyle: { width: \"150px\" },\n                        attrs: { placeholder: \"请输入用户名\" },\n                        model: {\n                          value: _vm.searchForm.username,\n                          callback: function($$v) {\n                            _vm.$set(_vm.searchForm, \"username\", $$v)\n                          },\n                          expression: \"searchForm.username\"\n                        }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-item\",\n                    { attrs: { label: \"支付宝信息\" } },\n                    [\n                      _c(\"a-input\", {\n                        staticStyle: { width: \"150px\" },\n                        attrs: { placeholder: \"支付宝账号或姓名\" },\n                        model: {\n                          value: _vm.searchForm.alipayInfo,\n                          callback: function($$v) {\n                            _vm.$set(_vm.searchForm, \"alipayInfo\", $$v)\n                          },\n                          expression: \"searchForm.alipayInfo\"\n                        }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-form-item\",\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: { type: \"primary\", loading: _vm.loading },\n                          on: { click: _vm.handleSearch }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"search\" } }),\n                          _vm._v(\"\\n            搜索\\n          \")\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-button\",\n                        {\n                          staticStyle: { \"margin-left\": \"8px\" },\n                          on: { click: _vm.handleReset }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"reload\" } }),\n                          _vm._v(\"\\n            重置\\n          \")\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"table-section\" },\n        [\n          _c(\n            \"a-card\",\n            { attrs: { bordered: false } },\n            [\n              _c(\"a-table\", {\n                attrs: {\n                  columns: _vm.columns,\n                  \"data-source\": _vm.dataSource,\n                  loading: _vm.loading,\n                  pagination: _vm.pagination,\n                  \"row-key\": \"id\",\n                  scroll: { x: 1200 }\n                },\n                on: { change: _vm.handleTableChange },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"userInfo\",\n                    fn: function(text, record) {\n                      return [\n                        record\n                          ? _c(\"div\", { staticClass: \"user-info\" }, [\n                              _c(\"div\", { staticClass: \"username\" }, [\n                                _vm._v(_vm._s(record.username || \"-\"))\n                              ]),\n                              _c(\"div\", { staticClass: \"user-id\" }, [\n                                _vm._v(\"ID: \" + _vm._s(record.user_id || \"-\"))\n                              ])\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"amount\",\n                    fn: function(text, record) {\n                      return [\n                        record\n                          ? _c(\"div\", { staticClass: \"amount-info\" }, [\n                              _c(\"div\", { staticClass: \"amount\" }, [\n                                _vm._v(\n                                  \"¥\" +\n                                    _vm._s(\n                                      _vm.formatNumber(record.withdrawal_amount)\n                                    )\n                                )\n                              ])\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"alipayInfo\",\n                    fn: function(text, record) {\n                      return [\n                        record\n                          ? _c(\"div\", { staticClass: \"alipay-info\" }, [\n                              _c(\"div\", { staticClass: \"name\" }, [\n                                _vm._v(_vm._s(record.alipay_name || \"-\"))\n                              ]),\n                              _c(\"div\", { staticClass: \"account\" }, [\n                                _vm._v(_vm._s(record.alipay_account || \"-\"))\n                              ])\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"status\",\n                    fn: function(text, record) {\n                      return [\n                        record\n                          ? _c(\n                              \"a-tag\",\n                              {\n                                attrs: {\n                                  color: _vm.getStatusColor(\n                                    record && record.status\n                                  )\n                                }\n                              },\n                              [\n                                _vm._v(\n                                  \"\\n            \" +\n                                    _vm._s(\n                                      _vm.getStatusText(\n                                        record.status,\n                                        record.review_remark\n                                      )\n                                    ) +\n                                    \"\\n          \"\n                                )\n                              ]\n                            )\n                          : _c(\"span\", [_vm._v(\"-\")])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"applyTime\",\n                    fn: function(text, record) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              record && record.apply_time\n                                ? _vm.formatDateTime(record.apply_time)\n                                : \"-\"\n                            )\n                          )\n                        ])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"reviewTime\",\n                    fn: function(text, record) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              record && record.review_time\n                                ? _vm.formatDateTime(record.review_time)\n                                : \"-\"\n                            )\n                          )\n                        ])\n                      ]\n                    }\n                  },\n                  {\n                    key: \"action\",\n                    fn: function(text, record) {\n                      return [\n                        record\n                          ? _c(\n                              \"div\",\n                              { staticClass: \"action-buttons\" },\n                              [\n                                record.status === 1\n                                  ? _c(\n                                      \"a-button\",\n                                      {\n                                        attrs: {\n                                          type: \"primary\",\n                                          size: \"small\",\n                                          loading: record.approving\n                                        },\n                                        on: {\n                                          click: function($event) {\n                                            return _vm.handleApprove(record)\n                                          }\n                                        }\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n              审核通过\\n            \"\n                                        )\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                record.status === 1\n                                  ? _c(\n                                      \"a-button\",\n                                      {\n                                        staticStyle: { \"margin-left\": \"8px\" },\n                                        attrs: {\n                                          type: \"danger\",\n                                          size: \"small\",\n                                          loading: record.rejecting\n                                        },\n                                        on: {\n                                          click: function($event) {\n                                            return _vm.handleReject(record)\n                                          }\n                                        }\n                                      },\n                                      [\n                                        _vm._v(\n                                          \"\\n              审核拒绝\\n            \"\n                                        )\n                                      ]\n                                    )\n                                  : _vm._e(),\n                                _c(\n                                  \"a-button\",\n                                  {\n                                    staticStyle: { \"margin-left\": \"8px\" },\n                                    attrs: { size: \"small\" },\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.handleViewDetail(record)\n                                      }\n                                    }\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n              查看详情\\n            \"\n                                    )\n                                  ]\n                                )\n                              ],\n                              1\n                            )\n                          : _c(\"span\", [_vm._v(\"-\")])\n                      ]\n                    }\n                  }\n                ])\n              })\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"a-modal\",\n        {\n          attrs: { title: \"审核拒绝\", footer: null, width: \"500px\" },\n          model: {\n            value: _vm.showRejectModal,\n            callback: function($$v) {\n              _vm.showRejectModal = $$v\n            },\n            expression: \"showRejectModal\"\n          }\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"reject-modal\" },\n            [\n              _c(\"a-alert\", {\n                staticStyle: { \"margin-bottom\": \"20px\" },\n                attrs: {\n                  message: \"请填写拒绝原因\",\n                  type: \"warning\",\n                  \"show-icon\": \"\"\n                }\n              }),\n              _c(\n                \"a-form\",\n                { attrs: { layout: \"vertical\" } },\n                [\n                  _c(\n                    \"a-form-item\",\n                    { attrs: { label: \"拒绝原因\", required: \"\" } },\n                    [\n                      _c(\"a-textarea\", {\n                        attrs: {\n                          placeholder: \"请输入拒绝原因\",\n                          rows: 4,\n                          maxLength: 200\n                        },\n                        model: {\n                          value: _vm.rejectReason,\n                          callback: function($$v) {\n                            _vm.rejectReason = $$v\n                          },\n                          expression: \"rejectReason\"\n                        }\n                      })\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"modal-actions\" },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      on: {\n                        click: function($event) {\n                          _vm.showRejectModal = false\n                        }\n                      }\n                    },\n                    [_vm._v(\"\\n          取消\\n        \")]\n                  ),\n                  _c(\n                    \"a-button\",\n                    {\n                      staticStyle: { \"margin-left\": \"10px\" },\n                      attrs: {\n                        type: \"danger\",\n                        loading: _vm.rejecting,\n                        disabled: !_vm.rejectReason.trim()\n                      },\n                      on: { click: _vm.confirmReject }\n                    },\n                    [_vm._v(\"\\n          确认拒绝\\n        \")]\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          attrs: { title: \"提现申请详情\", footer: null, width: \"600px\" },\n          model: {\n            value: _vm.showDetailModal,\n            callback: function($$v) {\n              _vm.showDetailModal = $$v\n            },\n            expression: \"showDetailModal\"\n          }\n        },\n        [\n          _vm.currentRecord\n            ? _c(\n                \"div\",\n                { staticClass: \"detail-modal\" },\n                [\n                  _c(\n                    \"a-descriptions\",\n                    { attrs: { column: 2, bordered: \"\" } },\n                    [\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"申请ID\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.currentRecord.id) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"用户名\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.currentRecord.username) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"提现金额\" } },\n                        [\n                          _c(\"span\", { staticClass: \"amount-text\" }, [\n                            _vm._v(\n                              \"¥\" +\n                                _vm._s(\n                                  _vm.formatNumber(\n                                    _vm.currentRecord.withdrawal_amount\n                                  )\n                                )\n                            )\n                          ])\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"申请状态\" } },\n                        [\n                          _c(\n                            \"a-tag\",\n                            {\n                              attrs: {\n                                color: _vm.getStatusColor(\n                                  _vm.currentRecord.status\n                                )\n                              }\n                            },\n                            [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(\n                                    _vm.getStatusText(\n                                      _vm.currentRecord.status,\n                                      _vm.currentRecord.review_remark\n                                    )\n                                  ) +\n                                  \"\\n          \"\n                              )\n                            ]\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"真实姓名\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.currentRecord.alipay_name) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"支付宝账号\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.currentRecord.alipay_account) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"申请时间\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(\n                                _vm.currentRecord.apply_time\n                                  ? _vm.formatDateTime(\n                                      _vm.currentRecord.apply_time\n                                    )\n                                  : \"-\"\n                              ) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _c(\n                        \"a-descriptions-item\",\n                        { attrs: { label: \"审核时间\" } },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(\n                                _vm.currentRecord.review_time\n                                  ? _vm.formatDateTime(\n                                      _vm.currentRecord.review_time\n                                    )\n                                  : \"-\"\n                              ) +\n                              \"\\n        \"\n                          )\n                        ]\n                      ),\n                      _vm.currentRecord.review_by\n                        ? _c(\n                            \"a-descriptions-item\",\n                            { attrs: { label: \"审核人\" } },\n                            [\n                              _vm._v(\n                                \"\\n          \" +\n                                  _vm._s(_vm.currentRecord.review_by) +\n                                  \"\\n        \"\n                              )\n                            ]\n                          )\n                        : _vm._e(),\n                      _vm.currentRecord.review_remark\n                        ? _c(\n                            \"a-descriptions-item\",\n                            { attrs: { label: \"审核备注\" } },\n                            [\n                              _vm._v(\n                                \"\\n          \" +\n                                  _vm._s(_vm.currentRecord.review_remark) +\n                                  \"\\n        \"\n                              )\n                            ]\n                          )\n                        : _vm._e()\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            : _vm._e()\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"page-header\" }, [\n      _c(\"h2\", [_vm._v(\"提现管理\")]),\n      _c(\"p\", [_vm._v(\"管理用户提现申请，审核通过或拒绝申请\")])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}