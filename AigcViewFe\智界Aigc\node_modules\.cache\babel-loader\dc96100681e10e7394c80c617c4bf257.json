{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Failure.vue", "mtime": 1753756420429}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'PaymentFailure',\n  data: function data() {\n    return {\n      orderInfo: null\n    };\n  },\n  mounted: function mounted() {\n    this.loadOrderInfo();\n  },\n  methods: {\n    loadOrderInfo: function loadOrderInfo() {\n      // 从URL参数获取订单信息\n      var orderId = this.$route.query.orderId;\n\n      if (orderId) {\n        this.orderInfo = {\n          orderId: orderId,\n          amount: this.$route.query.amount || null\n        };\n      }\n    },\n    formatTime: function formatTime(date) {\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    },\n    retryPayment: function retryPayment() {\n      // 重新发起支付\n      this.$router.push('/usercenter/credits');\n    },\n    goToUserCenter: function goToUserCenter() {\n      this.$router.push('/usercenter/orders');\n    },\n    goHome: function goHome() {\n      this.$router.push('/home');\n    }\n  }\n};", {"version": 3, "sources": ["Failure.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,SAAA,EAAA;AADA,KAAA;AAGA,GANA;AAQA,EAAA,OARA,qBAQA;AACA,SAAA,aAAA;AACA,GAVA;AAYA,EAAA,OAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,OAAA;;AACA,UAAA,OAAA,EAAA;AACA,aAAA,SAAA,GAAA;AACA,UAAA,OAAA,EAAA,OADA;AAEA,UAAA,MAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,MAAA,IAAA;AAFA,SAAA;AAIA;AACA,KAVA;AAYA,IAAA,UAZA,sBAYA,IAZA,EAYA;AACA,aAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,GAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,MAAA,EAAA;AANA,OAAA,CAAA;AAQA,KArBA;AAuBA,IAAA,YAvBA,0BAuBA;AACA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,qBAAA;AACA,KA1BA;AA4BA,IAAA,cA5BA,4BA4BA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA,KA9BA;AAgCA,IAAA,MAhCA,oBAgCA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,OAAA;AACA;AAlCA;AAZA,CAAA", "sourcesContent": ["<template>\n  <div class=\"payment-failure-page\">\n    <div class=\"failure-container\">\n      <!-- 失败图标 -->\n      <div class=\"failure-icon\">\n        <a-icon type=\"close-circle\" theme=\"filled\" />\n      </div>\n      \n      <!-- 失败标题 -->\n      <h1 class=\"failure-title\">支付失败</h1>\n      \n      <!-- 失败原因 -->\n      <div class=\"failure-message\">\n        <p>很抱歉，您的支付未能成功完成。</p>\n        <p>可能的原因：</p>\n        <ul class=\"reason-list\">\n          <li>支付过程中网络连接中断</li>\n          <li>支付信息验证失败</li>\n          <li>支付宝账户余额不足</li>\n          <li>银行卡限额或状态异常</li>\n        </ul>\n      </div>\n      \n      <!-- 订单信息 -->\n      <div class=\"order-info\" v-if=\"orderInfo\">\n        <div class=\"info-item\">\n          <span class=\"label\">订单号：</span>\n          <span class=\"value\">{{ orderInfo.orderId }}</span>\n        </div>\n        <div class=\"info-item\" v-if=\"orderInfo.amount\">\n          <span class=\"label\">订单金额：</span>\n          <span class=\"value amount\">¥{{ orderInfo.amount }}</span>\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">失败时间：</span>\n          <span class=\"value\">{{ formatTime(new Date()) }}</span>\n        </div>\n      </div>\n      \n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <a-button type=\"primary\" size=\"large\" @click=\"retryPayment\">\n          重新支付\n        </a-button>\n        <a-button size=\"large\" @click=\"goToUserCenter\" style=\"margin-left: 16px\">\n          查看订单\n        </a-button>\n        <a-button size=\"large\" @click=\"goHome\" style=\"margin-left: 16px\">\n          返回首页\n        </a-button>\n      </div>\n      \n      <!-- 帮助信息 -->\n      <div class=\"help-info\">\n        <a-alert\n          message=\"需要帮助？\"\n          description=\"如果问题持续存在，请联系客服：400-123-4567 或发送邮件至 <EMAIL>\"\n          type=\"warning\"\n          show-icon\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PaymentFailure',\n  data() {\n    return {\n      orderInfo: null\n    }\n  },\n  \n  mounted() {\n    this.loadOrderInfo()\n  },\n  \n  methods: {\n    loadOrderInfo() {\n      // 从URL参数获取订单信息\n      const orderId = this.$route.query.orderId\n      if (orderId) {\n        this.orderInfo = {\n          orderId: orderId,\n          amount: this.$route.query.amount || null\n        }\n      }\n    },\n    \n    formatTime(date) {\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    \n    retryPayment() {\n      // 重新发起支付\n      this.$router.push('/usercenter/credits')\n    },\n    \n    goToUserCenter() {\n      this.$router.push('/usercenter/orders')\n    },\n    \n    goHome() {\n      this.$router.push('/home')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.payment-failure-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.failure-container {\n  background: white;\n  border-radius: 16px;\n  padding: 48px 40px;\n  text-align: center;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n}\n\n.failure-icon {\n  font-size: 80px;\n  color: #ff4d4f;\n  margin-bottom: 24px;\n}\n\n.failure-title {\n  font-size: 32px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 32px;\n}\n\n.failure-message {\n  margin-bottom: 32px;\n  color: #595959;\n  line-height: 1.6;\n  text-align: left;\n}\n\n.reason-list {\n  margin: 16px 0;\n  padding-left: 20px;\n}\n\n.reason-list li {\n  margin-bottom: 8px;\n  color: #8c8c8c;\n}\n\n.order-info {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 32px;\n  text-align: left;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  color: #8c8c8c;\n  font-size: 14px;\n}\n\n.value {\n  color: #262626;\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.value.amount {\n  color: #f5222d;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.action-buttons {\n  margin-bottom: 32px;\n}\n\n.help-info {\n  text-align: left;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .failure-container {\n    padding: 32px 24px;\n  }\n  \n  .failure-title {\n    font-size: 24px;\n  }\n  \n  .failure-icon {\n    font-size: 60px;\n  }\n  \n  .action-buttons .ant-btn {\n    margin: 8px 4px !important;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/payment"}]}