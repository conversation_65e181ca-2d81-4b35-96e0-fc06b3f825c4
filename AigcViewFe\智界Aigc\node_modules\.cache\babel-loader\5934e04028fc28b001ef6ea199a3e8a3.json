{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\SmartNotFound.vue", "mtime": 1753692759803}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsiteNotFound from '@/views/website/exception/NotFound.vue';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { isAdmin, getUserRole } from '@/utils/roleUtils';\nimport { generateIndexRouter } from '@/utils/util';\nimport Vue from 'vue';\nexport default {\n  name: 'SmartNotFound',\n  components: {\n    WebsiteNotFound: WebsiteNotFound\n  },\n  data: function data() {\n    return {\n      loading: true\n    };\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              console.log('🔍 SmartNotFound: 开始智能路由检查');\n              console.log('  📍 当前访问路径:', this.$route.path);\n              console.log('  📍 完整路径:', this.$route.fullPath);\n              _context.next = 5;\n              return this.checkAndHandleRoute();\n\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    checkAndHandleRoute: function () {\n      var _checkAndHandleRoute = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var hasToken, userRole, isAdminUser, currentPath, isLikelyBackendPage;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                // 1. 检查是否有TOKEN\n                hasToken = !!Vue.ls.get(ACCESS_TOKEN);\n                console.log('  🔑 是否有TOKEN:', hasToken);\n\n                if (hasToken) {\n                  _context2.next = 7;\n                  break;\n                }\n\n                console.log('  ❌ 无TOKEN，显示404页面');\n                this.loading = false;\n                return _context2.abrupt(\"return\");\n\n              case 7:\n                _context2.next = 9;\n                return getUserRole();\n\n              case 9:\n                userRole = _context2.sent;\n                _context2.next = 12;\n                return isAdmin();\n\n              case 12:\n                isAdminUser = _context2.sent;\n                console.log('  👤 用户角色:', userRole);\n                console.log('  🔐 是否管理员:', isAdminUser);\n\n                if (isAdminUser) {\n                  _context2.next = 19;\n                  break;\n                }\n\n                console.log('  ❌ 非管理员用户，显示404页面');\n                this.loading = false;\n                return _context2.abrupt(\"return\");\n\n              case 19:\n                // 3. 检查访问路径是否可能是后台页面\n                currentPath = this.$route.path;\n                isLikelyBackendPage = this.isLikelyBackendPage(currentPath);\n                console.log('  🏢 是否疑似后台页面:', isLikelyBackendPage);\n\n                if (isLikelyBackendPage) {\n                  _context2.next = 26;\n                  break;\n                }\n\n                console.log('  ❌ 不是后台页面，显示404页面');\n                this.loading = false;\n                return _context2.abrupt(\"return\");\n\n              case 26:\n                // 4. admin用户访问疑似后台页面，尝试加载动态路由\n                console.log('  🚀 admin访问后台页面，尝试加载动态路由');\n                _context2.next = 29;\n                return this.loadDynamicRoutesAndRedirect(currentPath);\n\n              case 29:\n                _context2.next = 35;\n                break;\n\n              case 31:\n                _context2.prev = 31;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('  ❌ 智能路由检查失败:', _context2.t0);\n                this.loading = false;\n\n              case 35:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 31]]);\n      }));\n\n      function checkAndHandleRoute() {\n        return _checkAndHandleRoute.apply(this, arguments);\n      }\n\n      return checkAndHandleRoute;\n    }(),\n    isLikelyBackendPage: function isLikelyBackendPage(path) {\n      // 判断是否可能是后台页面的逻辑\n      var backendPatterns = [// 现有的模块\n      '/views/', // 所有views下的页面\n      '/dashboard/', // 仪表板页面\n      '/system/', // 系统管理页面\n      '/aigcview/', // 智界Aigc模块页面\n      '/cjsc', // 插件商城管理\n      '/spjc', // 视频教程管理\n      '/sensitive-word', // 敏感词管理页面\n      '/usercenter/withdrawal', // 提现管理页面\n      // JeecgBoot核心模块\n      '/online/', // 在线开发模块\n      '/jmreport/', // 积木报表模块\n      '/bigscreen/', // 大屏报表模块\n      '/desform/', // 表单设计器\n      '/act/', // 工作流模块\n      '/plug-in/', // 插件管理\n      '/generic/', // 通用功能\n      '/eoa/', // OA办公模块\n      '/joa/', // OA流程模块\n      // 简化路径和其他模块\n      '/isystem', // 系统管理简化路径\n      '/modules/', // 功能模块路径\n      '/examples/', // 示例页面路径\n      '/jeecg/' // JeecgBoot示例路径\n      ];\n      return backendPatterns.some(function (pattern) {\n        return path.startsWith(pattern);\n      });\n    },\n    loadDynamicRoutesAndRedirect: function () {\n      var _loadDynamicRoutesAndRedirect = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3(targetPath) {\n        var res, menuData, constRoutes;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                console.log('  📡 开始获取权限数据'); // 检查动态路由是否已加载\n\n                if (!(this.$store.getters.permissionList.length > 0)) {\n                  _context3.next = 6;\n                  break;\n                }\n\n                console.log('  ✅ 动态路由已存在，直接跳转');\n                this.$router.replace(targetPath);\n                return _context3.abrupt(\"return\");\n\n              case 6:\n                _context3.next = 8;\n                return this.$store.dispatch('GetPermissionList');\n\n              case 8:\n                res = _context3.sent;\n                console.log('  📡 权限接口响应:', res);\n                menuData = res.result.menu;\n\n                if (!(!menuData || menuData.length === 0)) {\n                  _context3.next = 15;\n                  break;\n                }\n\n                console.log('  ❌ 无菜单权限，显示404页面');\n                this.loading = false;\n                return _context3.abrupt(\"return\");\n\n              case 15:\n                // 生成动态路由\n                console.log('  🏗️ 生成动态路由');\n                constRoutes = generateIndexRouter(menuData); // 更新路由状态\n\n                _context3.next = 19;\n                return this.$store.dispatch('UpdateAppRouter', {\n                  constRoutes: constRoutes\n                });\n\n              case 19:\n                // 添加动态路由\n                this.$router.addRoutes(this.$store.getters.addRouters);\n                console.log('  ✅ 动态路由加载成功，跳转到目标页面:', targetPath); // 跳转到目标页面\n\n                this.$router.replace(targetPath);\n                _context3.next = 28;\n                break;\n\n              case 24:\n                _context3.prev = 24;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('  ❌ 动态路由加载失败:', _context3.t0);\n                this.loading = false;\n\n              case 28:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 24]]);\n      }));\n\n      function loadDynamicRoutesAndRedirect(_x) {\n        return _loadDynamicRoutesAndRedirect.apply(this, arguments);\n      }\n\n      return loadDynamicRoutesAndRedirect;\n    }()\n  }\n};", null]}