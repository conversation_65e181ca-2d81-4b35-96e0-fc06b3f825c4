{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\permission.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\permission.js", "mtime": 1753511198471}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nimport Vue from 'vue';\nimport router from './router';\nimport store from './store';\nimport NProgress from 'nprogress'; // progress bar\n\nimport 'nprogress/nprogress.css'; // progress bar style\n\nimport notification from 'ant-design-vue/es/notification';\nimport { ACCESS_TOKEN, INDEX_MAIN_PAGE_PATH, OAUTH2_LOGIN_PAGE_PATH } from '@/store/mutation-types';\nimport { generateIndexRouter, isOAuth2AppEnv } from '@/utils/util';\nimport { isAdmin, getUserRole, clearRoleInfo } from '@/utils/roleUtils';\nimport { handleServerConnectionError } from '@/utils/simpleErrorHandler';\nNProgress.configure({\n  showSpinner: false\n}); // NProgress Configuration\n// ✅ 白名单：无需登录即可访问的页面\n\nvar whiteList = ['/user/login', '/user/register', '/user/register-result', '/user/alteration']; // ✅ 官网页面：使用简洁路径，所有用户都可以访问（不包括个人中心和分销推广）\n\nvar websitePages = ['/home', '/market', '/cases', '/tutorials', '/signin', '/membership', '/login', '/not-found', '/carousel-test', '/route-test', '/server-error', '/error-test', '/JianYingDraft']; // ✅ 需要登录的官网页面\n\nvar authRequiredPages = ['/usercenter', '/affiliate']; // 将官网页面和OAuth2页面加入白名单\n\nwhiteList.push.apply(whiteList, websitePages);\nwhiteList.push(OAUTH2_LOGIN_PAGE_PATH);\n/**\n * 判断是否为官网页面（支持动态路由）\n * @param {string} path 路径\n * @returns {boolean} 是否为官网页面\n */\n\nfunction isWebsitePage(path) {\n  // 官网页面的路径模式\n  var websitePatterns = ['/market/plugin/', // 插件详情页\n  '/cases/', // 客户案例详情页\n  '/tutorials/' // 教程详情页\n  ];\n  return websitePatterns.some(function (pattern) {\n    return path.startsWith(pattern);\n  });\n}\n/**\n * 加载动态路由（如果尚未加载）\n */\n\n\nfunction loadDynamicRoutesIfNeeded() {\n  return _loadDynamicRoutesIfNeeded.apply(this, arguments);\n}\n\nfunction _loadDynamicRoutesIfNeeded() {\n  _loadDynamicRoutesIfNeeded = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n    var res, menuData, constRoutes;\n    return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n      while (1) {\n        switch (_context2.prev = _context2.next) {\n          case 0:\n            console.log('  🔧 开始检查动态路由加载状态');\n            console.log('  📋 当前权限列表长度:', store.getters.permissionList.length);\n\n            if (!(store.getters.permissionList.length === 0)) {\n              _context2.next = 25;\n              break;\n            }\n\n            console.log('  📡 权限列表为空，开始获取权限数据');\n            _context2.next = 6;\n            return store.dispatch('GetPermissionList');\n\n          case 6:\n            res = _context2.sent;\n            console.log('  📡 权限接口响应:', res);\n            menuData = res.result.menu;\n            console.log('  📋 菜单数据:', menuData);\n\n            if (!(menuData === null || menuData === \"\" || menuData === undefined || menuData.length === 0)) {\n              _context2.next = 13;\n              break;\n            }\n\n            console.error('  ❌ 菜单数据为空，无权限访问后台');\n            throw new Error('无菜单权限');\n\n          case 13:\n            console.log('  🏗️ 开始生成动态路由');\n            constRoutes = generateIndexRouter(menuData);\n            console.log('  🏗️ 生成的动态路由:', constRoutes);\n            console.log('  📦 更新应用路由状态');\n            _context2.next = 19;\n            return store.dispatch('UpdateAppRouter', {\n              constRoutes: constRoutes\n            });\n\n          case 19:\n            console.log('  ➕ 添加动态路由到路由器');\n            console.log('  ➕ 要添加的路由:', store.getters.addRouters);\n            router.addRoutes(store.getters.addRouters);\n            console.log('  ✅ 动态路由加载完成');\n            _context2.next = 26;\n            break;\n\n          case 25:\n            console.log('  ✅ 动态路由已存在，跳过加载');\n\n          case 26:\n          case \"end\":\n            return _context2.stop();\n        }\n      }\n    }, _callee2);\n  }));\n  return _loadDynamicRoutesIfNeeded.apply(this, arguments);\n}\n\nrouter.beforeEach( /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(to, from, next) {\n    var userRole, isAdminUser, isServerError, _isServerError, path;\n\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) {\n        switch (_context.prev = _context.next) {\n          case 0:\n            NProgress.start(); // ✅ 添加调试信息\n\n            console.log('🔍 路由守卫调试信息:');\n            console.log('  📍 目标路径 (to):', to.path, to.fullPath);\n            console.log('  📍 来源路径 (from):', from.path, from.fullPath);\n            console.log('  🔑 是否有TOKEN:', !!Vue.ls.get(ACCESS_TOKEN));\n\n            if (!Vue.ls.get(ACCESS_TOKEN)) {\n              _context.next = 97;\n              break;\n            }\n\n            _context.next = 8;\n            return getUserRole();\n\n          case 8:\n            userRole = _context.sent;\n            _context.next = 11;\n            return isAdmin();\n\n          case 11:\n            isAdminUser = _context.sent;\n            console.log('  👤 用户角色:', userRole);\n            console.log('  🔐 是否管理员:', isAdminUser);\n\n            if (!(to.path === '/user/login' || to.path === OAUTH2_LOGIN_PAGE_PATH)) {\n              _context.next = 20;\n              break;\n            }\n\n            // 已登录用户访问登录页，根据角色重定向\n            console.log('  ➡️ 已登录用户访问登录页，准备重定向');\n\n            if (isAdminUser) {\n              console.log('  🎯 管理员重定向到:', INDEX_MAIN_PAGE_PATH);\n              next({\n                path: INDEX_MAIN_PAGE_PATH\n              });\n            } else {\n              console.log('  🎯 普通用户重定向到: /home');\n              next({\n                path: '/home'\n              });\n            }\n\n            NProgress.done();\n            _context.next = 95;\n            break;\n\n          case 20:\n            if (!(to.path === '/')) {\n              _context.next = 50;\n              break;\n            }\n\n            // 根路径访问，根据角色决定去向\n            console.log('  ➡️ 访问根路径，根据角色决定去向');\n\n            if (!isAdminUser) {\n              _context.next = 45;\n              break;\n            }\n\n            // 管理员用户，检查并加载动态路由后进入后台\n            console.log('  🔧 管理员用户，开始加载动态路由');\n            _context.prev = 24;\n            _context.next = 27;\n            return loadDynamicRoutesIfNeeded();\n\n          case 27:\n            console.log('  ✅ 动态路由加载成功，跳转到:', INDEX_MAIN_PAGE_PATH);\n            console.log('  📋 当前路由表:', router.getRoutes ? router.getRoutes().map(function (r) {\n              return r.path;\n            }) : '无法获取');\n            next({\n              path: INDEX_MAIN_PAGE_PATH\n            });\n            _context.next = 43;\n            break;\n\n          case 32:\n            _context.prev = 32;\n            _context.t0 = _context[\"catch\"](24);\n            console.error('  ❌ 加载动态路由失败:', _context.t0);\n            console.log('  🔍 错误详情:', {\n              message: _context.t0.message,\n              code: _context.t0.code,\n              response: _context.t0.response\n            }); // 检查是否是服务器连接错误\n\n            isServerError = handleServerConnectionError(_context.t0);\n            console.log('  🔍 是否为服务器连接错误:', isServerError);\n\n            if (!isServerError) {\n              _context.next = 42;\n              break;\n            }\n\n            console.log('  🚨 检测到服务器连接错误，跳转到异常页面');\n            NProgress.done();\n            return _context.abrupt(\"return\");\n\n          case 42:\n            next({\n              path: '/404'\n            });\n\n          case 43:\n            _context.next = 47;\n            break;\n\n          case 45:\n            // 普通用户，重定向到官网首页\n            console.log('  🎯 普通用户重定向到: /home');\n            next({\n              path: '/home'\n            });\n\n          case 47:\n            NProgress.done();\n            _context.next = 95;\n            break;\n\n          case 50:\n            if (!(authRequiredPages.indexOf(to.path) !== -1)) {\n              _context.next = 56;\n              break;\n            }\n\n            // 需要登录的官网页面（如个人中心）\n            console.log('  🔐 访问需要登录的官网页面，已登录，允许访问');\n            next();\n            NProgress.done();\n            _context.next = 95;\n            break;\n\n          case 56:\n            if (!(websitePages.indexOf(to.path) !== -1 || isWebsitePage(to.path))) {\n              _context.next = 62;\n              break;\n            }\n\n            // 官网页面，所有用户都可以访问\n            console.log('  🌐 访问官网页面，直接通过');\n            next();\n            NProgress.done();\n            _context.next = 95;\n            break;\n\n          case 62:\n            // 后台页面，需要管理员角色\n            console.log('  🏢 访问后台页面，检查管理员权限');\n\n            if (!isAdminUser) {\n              _context.next = 92;\n              break;\n            }\n\n            // 管理员角色，检查菜单权限并加载动态路由\n            console.log('  🔧 管理员访问后台，加载动态路由');\n            _context.prev = 65;\n            _context.next = 68;\n            return loadDynamicRoutesIfNeeded();\n\n          case 68:\n            console.log('  ✅ 动态路由加载成功，允许访问:', to.path);\n            next();\n            _context.next = 90;\n            break;\n\n          case 72:\n            _context.prev = 72;\n            _context.t1 = _context[\"catch\"](65);\n            console.error('  ❌ 加载动态路由失败:', _context.t1);\n            console.log('  🔍 错误详情:', {\n              message: _context.t1.message,\n              code: _context.t1.code,\n              response: _context.t1.response\n            });\n            s; // 检查是否是认证错误（401）\n\n            if (!(_context.t1.response && _context.t1.response.status === 401)) {\n              _context.next = 83;\n              break;\n            }\n\n            console.log('  🔐 检测到认证失败，清除用户数据并重定向到登录页');\n            Vue.ls.remove(ACCESS_TOKEN);\n            store.dispatch('Logout').then(function () {\n              next({\n                path: '/login'\n              });\n            });\n            NProgress.done();\n            return _context.abrupt(\"return\");\n\n          case 83:\n            // 检查是否是服务器连接错误\n            _isServerError = handleServerConnectionError(_context.t1);\n            console.log('  🔍 是否为服务器连接错误:', _isServerError);\n\n            if (!_isServerError) {\n              _context.next = 89;\n              break;\n            }\n\n            console.log('  🚨 检测到服务器连接错误，跳转到异常页面');\n            NProgress.done();\n            return _context.abrupt(\"return\");\n\n          case 89:\n            next({\n              path: '/404'\n            });\n\n          case 90:\n            _context.next = 94;\n            break;\n\n          case 92:\n            // 非管理员角色，重定向到官网首页\n            console.log('  🚫 非管理员访问后台，重定向到: /home');\n            next({\n              path: '/home'\n            });\n\n          case 94:\n            NProgress.done();\n\n          case 95:\n            _context.next = 99;\n            break;\n\n          case 97:\n            /* 未登录用户 */\n            console.log('  🚫 未登录用户');\n\n            if (to.path === '/') {\n              // 未登录用户访问根路径，重定向到官网首页\n              console.log('  🎯 未登录访问根路径，重定向到: /home');\n              next({\n                path: '/home'\n              });\n              NProgress.done();\n            } else if (authRequiredPages.indexOf(to.path) !== -1) {\n              // 需要登录的官网页面（如个人中心），未登录用户重定向到官网登录页\n              console.log('  🔐 未登录访问个人中心，重定向到官网登录页');\n              next({\n                path: '/login',\n                query: {\n                  redirect: to.fullPath\n                }\n              });\n              NProgress.done();\n            } else if (whiteList.indexOf(to.path) !== -1 || isWebsitePage(to.path)) {\n              // 白名单页面或官网页面，直接访问\n              console.log('  ✅ 白名单/官网页面，直接访问');\n\n              if (to.path === '/user/login' && isOAuth2AppEnv()) {\n                next({\n                  path: OAUTH2_LOGIN_PAGE_PATH\n                });\n              } else {\n                next();\n              }\n\n              NProgress.done();\n            } else {\n              // 其他页面需要登录\n              console.log('  🔐 需要登录，重定向到登录页');\n              path = isOAuth2AppEnv() ? OAUTH2_LOGIN_PAGE_PATH : '/user/login';\n              next({\n                path: path,\n                query: {\n                  redirect: to.fullPath\n                }\n              });\n              NProgress.done();\n            }\n\n          case 99:\n          case \"end\":\n            return _context.stop();\n        }\n      }\n    }, _callee, null, [[24, 32], [65, 72]]);\n  }));\n\n  return function (_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}());\nrouter.afterEach(function () {\n  NProgress.done(); // finish progress bar\n});", null]}