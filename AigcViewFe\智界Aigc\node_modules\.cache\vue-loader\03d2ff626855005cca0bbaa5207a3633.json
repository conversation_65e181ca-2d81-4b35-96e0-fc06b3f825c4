{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue?vue&type=style&index=0&id=1e174e22&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\usercenter\\components\\Sidebar.vue", "mtime": 1753687927441}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.usercenter-sidebar {\n  width: 260px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20px;\n  padding: 1rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 138, 237, 0.1);\n  height: fit-content;\n  position: sticky;\n  /* top值通过JavaScript动态设置 */\n  align-self: flex-start;\n  transition: all 0.3s ease;\n  /* 取消浮动定位相关样式 */\n  /* position: fixed;\n  top: 140px;\n  left: 50%;\n  transform: translateX(-50%);\n  margin-left: -600px;\n  z-index: 100; */\n}\n\n/* 用户信息卡片 */\n.user-info-card {\n  text-align: center;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.user-avatar {\n  position: relative;\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 1rem;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n  border: 3px solid rgba(124, 138, 237, 0.2);\n}\n\n.avatar-status {\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: #10b981;\n  border: 2px solid white;\n  opacity: 0.7;\n}\n\n.avatar-status.online {\n  opacity: 1;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n.user-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #334155;\n  margin: 0 0 0.5rem 0;\n}\n\n.user-email {\n  font-size: 0.9rem;\n  color: #64748b;\n  margin: 0 0 1rem 0;\n}\n\n.level-badge {\n  display: inline-block;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.level-badge.level-user {\n  background: rgba(107, 114, 128, 0.1);\n  color: #6b7280;\n}\n\n.level-badge.level-vip {\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  color: white;\n}\n\n.level-badge.level-svip {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  color: white;\n}\n\n.level-badge.level-admin {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  color: white;\n}\n\n/* 用户余额显示 */\n.user-balance {\n  margin-top: 0.5rem;\n  padding: 0.8rem;\n  background: linear-gradient(135deg, rgba(124, 138, 237, 0.05) 0%, rgba(139, 95, 191, 0.05) 100%);\n  border-radius: 12px;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n  text-align: center;\n}\n\n.user-balance .balance-label {\n  font-size: 0.8rem;\n  color: #64748b;\n  margin-bottom: 0.5rem;\n}\n\n.user-balance .balance-amount {\n  font-size: 1.3rem;\n  font-weight: 700;\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: 0.75rem;\n}\n\n.balance-btn-mini {\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  border: none;\n  border-radius: 8px;\n  color: white;\n  font-size: 0.8rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.balance-btn-mini:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 15px rgba(124, 138, 237, 0.3);\n}\n\n/* 导航菜单 */\n.sidebar-nav {\n  margin-bottom: 1rem;\n}\n\n.nav-menu {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.nav-item {\n  margin-bottom: 0.2rem;\n}\n\n.nav-link {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  border-radius: 12px;\n  text-decoration: none;\n  color: #64748b;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-link:hover {\n  background: rgba(124, 138, 237, 0.08);\n  color: #7c8aed;\n  transform: translateX(4px);\n}\n\n.nav-link.active {\n  background: linear-gradient(135deg, rgba(124, 138, 237, 0.1) 0%, rgba(139, 95, 191, 0.1) 100%);\n  color: #7c8aed;\n  border-left: 3px solid #7c8aed;\n}\n\n.nav-icon {\n  font-size: 1.2rem;\n  margin-right: 0.75rem;\n  width: 20px;\n  text-align: center;\n}\n\n.nav-text {\n  flex: 1;\n  font-weight: 500;\n}\n\n.nav-badge {\n  background: #ef4444;\n  color: white;\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-weight: 600;\n  min-width: 18px;\n  text-align: center;\n  animation: pulse-badge 2s infinite;\n}\n\n@keyframes pulse-badge {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n\n/* 快捷操作 */\n.quick-actions {\n  display: flex;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n}\n\n.action-btn {\n  flex: 1;\n  padding: 0.75rem;\n  border: none;\n  border-radius: 10px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-btn.primary {\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  color: white;\n}\n\n.action-btn.primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(124, 138, 237, 0.3);\n}\n\n.action-btn.secondary {\n  background: rgba(124, 138, 237, 0.1);\n  color: #7c8aed;\n  border: 1px solid rgba(124, 138, 237, 0.2);\n}\n\n.action-btn.secondary:hover {\n  background: rgba(124, 138, 237, 0.15);\n  transform: translateY(-2px);\n}\n\n/* 余额卡片 */\n.balance-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-radius: 16px;\n  padding: 1.5rem;\n  border: 1px solid rgba(124, 138, 237, 0.1);\n}\n\n.balance-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.balance-label {\n  font-size: 0.9rem;\n  color: #64748b;\n  text-align: center;\n}\n\n.balance-amount {\n  font-size: 1.5rem;\n  text-align: center;\n  font-weight: 700;\n  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.balance-btn {\n  width: 100%;\n  padding: 0.75rem;\n  background: rgba(124, 138, 237, 0.1);\n  border: 1px solid rgba(124, 138, 237, 0.2);\n  border-radius: 8px;\n  color: #7c8aed;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.balance-btn:hover {\n  background: rgba(124, 138, 237, 0.15);\n  transform: translateY(-1px);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .usercenter-sidebar {\n    width: 100%;\n    margin-bottom: 2rem;\n    position: static; /* 小屏幕取消sticky */\n  }\n}\n\n@media (max-width: 768px) {\n  .usercenter-sidebar {\n    width: 100%;\n    margin-bottom: 2rem;\n    position: static; /* 小屏幕取消sticky */\n  }\n\n  .quick-actions {\n    flex-direction: column;\n  }\n\n  .action-btn {\n    justify-content: flex-start;\n  }\n}\n", null]}