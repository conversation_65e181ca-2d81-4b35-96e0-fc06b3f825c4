{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\api\\usercenter.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\api\\usercenter.js", "mtime": 1753688681269}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { axios } from '@/utils/request';\nvar api = {\n  // 用户扩展信息相关接口\n  userProfile: '/demo/userprofile',\n  userTransactions: '/demo/userprofile/transactions',\n  updateNickname: '/demo/userprofile/updateNickname',\n  regenerateApiKey: '/demo/userprofile/regenerateApiKey',\n  adminRecharge: '/demo/userprofile/adminRecharge',\n  // 兑换码相关接口\n  exchangeCode: '/demo/exchangecode',\n  useExchangeCode: '/demo/exchangecode/use',\n  validateExchangeCode: '/demo/exchangecode/validate',\n  userUsedCodes: '/demo/exchangecode/userUsedCodes',\n  generateExchangeCode: '/demo/exchangecode/generate',\n  updateExpiredCodes: '/demo/exchangecode/updateExpired',\n  // 交易记录相关接口\n  userRecord: '/demo/userrecord',\n  // 仪表板数据接口\n  dashboardData: '/api/aigc/dashboard-data',\n  // 桌面应用下载统计接口\n  desktopDownloadStats: '/sys/common/desktop-app-download-stats',\n  // 用户中心验证码接口\n  userCenterSmsCode: '/api/usercenter/sendSmsCode',\n  userCenterEmailCode: '/api/usercenter/sendEmailCode',\n  userCenterVerifyCode: '/api/usercenter/verifyCode',\n  // 昵称校验接口\n  validateNickname: '/api/usercenter/validateNickname'\n};\n/**\n * 获取当前用户的扩展信息\n */\n\nexport function getUserProfile() {\n  return axios({\n    url: api.userProfile + '/current',\n    method: 'get'\n  });\n}\n/**\n * 获取用户交易记录\n * @param {Object} params - 查询参数\n * @param {number} params.pageNo - 页码\n * @param {number} params.pageSize - 每页大小\n */\n\nexport function getUserTransactions(params) {\n  return axios({\n    url: api.userTransactions,\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 更新用户昵称\n * @param {Object} data - 请求数据\n * @param {string} data.nickname - 新昵称\n */\n\nexport function updateNickname(data) {\n  return axios({\n    url: api.updateNickname,\n    method: 'post',\n    params: data\n  });\n}\n/**\n * 重新生成API密钥\n */\n\nexport function regenerateApiKey() {\n  return axios({\n    url: api.regenerateApiKey,\n    method: 'post'\n  });\n}\n/**\n * 管理员充值接口\n * @param {Object} data - 充值数据\n * @param {string} data.userId - 用户ID\n * @param {number} data.amount - 充值金额\n * @param {string} data.description - 充值描述\n */\n\nexport function adminRecharge(data) {\n  return axios({\n    url: api.adminRecharge,\n    method: 'post',\n    params: data\n  });\n}\n/**\n * 使用兑换码\n * @param {Object} data - 兑换码数据\n * @param {string} data.code - 兑换码\n */\n\nexport function useExchangeCode(data) {\n  return axios({\n    url: api.useExchangeCode,\n    method: 'post',\n    params: data\n  });\n}\n/**\n * 验证兑换码\n * @param {Object} params - 查询参数\n * @param {string} params.code - 兑换码\n */\n\nexport function validateExchangeCode(params) {\n  return axios({\n    url: api.validateExchangeCode,\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取用户使用的兑换码记录\n */\n\nexport function getUserUsedCodes() {\n  return axios({\n    url: api.userUsedCodes,\n    method: 'get'\n  });\n}\n/**\n * 生成兑换码\n * @param {Object} data - 生成参数\n * @param {number} data.codeType - 兑换码类型：1-余额，2-会员，3-积分\n * @param {number} data.value - 兑换价值\n * @param {string} data.expireTime - 过期时间\n * @param {string} data.batchNo - 批次号\n * @param {number} data.count - 生成数量\n */\n\nexport function generateExchangeCode(data) {\n  return axios({\n    url: api.generateExchangeCode,\n    method: 'post',\n    params: data\n  });\n}\n/**\n * 更新过期兑换码状态\n */\n\nexport function updateExpiredCodes() {\n  return axios({\n    url: api.updateExpiredCodes,\n    method: 'post'\n  });\n}\n/**\n * 获取兑换码列表\n * @param {Object} params - 查询参数\n */\n\nexport function getExchangeCodeList(params) {\n  return axios({\n    url: api.exchangeCode + '/list',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 添加兑换码\n * @param {Object} data - 兑换码数据\n */\n\nexport function addExchangeCode(data) {\n  return axios({\n    url: api.exchangeCode + '/add',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 编辑兑换码\n * @param {Object} data - 兑换码数据\n */\n\nexport function editExchangeCode(data) {\n  return axios({\n    url: api.exchangeCode + '/edit',\n    method: 'put',\n    data: data\n  });\n}\n/**\n * 删除兑换码\n * @param {string} id - 兑换码ID\n */\n\nexport function deleteExchangeCode(id) {\n  return axios({\n    url: api.exchangeCode + '/delete',\n    method: 'delete',\n    params: {\n      id: id\n    }\n  });\n}\n/**\n * 批量删除兑换码\n * @param {string} ids - 兑换码ID列表，逗号分隔\n */\n\nexport function batchDeleteExchangeCode(ids) {\n  return axios({\n    url: api.exchangeCode + '/deleteBatch',\n    method: 'delete',\n    params: {\n      ids: ids\n    }\n  });\n}\n/**\n * 获取用户扩展信息列表\n * @param {Object} params - 查询参数\n */\n\nexport function getUserProfileList(params) {\n  return axios({\n    url: api.userProfile + '/list',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 添加用户扩展信息\n * @param {Object} data - 用户扩展信息数据\n */\n\nexport function addUserProfile(data) {\n  return axios({\n    url: api.userProfile + '/add',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 编辑用户扩展信息\n * @param {Object} data - 用户扩展信息数据\n */\n\nexport function editUserProfile(data) {\n  return axios({\n    url: api.userProfile + '/edit',\n    method: 'put',\n    data: data\n  });\n}\n/**\n * 删除用户扩展信息\n * @param {string} id - 用户扩展信息ID\n */\n\nexport function deleteUserProfile(id) {\n  return axios({\n    url: api.userProfile + '/delete',\n    method: 'delete',\n    params: {\n      id: id\n    }\n  });\n}\n/**\n * 获取交易记录列表\n * @param {Object} params - 查询参数\n */\n\nexport function getUserRecordList(params) {\n  return axios({\n    url: api.userRecord + '/list',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 添加交易记录\n * @param {Object} data - 交易记录数据\n */\n\nexport function addUserRecord(data) {\n  return axios({\n    url: api.userRecord + '/add',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 编辑交易记录\n * @param {Object} data - 交易记录数据\n */\n\nexport function editUserRecord(data) {\n  return axios({\n    url: api.userRecord + '/edit',\n    method: 'put',\n    data: data\n  });\n}\n/**\n * 删除交易记录\n * @param {string} id - 交易记录ID\n */\n\nexport function deleteUserRecord(id) {\n  return axios({\n    url: api.userRecord + '/delete',\n    method: 'delete',\n    params: {\n      id: id\n    }\n  });\n}\n/**\n * 用户充值接口（对接支付）\n * @param {Object} data - 充值数据\n * @param {number} data.amount - 充值金额\n * @param {string} data.paymentMethod - 支付方式\n */\n\nexport function userRecharge(data) {\n  return axios({\n    url: '/api/payment/recharge',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取用户余额\n */\n\nexport function getUserBalance() {\n  return axios({\n    url: api.userProfile + '/balance',\n    method: 'get'\n  });\n}\n/**\n * 检查用户余额是否足够\n * @param {Object} params - 查询参数\n * @param {number} params.amount - 需要的金额\n */\n\nexport function checkUserBalance(params) {\n  return axios({\n    url: api.userProfile + '/checkBalance',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取用户统计信息\n */\n\nexport function getUserStats() {\n  return axios({\n    url: api.userProfile + '/stats',\n    method: 'get'\n  });\n}\n/**\n * 获取仪表板数据\n * @param {Object} params - 查询参数\n * @param {string} params.timeRange - 时间范围：today、week、month\n */\n\nexport function getDashboardData(params) {\n  return axios({\n    url: api.dashboardData,\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取桌面应用下载统计数据（仅管理员）\n * @param {Object} params - 查询参数\n * @param {number} params.days - 统计天数，默认7天\n */\n\nexport function getDesktopDownloadStats(params) {\n  return axios({\n    url: api.desktopDownloadStats,\n    method: 'get',\n    params: params\n  });\n} // ==================== 官网个人中心专用接口 ====================\n\n/**\n * 获取个人中心概览数据\n */\n\nexport function getUserCenterOverview() {\n  return axios({\n    url: '/api/usercenter/overview',\n    method: 'get'\n  });\n}\n/**\n * 获取用户完整信息\n */\n\nexport function getUserFullInfo() {\n  return axios({\n    url: '/api/usercenter/userFullInfo',\n    method: 'get'\n  });\n}\n/**\n * 获取最近活动记录\n */\n\nexport function getRecentActivities(params) {\n  return axios({\n    url: '/api/usercenter/recentActivities',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 更新用户基本信息\n */\n\nexport function updateUserInfo(data) {\n  return axios({\n    url: '/api/usercenter/updateUserInfo',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 更新用户扩展信息\n */\n\nexport function updateUserProfile(data) {\n  return axios({\n    url: '/api/usercenter/updateUserProfile',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取消费趋势图表数据\n */\n\nexport function getConsumptionChart(params) {\n  return axios({\n    url: '/api/usercenter/consumptionChart',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 修改用户密码\n */\n\nexport function changeUserPassword(data) {\n  return axios({\n    url: '/api/usercenter/changePassword',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 上传头像文件 - 使用标准文件上传接口\n */\n\nexport function uploadAvatarFile(file) {\n  var formData = new FormData();\n  formData.append('file', file);\n  formData.append('biz', 'avatar'); // 业务类型\n\n  return axios({\n    url: '/sys/common/upload',\n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n}\n/**\n * 更新用户头像到数据库\n */\n\nexport function updateUserAvatarUrl(avatarUrl) {\n  return axios({\n    url: '/api/usercenter/updateAvatarUrl',\n    method: 'post',\n    data: {\n      avatar: avatarUrl\n    }\n  });\n}\n/**\n * 获取交易记录列表\n */\n\nexport function getTransactionList(params) {\n  return axios({\n    url: '/api/usercenter/transactionList',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取交易统计数据\n */\n\nexport function getTransactionStats() {\n  return axios({\n    url: '/api/usercenter/transactionStats',\n    method: 'get'\n  });\n}\n/**\n * 导出交易记录\n * @param {Object} params - 导出参数\n * @param {string} params.transactionType - 交易类型\n * @param {string} params.status - 交易状态\n * @param {string} params.startDate - 开始日期\n * @param {string} params.endDate - 结束日期\n * @param {string} params.keyword - 搜索关键词\n */\n\nexport function exportTransactions(params) {\n  return axios({\n    url: '/api/usercenter/exportTransactions',\n    method: 'get',\n    params: params,\n    responseType: 'blob' // 重要：设置响应类型为blob用于文件下载\n\n  });\n}\n/**\n * 获取充值选项配置\n */\n\nexport function getRechargeOptions() {\n  return axios({\n    url: '/api/usercenter/rechargeOptions',\n    method: 'get'\n  });\n}\n/**\n * 创建充值订单\n */\n\nexport function createRechargeOrder(data) {\n  return axios({\n    url: '/api/usercenter/createRechargeOrder',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取会员信息\n */\n\nexport function getMembershipInfo() {\n  return axios({\n    url: '/api/usercenter/membership',\n    method: 'get'\n  });\n}\n/**\n * 获取会员等级配置\n */\n\nexport function getMembershipLevels() {\n  return axios({\n    url: '/api/usercenter/membershipLevels',\n    method: 'get'\n  });\n}\n/**\n * 会员升级\n */\n\nexport function upgradeMembership(data) {\n  return axios({\n    url: '/api/usercenter/upgradeMembership',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取会员历史记录\n */\n\nexport function getMembershipHistory(params) {\n  return axios({\n    url: '/api/usercenter/membershipHistory',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取推荐统计信息\n */\n\nexport function getReferralStats() {\n  return axios({\n    url: '/api/usercenter/referralStats',\n    method: 'get'\n  });\n}\n/**\n * 生成推荐链接\n */\n\nexport function generateReferralLink(data) {\n  return axios({\n    url: '/api/usercenter/generateReferralLink',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取用户角色信息\n */\n\nexport function getUserRole() {\n  return axios({\n    url: '/api/usercenter/userRole',\n    method: 'get'\n  });\n}\n/**\n * 获取等级配置信息\n */\n\nexport function getLevelConfig() {\n  return axios({\n    url: '/api/usercenter/levelConfig',\n    method: 'get'\n  });\n}\n/**\n * 获取用户当前等级\n */\n\nexport function getCurrentLevel() {\n  return axios({\n    url: '/api/usercenter/currentLevel',\n    method: 'get'\n  });\n}\n/**\n * 获取推荐记录列表\n */\n\nexport function getReferralList(params) {\n  return axios({\n    url: '/api/usercenter/referralList',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 申请提现\n */\n\nexport function requestWithdrawal(data) {\n  return axios({\n    url: '/api/usercenter/requestWithdrawal',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取提现历史记录\n */\n\nexport function getWithdrawalHistory(params) {\n  return axios({\n    url: '/api/usercenter/withdrawalHistory',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取订单记录列表\n */\n\nexport function getUserOrderList(params) {\n  return axios({\n    url: '/api/usercenter/orders',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取订单详情\n */\n\nexport function getUserOrderDetail(orderId) {\n  return axios({\n    url: \"/api/usercenter/orders/\".concat(orderId),\n    method: 'get'\n  });\n}\n/**\n * 获取订单统计数据\n */\n\nexport function getOrderStats() {\n  return axios({\n    url: '/api/usercenter/orderStats',\n    method: 'get'\n  });\n}\n/**\n * 导出订单数据\n */\n\nexport function exportOrders(params) {\n  return axios({\n    url: '/api/usercenter/exportOrders',\n    method: 'get',\n    params: params,\n    responseType: 'blob' // 重要：设置响应类型为blob\n\n  });\n}\n/**\n * 创建会员订阅订单\n */\n\nexport function createMembershipOrder(data) {\n  return axios({\n    url: '/api/usercenter/createMembershipOrder',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 支付订单\n */\n\nexport function payOrder(data) {\n  return axios({\n    url: '/api/usercenter/payOrder',\n    method: 'post',\n    data: data\n  });\n}\n/**\n * 获取API调用统计\n */\n\nexport function getApiUsageStats(params) {\n  return axios({\n    url: '/api/usercenter/apiUsageStats',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取插件使用历史\n */\n\nexport function getPluginUsageHistory(params) {\n  return axios({\n    url: '/api/usercenter/pluginUsageHistory',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 获取用户使用记录列表\n */\n\nexport function getUserUsageList(params) {\n  return axios({\n    url: '/api/usercenter/usageList',\n    method: 'get',\n    params: params\n  });\n}\n/**\n * 导出使用记录数据\n */\n\nexport function exportUsageRecords(params) {\n  return axios({\n    url: '/api/usercenter/exportUsageRecords',\n    method: 'get',\n    params: params,\n    responseType: 'blob' // 重要：设置响应类型为blob\n\n  });\n}\n/**\n * 获取系统通知（调用后台接口）\n * 注意：不要在URL前加/jeecg-boot，因为axios已经配置了baseURL\n */\n\nexport function getSystemNotifications() {\n  var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return axios({\n    url: '/sys/message/sysMessageTemplate/list',\n    method: 'get',\n    params: _objectSpread({\n      templateType: '系统',\n      pageNo: 1,\n      pageSize: 20\n    }, params)\n  });\n} // ==================== 用户中心验证码接口 ====================\n\n/**\n * 发送短信验证码（用户中心专用，需要token）\n * @param {string} phone 手机号\n * @param {string} scene 使用场景，默认changePhone\n */\n\nexport function sendUserCenterSmsCode(phone) {\n  var scene = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'changePhone';\n  return axios({\n    url: api.userCenterSmsCode,\n    method: 'post',\n    params: {\n      phone: phone,\n      scene: scene\n    }\n  });\n}\n/**\n * 发送邮箱验证码（用户中心专用，需要token）\n * @param {string} email 邮箱\n * @param {string} scene 使用场景，默认changeEmail\n */\n\nexport function sendUserCenterEmailCode(email) {\n  var scene = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'changeEmail';\n  return axios({\n    url: api.userCenterEmailCode,\n    method: 'post',\n    params: {\n      email: email,\n      scene: scene\n    }\n  });\n}\n/**\n * 验证验证码（用户中心专用，需要token）\n * @param {string} target 目标（手机号/邮箱）\n * @param {string} code 验证码\n * @param {string} codeType 验证码类型：sms-短信，email-邮箱\n * @param {string} scene 使用场景\n */\n\nexport function verifyUserCenterCode(target, code, codeType, scene) {\n  return axios({\n    url: api.userCenterVerifyCode,\n    method: 'post',\n    params: {\n      target: target,\n      code: code,\n      codeType: codeType,\n      scene: scene\n    }\n  });\n}\n/**\n * 验证昵称是否可用（需要token）\n * @param {string} nickname 昵称\n */\n\nexport function validateNickname(nickname) {\n  return axios({\n    url: api.validateNickname,\n    method: 'post',\n    params: {\n      nickname: nickname\n    }\n  });\n}", null]}