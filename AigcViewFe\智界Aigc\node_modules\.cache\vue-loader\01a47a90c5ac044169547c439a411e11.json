{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=template&id=e0dcc94e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"affiliate-container\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">邀请奖励</h1>\n      <p class=\"simple-subtitle\">邀请好友注册智界AIGC，获得丰厚奖励</p>\n      <div class=\"commission-badge\">\n        <span class=\"badge-text\">当前奖励比例：{{ currentCommissionRate }}%</span>\n        <span class=\"badge-level\">{{ commissionLevelText }}</span>\n      </div>\n    </div>\n\n    <!-- 分销内容区域 -->\n    <section class=\"affiliate-section\">\n      <div class=\"container\">\n        <!-- 邀请链接区域 - 最显眼位置 -->\n        <div class=\"promotion-link-section\">\n          <h2 class=\"section-title\">您的专属邀请链接</h2>\n          <div class=\"link-main-container\">\n            <div class=\"link-input-large\">\n              <a-input\n                :value=\"affiliateLink || '正在生成邀请链接...'\"\n                readonly\n                :loading=\"loading\"\n                size=\"large\"\n                placeholder=\"邀请链接生成中...\"\n              />\n            </div>\n            <div class=\"link-actions\">\n              <a-button\n                type=\"primary\"\n                size=\"large\"\n                :disabled=\"!affiliateLink || loading\"\n                @click=\"copyLink\"\n                class=\"copy-btn\"\n              >\n                <a-icon type=\"copy\" />\n                复制链接\n              </a-button>\n              <a-button\n                size=\"large\"\n                :loading=\"qrLoading\"\n                @click=\"generateQRCode\"\n                class=\"qr-btn\"\n              >\n                <a-icon type=\"qrcode\" />\n                邀请二维码\n              </a-button>\n            </div>\n          </div>\n          <div class=\"link-tips\">\n            <a-icon type=\"info-circle\" />\n            分享此链接，您将获得好友付费的 <strong>{{ currentCommissionRate }}%</strong> 奖励\n          </div>\n        </div>\n\n        <!-- 收益展示 -->\n        <div class=\"earnings-dashboard\">\n          <h2 class=\"section-title\">收益概览</h2>\n          <div class=\"earnings-grid\">\n            <div class=\"earning-card primary\">\n              <div class=\"card-icon\">\n                <a-icon type=\"dollar\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                  <div class=\"earning-label\">累计收益</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card success\">\n              <div class=\"card-icon\">\n                <a-icon type=\"wallet\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                  <div class=\"earning-label\">可提现金额</div>\n                </a-spin>\n                <div class=\"card-action\">\n                  <a-button\n                    type=\"primary\"\n                    size=\"small\"\n                    :disabled=\"availableEarnings <= 0 || loading\"\n                    @click=\"openWithdrawModal\"\n                  >\n                    立即提现\n                  </a-button>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"earning-card info\">\n              <div class=\"card-icon\">\n                <a-icon type=\"team\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ Math.floor(totalReferrals) }}</div>\n                  <div class=\"earning-label\">邀请注册人数</div>\n                </a-spin>\n              </div>\n            </div>\n\n            <div class=\"earning-card warning\">\n              <div class=\"card-icon\">\n                <a-icon type=\"crown\" />\n              </div>\n              <div class=\"card-content\">\n                <a-spin :spinning=\"loading\" size=\"small\">\n                  <div class=\"earning-number\">{{ Math.floor(memberReferrals) }}</div>\n                  <div class=\"earning-label\">转化人数</div>\n                </a-spin>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 奖励等级进度 -->\n        <div class=\"commission-progress\">\n          <h2 class=\"section-title\">奖励等级进度</h2>\n          <div class=\"progress-card\">\n            <!-- 一行显示所有等级 -->\n            <div class=\"level-timeline-horizontal\">\n              <div\n                v-for=\"(level, index) in commissionLevels\"\n                :key=\"index\"\n                class=\"level-step-horizontal\"\n                :class=\"{\n                  'current': level.isCurrent,\n                  'completed': level.isCompleted,\n                  'upcoming': level.isUpcoming\n                }\"\n              >\n                <div class=\"step-circle-horizontal\">\n                  <a-icon v-if=\"level.isCompleted\" type=\"check\" />\n                  <span v-else-if=\"level.isCurrent\" class=\"current-dot\"></span>\n                  <span v-else class=\"step-number\">{{ index + 1 }}</span>\n                </div>\n                <div class=\"step-content-horizontal\">\n                  <div class=\"step-title\">{{ level.name }}</div>\n                  <div class=\"step-rate\">{{ level.rate }}%</div>\n                  <div class=\"step-requirement\">{{ level.requirement }}人</div>\n                  <div v-if=\"level.remaining > 0\" class=\"step-remaining\">\n                    还需{{ level.remaining }}个\n                  </div>\n                  <div v-else-if=\"level.isCompleted\" class=\"step-completed\">\n                    已达成\n                  </div>\n                </div>\n                <!-- 连接线 -->\n                <div v-if=\"index < commissionLevels.length - 1\" class=\"step-line-horizontal\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 奖励规则说明 -->\n        <div class=\"commission-rules\">\n          <h2 class=\"section-title\">奖励规则</h2>\n          <div class=\"rules-table\">\n            <div class=\"rule-row header\">\n              <div class=\"rule-cell\">用户等级</div>\n              <div class=\"rule-cell\">邀请人数要求</div>\n              <div class=\"rule-cell\">奖励比例</div>\n              <div class=\"rule-cell\">说明</div>\n            </div>\n            <a-spin :spinning=\"loading\" size=\"small\">\n              <div\n                v-for=\"config in allLevelConfigs\"\n                :key=\"config.id\"\n                class=\"rule-row\"\n                :class=\"{\n                  'vip': config.role_code === 'VIP',\n                  'svip': config.role_code === 'SVIP'\n                }\"\n              >\n                <div class=\"rule-cell\">{{ getRoleDisplayName(config.role_code) }}</div>\n                <div class=\"rule-cell\">{{ getRequirementText(config) }}</div>\n                <div class=\"rule-cell highlight\">{{ config.commission_rate }}%</div>\n                <div class=\"rule-cell\">{{ config.level_name }}</div>\n              </div>\n            </a-spin>\n          </div>\n        </div>\n\n        <!-- 邀请用户列表 -->\n        <div class=\"referral-users\">\n          <h2 class=\"section-title\">我的邀请用户</h2>\n          <div class=\"users-table-container\">\n            <a-table\n              :columns=\"userColumns\"\n              :data-source=\"referralUsers\"\n              :loading=\"usersLoading\"\n              :pagination=\"usersPagination\"\n              size=\"middle\"\n              @change=\"handleUsersTableChange\"\n            >\n              <template slot=\"avatar\" slot-scope=\"text, record\">\n                <a-avatar :src=\"getAvatarUrl(record.avatar)\" :style=\"{ backgroundColor: '#87d068' }\">\n                  {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                </a-avatar>\n              </template>\n              <template slot=\"reward\" slot-scope=\"text\">\n                <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n              </template>\n            </a-table>\n          </div>\n        </div>\n\n        <!-- 提现记录 -->\n        <div class=\"withdraw-records\">\n          <h2 class=\"section-title\">提现记录</h2>\n\n          <!-- 筛选区域 -->\n          <div class=\"filter-section\" style=\"margin-bottom: 16px; padding: 16px; background: #fafafa; border-radius: 6px;\">\n            <a-row :gutter=\"16\">\n              <a-col :span=\"5\">\n                <a-form-item label=\"提现金额\">\n                  <a-input-group compact>\n                    <a-input-number\n                      v-model=\"withdrawFilter.minAmount\"\n                      placeholder=\"最小金额\"\n                      :min=\"0\"\n                      :precision=\"2\"\n                      style=\"width: 50%\"\n                    />\n                    <a-input-number\n                      v-model=\"withdrawFilter.maxAmount\"\n                      placeholder=\"最大金额\"\n                      :min=\"0\"\n                      :precision=\"2\"\n                      style=\"width: 50%\"\n                    />\n                  </a-input-group>\n                </a-form-item>\n              </a-col>\n\n              <a-col :span=\"5\">\n                <a-form-item label=\"申请时间\">\n                  <a-range-picker\n                    v-model=\"withdrawFilter.dateRange\"\n                    format=\"YYYY-MM-DD\"\n                    placeholder=\"['开始日期', '结束日期']\"\n                    style=\"width: 100%\"\n                  />\n                </a-form-item>\n              </a-col>\n\n              <a-col :span=\"4\">\n                <a-form-item label=\"状态\">\n                  <a-select\n                    v-model=\"withdrawFilter.status\"\n                    placeholder=\"选择状态\"\n                    style=\"width: 100%\"\n                  >\n                    <a-select-option :value=\"null\">全部</a-select-option>\n                    <a-select-option :value=\"1\">待审核</a-select-option>\n                    <a-select-option :value=\"2\">已发放</a-select-option>\n                    <a-select-option :value=\"3\">审核拒绝</a-select-option>\n                    <a-select-option :value=\"4\">已取消</a-select-option>\n                  </a-select>\n                </a-form-item>\n              </a-col>\n\n              <a-col :span=\"5\">\n                <a-form-item label=\"完成时间\">\n                  <a-range-picker\n                    v-model=\"withdrawFilter.completeDateRange\"\n                    format=\"YYYY-MM-DD\"\n                    placeholder=\"['开始日期', '结束日期']\"\n                    style=\"width: 100%\"\n                  />\n                </a-form-item>\n              </a-col>\n\n              <a-col :span=\"5\">\n                <a-form-item label=\" \">\n                  <a-button type=\"primary\" @click=\"handleWithdrawFilter\" :loading=\"recordsLoading\" style=\"margin-right: 8px;\">\n                    搜索\n                  </a-button>\n                  <a-button @click=\"handleWithdrawReset\">重置</a-button>\n                </a-form-item>\n              </a-col>\n            </a-row>\n          </div>\n\n          <div class=\"records-table-container\">\n            <a-table\n              :columns=\"withdrawColumns\"\n              :data-source=\"withdrawRecords\"\n              :loading=\"recordsLoading\"\n              :pagination=\"withdrawPagination\"\n              size=\"middle\"\n              @change=\"handleWithdrawTableChange\"\n            >\n              <template slot=\"status\" slot-scope=\"text\">\n                <a-tag :color=\"getStatusColor(text)\">\n                  {{ text }}\n                </a-tag>\n              </template>\n              <template slot=\"amount\" slot-scope=\"text\">\n                <span class=\"withdraw-amount\">¥{{ text }}</span>\n              </template>\n              <template slot=\"action\" slot-scope=\"text, record\">\n                <a-button\n                  v-if=\"record.rawStatus === 1\"\n                  type=\"danger\"\n                  size=\"small\"\n                  :loading=\"cancelLoading\"\n                  @click=\"handleCancelWithdraw(record)\"\n                >\n                  取消提现\n                </a-button>\n                <span v-else>-</span>\n              </template>\n            </a-table>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 二维码弹窗 -->\n    <a-modal\n      v-model=\"showQRModal\"\n      title=\"邀请二维码\"\n      :footer=\"null\"\n      width=\"400px\"\n      centered\n    >\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n          <img :src=\"qrCodeUrl\" alt=\"邀请二维码\" class=\"qr-code-image\" />\n        </div>\n        <div class=\"qr-actions\">\n          <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n            <a-icon type=\"download\" />\n            下载二维码\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n\n    <!-- 提现弹窗 -->\n    <a-modal\n      v-model=\"showWithdrawModal\"\n      title=\"申请提现\"\n      :footer=\"null\"\n      width=\"500px\"\n      centered\n    >\n      <div class=\"withdraw-modal-content\">\n        <div class=\"withdraw-info\">\n          <div class=\"info-item\">\n            <span class=\"info-label\">可提现金额：</span>\n            <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n          </div>\n          <div class=\"info-item\">\n            <span class=\"info-label\">最低提现金额：</span>\n            <span class=\"info-value\">¥50.00</span>\n          </div>\n        </div>\n\n        <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n          <a-form-item label=\"提现金额\">\n            <a-input-number\n              v-decorator=\"['amount', {\n                rules: [\n                  { required: true, message: '请输入提现金额' },\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                ]\n              }]\"\n              :min=\"50\"\n              :max=\"availableEarnings\"\n              :precision=\"2\"\n              style=\"width: 100%\"\n              placeholder=\"请输入提现金额\"\n            >\n              <template slot=\"addonAfter\">元</template>\n            </a-input-number>\n          </a-form-item>\n\n          <a-form-item label=\"提现方式\">\n            <a-select\n              v-decorator=\"['method', {\n                rules: [{ required: true, message: '请选择提现方式' }],\n                initialValue: 'alipay'\n              }]\"\n              placeholder=\"请选择提现方式\"\n              disabled\n            >\n              <a-select-option value=\"alipay\">支付宝</a-select-option>\n            </a-select>\n          </a-form-item>\n\n          <a-form-item label=\"支付宝手机号\">\n            <a-input\n              v-decorator=\"['alipayAccount', {\n                rules: [\n                  { required: true, message: '请输入支付宝手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                ]\n              }]\"\n              placeholder=\"请输入支付宝手机号\"\n            />\n          </a-form-item>\n\n          <a-form-item label=\"收款人真实姓名\">\n            <a-input\n              v-decorator=\"['realName', {\n                rules: [\n                  { required: true, message: '请输入收款人真实姓名' },\n                  { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                ]\n              }]\"\n              placeholder=\"请输入收款人真实姓名\"\n            />\n          </a-form-item>\n        </a-form>\n\n        <div class=\"withdraw-actions\">\n          <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n            取消\n          </a-button>\n          <a-button\n            type=\"primary\"\n            :loading=\"withdrawLoading\"\n            @click=\"handleWithdraw\"\n          >\n            申请提现\n          </a-button>\n        </div>\n      </div>\n    </a-modal>\n  </div>\n</WebsitePage>\n", null]}