{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\payment\\Success.vue", "mtime": 1753756307330}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'PaymentSuccess',\n  data: function data() {\n    return {\n      orderInfo: null\n    };\n  },\n  mounted: function mounted() {\n    this.loadOrderInfo();\n  },\n  methods: {\n    loadOrderInfo: function loadOrderInfo() {\n      // 从URL参数获取订单信息\n      var orderId = this.$route.query.orderId;\n\n      if (orderId) {\n        this.orderInfo = {\n          orderId: orderId,\n          amount: this.$route.query.amount || null\n        };\n      }\n    },\n    formatTime: function formatTime(date) {\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    },\n    goToUserCenter: function goToUserCenter() {\n      this.$router.push('/usercenter/credits');\n    },\n    goToMarket: function goToMarket() {\n      this.$router.push('/market');\n    },\n    goHome: function goHome() {\n      this.$router.push('/home');\n    }\n  }\n};", {"version": 3, "sources": ["Success.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,SAAA,EAAA;AADA,KAAA;AAGA,GANA;AAQA,EAAA,OARA,qBAQA;AACA,SAAA,aAAA;AACA,GAVA;AAYA,EAAA,OAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA;AACA,UAAA,OAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,OAAA;;AACA,UAAA,OAAA,EAAA;AACA,aAAA,SAAA,GAAA;AACA,UAAA,OAAA,EAAA,OADA;AAEA,UAAA,MAAA,EAAA,KAAA,MAAA,CAAA,KAAA,CAAA,MAAA,IAAA;AAFA,SAAA;AAIA;AACA,KAVA;AAYA,IAAA,UAZA,sBAYA,IAZA,EAYA;AACA,aAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,GAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,MAAA,EAAA;AANA,OAAA,CAAA;AAQA,KArBA;AAuBA,IAAA,cAvBA,4BAuBA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,qBAAA;AACA,KAzBA;AA2BA,IAAA,UA3BA,wBA2BA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,SAAA;AACA,KA7BA;AA+BA,IAAA,MA/BA,oBA+BA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,OAAA;AACA;AAjCA;AAZA,CAAA", "sourcesContent": ["<template>\n  <div class=\"payment-success-page\">\n    <div class=\"success-container\">\n      <!-- 成功图标 -->\n      <div class=\"success-icon\">\n        <a-icon type=\"check-circle\" theme=\"filled\" />\n      </div>\n      \n      <!-- 成功标题 -->\n      <h1 class=\"success-title\">支付成功！</h1>\n      \n      <!-- 订单信息 -->\n      <div class=\"order-info\" v-if=\"orderInfo\">\n        <div class=\"info-item\">\n          <span class=\"label\">订单号：</span>\n          <span class=\"value\">{{ orderInfo.orderId }}</span>\n        </div>\n        <div class=\"info-item\" v-if=\"orderInfo.amount\">\n          <span class=\"label\">支付金额：</span>\n          <span class=\"value amount\">¥{{ orderInfo.amount }}</span>\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">支付时间：</span>\n          <span class=\"value\">{{ formatTime(new Date()) }}</span>\n        </div>\n      </div>\n      \n      <!-- 成功消息 -->\n      <div class=\"success-message\">\n        <p>您的充值已成功完成，余额将在几分钟内到账。</p>\n        <p>感谢您对智界AIGC的支持！</p>\n      </div>\n      \n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <a-button type=\"primary\" size=\"large\" @click=\"goToUserCenter\">\n          查看余额\n        </a-button>\n        <a-button size=\"large\" @click=\"goToMarket\" style=\"margin-left: 16px\">\n          去购买插件\n        </a-button>\n        <a-button size=\"large\" @click=\"goHome\" style=\"margin-left: 16px\">\n          返回首页\n        </a-button>\n      </div>\n      \n      <!-- 温馨提示 -->\n      <div class=\"tips\">\n        <a-alert\n          message=\"温馨提示\"\n          description=\"如果余额未及时到账，请联系客服或查看交易记录。\"\n          type=\"info\"\n          show-icon\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'PaymentSuccess',\n  data() {\n    return {\n      orderInfo: null\n    }\n  },\n  \n  mounted() {\n    this.loadOrderInfo()\n  },\n  \n  methods: {\n    loadOrderInfo() {\n      // 从URL参数获取订单信息\n      const orderId = this.$route.query.orderId\n      if (orderId) {\n        this.orderInfo = {\n          orderId: orderId,\n          amount: this.$route.query.amount || null\n        }\n      }\n    },\n    \n    formatTime(date) {\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    \n    goToUserCenter() {\n      this.$router.push('/usercenter/credits')\n    },\n    \n    goToMarket() {\n      this.$router.push('/market')\n    },\n    \n    goHome() {\n      this.$router.push('/home')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.payment-success-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.success-container {\n  background: white;\n  border-radius: 16px;\n  padding: 48px 40px;\n  text-align: center;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 100%;\n}\n\n.success-icon {\n  font-size: 80px;\n  color: #52c41a;\n  margin-bottom: 24px;\n}\n\n.success-title {\n  font-size: 32px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 32px;\n}\n\n.order-info {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 32px;\n  text-align: left;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.label {\n  color: #8c8c8c;\n  font-size: 14px;\n}\n\n.value {\n  color: #262626;\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.value.amount {\n  color: #f5222d;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.success-message {\n  margin-bottom: 32px;\n  color: #595959;\n  line-height: 1.6;\n}\n\n.action-buttons {\n  margin-bottom: 32px;\n}\n\n.tips {\n  text-align: left;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .success-container {\n    padding: 32px 24px;\n  }\n  \n  .success-title {\n    font-size: 24px;\n  }\n  \n  .success-icon {\n    font-size: 60px;\n  }\n  \n  .action-buttons .ant-btn {\n    margin: 8px 4px !important;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/payment"}]}