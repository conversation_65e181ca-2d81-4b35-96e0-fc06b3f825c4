{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue?vue&type=template&id=7ef8aeb4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\auth\\Login.vue", "mtime": 1753512620053}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"website-login\"},[_vm._m(0),_c('WebsiteHeader'),_c('div',{staticClass:\"login-main\"},[_c('div',{ref:\"loginInfo\",staticClass:\"login-info\"},[_c('div',{staticClass:\"info-content\"},[_c('div',{staticClass:\"brand-showcase\"},[_c('div',{staticClass:\"brand-logo-large\"},[_c('LogoImage',{attrs:{\"size\":\"large\",\"hover\":false,\"container-class\":\"login-logo-container\",\"image-class\":\"login-logo-image\",\"fallback-class\":\"login-logo-fallback\"}}),_c('h1',{staticClass:\"brand-title\"},[_vm._v(\"智界AIGC\")])],1),_c('p',{staticClass:\"brand-slogan\"},[_vm._v(\"AI驱动的内容生成平台\")])]),_c('div',{staticClass:\"feature-highlights\"},_vm._l((_vm.features),function(feature,index){return _c('div',{key:index,staticClass:\"feature-item\"},[_c('div',{staticClass:\"feature-icon\"},[_c('a-icon',{attrs:{\"type\":feature.icon}})],1),_c('div',{staticClass:\"feature-text\"},[_c('h3',[_vm._v(_vm._s(feature.title))]),_c('p',[_vm._v(_vm._s(feature.description))])])])}),0)])]),_c('div',{ref:\"loginContainer\",staticClass:\"login-container\"},[_c('div',{staticClass:\"login-card\"},[_c('div',{staticClass:\"login-header\"},[_c('h2',{staticClass:\"login-title\"},[_vm._v(\"欢迎使用智界AIGC\")]),_c('p',{staticClass:\"login-subtitle\"},[_vm._v(_vm._s(_vm.inviteCodeFromUrl ? '您正在通过邀请链接登录' : '选择您的登录方式，开启AI创作之旅'))])]),_c('div',{staticClass:\"auth-tabs\"},[_c('div',{staticClass:\"tab-buttons\"},[_c('button',{class:['tab-btn', { active: _vm.loginType === 'phone' }],on:{\"click\":function($event){return _vm.switchLoginType('phone')}}},[_c('a-icon',{attrs:{\"type\":\"mobile\"}}),_c('span',{staticClass:\"tab-text\"},[_vm._v(\"手机号\")])],1),_c('button',{class:['tab-btn', { active: _vm.loginType === 'email' }],on:{\"click\":function($event){return _vm.switchLoginType('email')}}},[_c('a-icon',{attrs:{\"type\":\"mail\"}}),_c('span',{staticClass:\"tab-text\"},[_vm._v(\"邮箱\")])],1),_c('button',{class:['tab-btn', { active: _vm.loginType === 'password' }],on:{\"click\":function($event){return _vm.switchLoginType('password')}}},[_c('a-icon',{attrs:{\"type\":\"lock\"}}),_c('span',{staticClass:\"tab-text\"},[_vm._v(\"密码登录\")])],1)])]),_c('div',{staticClass:\"login-form\"},[(_vm.loginType === 'password')?_c('div',{staticClass:\"login-content\"},[_c('a-form',{staticClass:\"account-login-form\",attrs:{\"form\":_vm.form},on:{\"submit\":_vm.handleSubmit}},[_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]),expression:\"['username', { rules: [{ required: true, message: '请输入用户名或邮箱' }] }]\"}],staticClass:\"clean-input\",attrs:{\"size\":\"large\",\"placeholder\":\"用户名或邮箱\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"user\"},slot:\"prefix\"})],1)],1)],1),_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('a-input-password',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['password', { rules: [{ required: true, message: '请输入密码' }] }]),expression:\"['password', { rules: [{ required: true, message: '请输入密码' }] }]\"}],staticClass:\"clean-input\",attrs:{\"size\":\"large\",\"placeholder\":\"密码\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"lock\"},slot:\"prefix\"})],1)],1)],1),_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('div',{staticClass:\"captcha-row\"},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]),expression:\"['inputCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"}],staticClass:\"clean-input captcha-input\",attrs:{\"size\":\"large\",\"placeholder\":\"验证码\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"safety-certificate\"},slot:\"prefix\"})],1),_c('div',{staticClass:\"captcha-image-container\",on:{\"click\":_vm.handleChangeCheckCode}},[_c('img',{staticClass:\"captcha-image\",attrs:{\"src\":_vm.randCodeImage,\"alt\":\"验证码\"}}),_c('div',{staticClass:\"captcha-refresh-overlay\"},[_c('a-icon',{attrs:{\"type\":\"reload\"}})],1)])],1)])],1),_c('div',{staticClass:\"login-options\"},[_c('a-checkbox',{staticClass:\"remember-me\",model:{value:(_vm.rememberMe),callback:function ($$v) {_vm.rememberMe=$$v},expression:\"rememberMe\"}},[_vm._v(\"\\n                记住我\\n              \")]),_c('a',{staticClass:\"forgot-link\",on:{\"click\":_vm.handleForgotPassword}},[_vm._v(\"\\n                忘记密码？\\n              \")])],1),_c('a-form-item',{staticClass:\"login-button-item\"},[_c('a-button',{staticClass:\"login-submit-button\",attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"size\":\"large\",\"loading\":_vm.loginLoading,\"block\":\"\"}},[(!_vm.loginLoading)?_c('span',[_vm._v(\"登录\")]):_c('span',[_vm._v(\"登录中...\")])])],1)],1)],1):_vm._e(),(_vm.loginType === 'phone')?_c('div',{staticClass:\"login-content\"},[_c('a-form',{staticClass:\"phone-login-form\",attrs:{\"form\":_vm.phoneLoginForm},on:{\"submit\":_vm.handlePhoneLogin}},[_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['phone', { rules: [\n                      { required: true, message: '请输入手机号' },\n                      { pattern: /^1[3-9]\\d{9}$/, message: '手机号格式不正确' }\n                    ] }]),expression:\"['phone', { rules: [\\n                      { required: true, message: '请输入手机号' },\\n                      { pattern: /^1[3-9]\\\\d{9}$/, message: '手机号格式不正确' }\\n                    ] }]\"}],staticClass:\"clean-input\",attrs:{\"size\":\"large\",\"placeholder\":\"请输入手机号\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"mobile\"},slot:\"prefix\"})],1)],1)],1),_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('div',{staticClass:\"verify-code-row\"},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]),expression:\"['smsCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"}],staticClass:\"clean-input verify-code-input\",attrs:{\"size\":\"large\",\"placeholder\":\"请输入短信验证码\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"safety-certificate\"},slot:\"prefix\"})],1),_c('a-button',{staticClass:\"send-code-btn\",attrs:{\"disabled\":_vm.smsCodeSending || _vm.smsCountdown > 0,\"size\":\"large\"},on:{\"click\":_vm.sendLoginSmsCode}},[_vm._v(\"\\n                      \"+_vm._s(_vm.smsCountdown > 0 ? (_vm.smsCountdown + \"s后重发\") : '发送验证码')+\"\\n                    \")])],1)])],1),_c('a-form-item',{staticClass:\"login-button-item\"},[_c('a-button',{staticClass:\"login-submit-button\",attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"size\":\"large\",\"loading\":_vm.phoneLoginLoading,\"block\":\"\"}},[(!_vm.phoneLoginLoading)?_c('span',[_vm._v(\"登录\")]):_c('span',[_vm._v(\"登录中...\")])])],1),_c('div',{staticClass:\"phone-login-tip\"},[_c('a-alert',{attrs:{\"message\":\"手机号登录说明\",\"description\":\"首次使用手机号登录将自动为您创建账户，无需设置密码\",\"type\":\"info\",\"show-icon\":\"\"}})],1)],1)],1):_vm._e(),(_vm.loginType === 'email')?_c('div',{staticClass:\"login-content\"},[_c('a-form',{staticClass:\"email-login-form\",attrs:{\"form\":_vm.emailLoginForm},on:{\"submit\":_vm.handleEmailLogin}},[_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['email', { rules: [\n                      { required: true, message: '请输入邮箱' },\n                      { type: 'email', message: '邮箱格式不正确' }\n                    ] }]),expression:\"['email', { rules: [\\n                      { required: true, message: '请输入邮箱' },\\n                      { type: 'email', message: '邮箱格式不正确' }\\n                    ] }]\"}],staticClass:\"clean-input\",attrs:{\"size\":\"large\",\"placeholder\":\"请输入邮箱\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"mail\"},slot:\"prefix\"})],1)],1)],1),_c('div',{staticClass:\"input-group\"},[_c('a-form-item',[_c('div',{staticClass:\"verify-code-row\"},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]),expression:\"['emailCode', { rules: [{ required: true, message: '请输入验证码' }] }]\"}],staticClass:\"clean-input verify-code-input\",attrs:{\"size\":\"large\",\"placeholder\":\"请输入邮箱验证码\"}},[_c('a-icon',{attrs:{\"slot\":\"prefix\",\"type\":\"safety-certificate\"},slot:\"prefix\"})],1),_c('a-button',{staticClass:\"send-code-btn\",attrs:{\"disabled\":_vm.emailCodeSending || _vm.emailCountdown > 0,\"size\":\"large\"},on:{\"click\":_vm.sendLoginEmailCode}},[_vm._v(\"\\n                      \"+_vm._s(_vm.emailCountdown > 0 ? (_vm.emailCountdown + \"s后重发\") : '发送验证码')+\"\\n                    \")])],1)])],1),_c('a-form-item',{staticClass:\"login-button-item\"},[_c('a-button',{staticClass:\"login-submit-button\",attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"size\":\"large\",\"loading\":_vm.emailLoginLoading,\"block\":\"\"}},[(!_vm.emailLoginLoading)?_c('span',[_vm._v(\"登录\")]):_c('span',[_vm._v(\"登录中...\")])])],1),_c('div',{staticClass:\"email-login-tip\"},[_c('a-alert',{attrs:{\"message\":\"邮箱登录说明\",\"description\":\"首次使用邮箱登录将自动为您创建账户，无需设置密码\",\"type\":\"info\",\"show-icon\":\"\"}})],1)],1)],1):_vm._e(),(_vm.loginType === 'wechat')?_c('div',{staticClass:\"login-content\"},[_c('div',{staticClass:\"wechat-login-container\"},[_c('div',{staticClass:\"wechat-qr-section\"},[_c('div',{staticClass:\"qr-code-container\"},[(_vm.wechatLoginQrCode)?_c('img',{staticClass:\"qr-code-image\",attrs:{\"src\":_vm.wechatLoginQrCode,\"alt\":\"微信登录二维码\"}}):_c('div',{staticClass:\"qr-loading\"},[_c('a-spin',{attrs:{\"size\":\"large\"}}),_c('p',[_vm._v(\"正在生成二维码...\")])],1)]),_c('div',{staticClass:\"qr-instructions\"},[_c('h4',[_vm._v(\"使用微信扫码登录\")]),_c('p',[_vm._v(\"1. 打开微信扫一扫\")]),_c('p',[_vm._v(\"2. 扫描上方二维码\")]),_c('p',[_vm._v(\"3. 确认登录\")]),(_vm.inviteCodeFromUrl)?_c('p',{staticClass:\"invite-tip\"},[_vm._v(\"* 您正在通过邀请链接登录\")]):_vm._e()])])])]):_vm._e()])])])])],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"login-background\"},[_c('div',{staticClass:\"bg-animated-grid\"}),_c('div',{staticClass:\"bg-floating-elements\"}),_c('div',{staticClass:\"bg-gradient-overlay\"})])}]\n\nexport { render, staticRenderFns }"]}