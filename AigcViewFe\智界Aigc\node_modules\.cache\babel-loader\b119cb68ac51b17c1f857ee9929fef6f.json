{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport { getReferralStats, generateReferralLink, getUserRole, getLevelConfig } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      loading: true,\n      qrLoading: false,\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n      // 邀请链接\n      affiliateLink: '',\n      // 佣金等级\n      userRole: 'user',\n      // user, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手邀请员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级邀请员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n      // 佣金等级配置\n      commissionLevels: [],\n      allLevelConfigs: [],\n      // 从数据库获取的完整等级配置\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false,\n      // 是否已预生成二维码\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n      // 邀请用户列表\n      referralUsers: [],\n      usersLoading: false,\n      usersPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total, range) {\n          return \"\\u7B2C \".concat(range[0], \"-\").concat(range[1], \" \\u6761\\uFF0C\\u5171 \").concat(total, \" \\u6761\");\n        }\n      },\n      // 邀请用户排序\n      usersSort: {\n        orderBy: 'total_reward',\n        order: 'desc'\n      },\n      defaultAvatar: '/default-avatar.png',\n      // 本地降级头像\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      cancelLoading: false,\n      withdrawPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total, range) {\n          return \"\\u7B2C \".concat(range[0], \"-\").concat(range[1], \" \\u6761\\uFF0C\\u5171 \").concat(total, \" \\u6761\");\n        }\n      },\n      // 提现记录排序\n      withdrawSort: {\n        orderBy: 'apply_time',\n        order: 'desc'\n      },\n      // 提现记录筛选\n      withdrawFilter: {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        // null表示\"全部\"\n        dateRange: [],\n        completeDateRange: []\n      },\n      // 用户信息\n      userInfo: null\n    };\n  },\n  computed: {\n    // 动态计算表格列配置，确保排序状态响应式更新\n    userColumns: function userColumns() {\n      return [{\n        title: '头像',\n        dataIndex: 'avatar',\n        key: 'avatar',\n        scopedSlots: {\n          customRender: 'avatar'\n        },\n        width: 80\n      }, {\n        title: '用户昵称',\n        dataIndex: 'nickname',\n        key: 'nickname',\n        sorter: true\n      }, {\n        title: '注册时间',\n        dataIndex: 'registerTime',\n        key: 'registerTime',\n        sorter: true\n      }, {\n        title: '获得奖励',\n        dataIndex: 'reward',\n        key: 'reward',\n        scopedSlots: {\n          customRender: 'reward'\n        },\n        sorter: true\n      }];\n    },\n    // 动态计算提现记录表格列配置，确保排序状态响应式更新\n    withdrawColumns: function withdrawColumns() {\n      return [{\n        title: '提现金额',\n        dataIndex: 'amount',\n        key: 'amount',\n        scopedSlots: {\n          customRender: 'amount'\n        },\n        sorter: true\n      }, {\n        title: '提现方式',\n        dataIndex: 'method',\n        key: 'method'\n      }, {\n        title: '申请时间',\n        dataIndex: 'applyTime',\n        key: 'applyTime',\n        sorter: true\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        scopedSlots: {\n          customRender: 'status'\n        },\n        sorter: true\n      }, {\n        title: '完成时间',\n        dataIndex: 'completeTime',\n        key: 'completeTime',\n        sorter: true\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 100,\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }];\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.checkLoginAndLoadData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    // 获取头像URL（处理CDN路径和默认头像）\n    getAvatarUrl: function getAvatarUrl(avatar) {\n      if (!avatar) {\n        return this.defaultAvatar;\n      } // 如果是完整的URL，直接返回\n\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是相对路径，使用getFileAccessHttpUrl转换\n\n\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar;\n    },\n    // 处理文件访问URL（和其他组件保持一致）\n    getFileAccessHttpUrl: function getFileAccessHttpUrl(avatar) {\n      if (!avatar) return this.defaultAvatar; // 如果已经是完整URL，直接返回\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是TOS文件，使用全局方法\n\n\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar;\n      } // 本地文件，使用静态域名\n\n\n      var staticDomain = this.$store.state.app.staticDomainURL;\n      return staticDomain ? \"\".concat(staticDomain, \"/\").concat(avatar) : avatar;\n    },\n    // 加载TOS默认头像URL\n    loadDefaultAvatar: function () {\n      var _loadDefaultAvatar = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return this.$http.get('/sys/common/default-avatar-url');\n\n              case 3:\n                response = _context2.sent;\n\n                if (response && response.success && response.result) {\n                  this.defaultAvatar = response.result;\n                  console.log('🎯 Affiliate: 已加载TOS默认头像:', this.defaultAvatar);\n                }\n\n                _context2.next = 10;\n                break;\n\n              case 7:\n                _context2.prev = 7;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.warn('⚠️ Affiliate: 获取TOS默认头像失败，使用本地降级:', _context2.t0); // 保持本地默认头像作为降级方案\n\n              case 10:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 7]]);\n      }));\n\n      function loadDefaultAvatar() {\n        return _loadDefaultAvatar.apply(this, arguments);\n      }\n\n      return loadDefaultAvatar;\n    }(),\n    // 检查登录状态并加载数据\n    checkLoginAndLoadData: function () {\n      var _checkLoginAndLoadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var token;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context3.next = 4;\n                  break;\n                }\n\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context3.abrupt(\"return\");\n\n              case 4:\n                _context3.prev = 4;\n                _context3.next = 7;\n                return Promise.all([this.loadReferralData(), this.loadReferralLink(), this.loadUserRole(), this.loadLevelConfig(), this.loadReferralUsers(), this.loadWithdrawRecords(), this.loadDefaultAvatar()]);\n\n              case 7:\n                // 计算佣金等级\n                this.calculateCommissionLevel(); // 自动预生成邀请二维码\n\n                this.preGenerateQRCode();\n                _context3.next = 15;\n                break;\n\n              case 11:\n                _context3.prev = 11;\n                _context3.t0 = _context3[\"catch\"](4);\n                console.error('加载分销数据失败:', _context3.t0);\n                this.$notification.error({\n                  message: '加载失败',\n                  description: '获取分销数据失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 15:\n                _context3.prev = 15;\n                this.loading = false;\n                return _context3.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[4, 11, 15, 18]]);\n      }));\n\n      function checkLoginAndLoadData() {\n        return _checkLoginAndLoadData.apply(this, arguments);\n      }\n\n      return checkLoginAndLoadData;\n    }(),\n    // 加载推荐统计数据\n    loadReferralData: function () {\n      var _loadReferralData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return getReferralStats();\n\n              case 3:\n                response = _context4.sent;\n\n                if (response.success) {\n                  data = response.result;\n                  this.totalEarnings = data.total_reward_amount || 0;\n                  this.availableEarnings = data.available_rewards || 0;\n                  this.totalReferrals = data.total_referrals || 0;\n                  this.memberReferrals = data.member_referrals || 0;\n                }\n\n                _context4.next = 11;\n                break;\n\n              case 7:\n                _context4.prev = 7;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('获取推荐统计失败:', _context4.t0);\n                throw _context4.t0;\n\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 7]]);\n      }));\n\n      function loadReferralData() {\n        return _loadReferralData.apply(this, arguments);\n      }\n\n      return loadReferralData;\n    }(),\n    // 加载推荐链接\n    loadReferralLink: function () {\n      var _loadReferralLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                _context5.next = 3;\n                return generateReferralLink({});\n\n              case 3:\n                response = _context5.sent;\n\n                if (response.success) {\n                  this.affiliateLink = response.result || '';\n                }\n\n                _context5.next = 11;\n                break;\n\n              case 7:\n                _context5.prev = 7;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('获取推荐链接失败:', _context5.t0); // 如果获取失败，使用默认链接格式\n\n                this.affiliateLink = \"\".concat(window.location.origin, \"?ref=loading...\");\n\n              case 11:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 7]]);\n      }));\n\n      function loadReferralLink() {\n        return _loadReferralLink.apply(this, arguments);\n      }\n\n      return loadReferralLink;\n    }(),\n    // 加载用户角色信息\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                _context6.next = 3;\n                return getUserRole();\n\n              case 3:\n                response = _context6.sent;\n\n                if (response.success) {\n                  this.userRole = response.result.role_code || 'user';\n                }\n\n                _context6.next = 11;\n                break;\n\n              case 7:\n                _context6.prev = 7;\n                _context6.t0 = _context6[\"catch\"](0);\n                console.error('获取用户角色失败:', _context6.t0);\n                this.userRole = 'user';\n\n              case 11:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 7]]);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    // 加载等级配置信息\n    loadLevelConfig: function () {\n      var _loadLevelConfig = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                _context7.prev = 0;\n                _context7.next = 3;\n                return getLevelConfig();\n\n              case 3:\n                response = _context7.sent;\n\n                if (response.success) {\n                  this.allLevelConfigs = response.result || [];\n                }\n\n                _context7.next = 11;\n                break;\n\n              case 7:\n                _context7.prev = 7;\n                _context7.t0 = _context7[\"catch\"](0);\n                console.error('获取等级配置失败:', _context7.t0);\n                this.allLevelConfigs = [];\n\n              case 11:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[0, 7]]);\n      }));\n\n      function loadLevelConfig() {\n        return _loadLevelConfig.apply(this, arguments);\n      }\n\n      return loadLevelConfig;\n    }(),\n    // 计算佣金等级和进度\n    calculateCommissionLevel: function calculateCommissionLevel() {\n      var _this = this;\n\n      var memberCount = this.memberReferrals; // 从数据库配置中获取当前用户角色的等级配置\n\n      var userLevelConfigs = this.allLevelConfigs.filter(function (config) {\n        return config.role_code === _this.userRole;\n      });\n\n      if (userLevelConfigs.length === 0) {\n        console.warn('未找到用户角色的等级配置:', this.userRole);\n        return;\n      } // 根据邀请人数确定当前等级\n\n\n      var currentLevel = null;\n\n      for (var i = userLevelConfigs.length - 1; i >= 0; i--) {\n        if (memberCount >= userLevelConfigs[i].min_referrals) {\n          currentLevel = userLevelConfigs[i];\n          break;\n        }\n      }\n\n      if (!currentLevel) {\n        currentLevel = userLevelConfigs[0]; // 默认最低等级\n      } // 设置当前等级信息\n\n\n      this.currentCommissionRate = parseFloat(currentLevel.commission_rate);\n      this.commissionLevelText = currentLevel.level_name; // 查找下一个等级\n\n      var nextLevel = userLevelConfigs.find(function (config) {\n        return config.min_referrals > memberCount;\n      });\n\n      if (nextLevel) {\n        this.nextLevelRequirement = nextLevel.min_referrals;\n        this.nextLevelText = nextLevel.level_name;\n        this.nextLevelRate = parseFloat(nextLevel.commission_rate);\n        this.levelProgress = memberCount / nextLevel.min_referrals * 100;\n        this.progressColor = '#1890ff';\n      } else {\n        // 已达最高等级\n        this.nextLevelRequirement = 0;\n        this.nextLevelText = '已达最高等级';\n        this.nextLevelRate = this.currentCommissionRate;\n        this.levelProgress = 100;\n        this.progressColor = '#722ed1';\n      } // 生成等级进度显示数据\n\n\n      this.commissionLevels = userLevelConfigs.map(function (config, index) {\n        var isCompleted = memberCount >= config.min_referrals; // 判断当前等级：如果不是已完成，且满足前一个等级的要求，则为当前等级\n\n        var isCurrent = false;\n\n        if (!isCompleted) {\n          if (index === 0) {\n            // 第一个等级，如果没完成就是当前等级\n            isCurrent = true;\n          } else {\n            // 其他等级，如果满足前一个等级要求但不满足当前等级要求，则为当前等级\n            var prevRequirement = userLevelConfigs[index - 1].min_referrals;\n            isCurrent = memberCount >= prevRequirement;\n          }\n        }\n\n        var isUpcoming = !isCompleted && !isCurrent;\n        var remaining = 0;\n\n        if (!isCompleted) {\n          remaining = config.min_referrals - memberCount;\n        }\n\n        return {\n          name: config.level_name,\n          rate: parseFloat(config.commission_rate),\n          requirement: config.min_referrals,\n          isCompleted: isCompleted,\n          isCurrent: isCurrent,\n          isUpcoming: isUpcoming,\n          remaining: remaining > 0 ? remaining : 0\n        };\n      });\n    },\n    // 复制邀请链接\n    copyLink: function copyLink() {\n      var _this2 = this;\n\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '邀请链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(function () {\n        _this2.$notification.success({\n          message: '邀请链接已复制',\n          description: '邀请链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        });\n      }).catch(function () {\n        _this2.$notification.error({\n          message: '复制失败',\n          description: '复制邀请链接失败，请手动复制',\n          placement: 'topRight'\n        });\n      });\n    },\n    // 预生成邀请二维码（后台静默生成）\n    preGenerateQRCode: function () {\n      var _preGenerateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                if (!(!this.affiliateLink || this.qrPreGenerated)) {\n                  _context8.next = 2;\n                  break;\n                }\n\n                return _context8.abrupt(\"return\");\n\n              case 2:\n                _context8.prev = 2;\n                console.log('开始预生成邀请二维码...'); // 调用后端API生成二维码并上传到TOS\n\n                _context8.next = 6;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 6:\n                response = _context8.sent;\n\n                if (response && response.success) {\n                  // 静默保存二维码URL\n                  this.qrCodeUrl = response.result;\n                  this.qrPreGenerated = true;\n                  console.log('邀请二维码预生成成功:', this.qrCodeUrl);\n                }\n\n                _context8.next = 13;\n                break;\n\n              case 10:\n                _context8.prev = 10;\n                _context8.t0 = _context8[\"catch\"](2);\n                console.error('预生成二维码失败:', _context8.t0); // 预生成失败不显示错误提示，用户点击时再重试\n\n              case 13:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[2, 10]]);\n      }));\n\n      function preGenerateQRCode() {\n        return _preGenerateQRCode.apply(this, arguments);\n      }\n\n      return preGenerateQRCode;\n    }(),\n    // 生成邀请二维码（用户主动点击）\n    generateQRCode: function () {\n      var _generateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        var response, errorMsg;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                if (this.affiliateLink) {\n                  _context9.next = 3;\n                  break;\n                }\n\n                this.$notification.warning({\n                  message: '邀请链接未生成',\n                  description: '请等待邀请链接生成完成后再生成二维码',\n                  placement: 'topRight'\n                });\n                return _context9.abrupt(\"return\");\n\n              case 3:\n                if (!(this.qrPreGenerated && this.qrCodeUrl)) {\n                  _context9.next = 6;\n                  break;\n                }\n\n                this.showQRModal = true;\n                return _context9.abrupt(\"return\");\n\n              case 6:\n                _context9.prev = 6;\n                this.qrLoading = true; // 调用后端API生成二维码并上传到TOS\n\n                _context9.next = 10;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 10:\n                response = _context9.sent;\n                console.log('二维码生成响应:', response);\n\n                if (!(response && response.success)) {\n                  _context9.next = 19;\n                  break;\n                }\n\n                // 使用CDN地址\n                this.qrCodeUrl = response.result;\n                this.qrPreGenerated = true;\n                this.showQRModal = true;\n                this.$notification.success({\n                  message: '二维码生成成功',\n                  description: '邀请二维码已生成并存储到CDN，可以下载保存',\n                  placement: 'topRight'\n                });\n                _context9.next = 21;\n                break;\n\n              case 19:\n                errorMsg = response && response.message || '生成失败';\n                throw new Error(errorMsg);\n\n              case 21:\n                _context9.next = 27;\n                break;\n\n              case 23:\n                _context9.prev = 23;\n                _context9.t0 = _context9[\"catch\"](6);\n                console.error('生成二维码失败:', _context9.t0);\n                this.$notification.error({\n                  message: '生成失败',\n                  description: _context9.t0.message || '二维码生成失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 27:\n                _context9.prev = 27;\n                this.qrLoading = false;\n                return _context9.finish(27);\n\n              case 30:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[6, 23, 27, 30]]);\n      }));\n\n      function generateQRCode() {\n        return _generateQRCode.apply(this, arguments);\n      }\n\n      return generateQRCode;\n    }(),\n    // 下载二维码\n    downloadQRCode: function downloadQRCode() {\n      if (!this.qrCodeUrl) return;\n\n      try {\n        // 从邀请链接中提取邀请码\n        var referralCode = this.extractReferralCode(this.affiliateLink); // 通过后端代理下载，避免CORS问题\n\n        var backendUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/jeecg-boot';\n        var downloadUrl = \"\".concat(backendUrl, \"/api/usercenter/downloadReferralQRCode?url=\").concat(encodeURIComponent(this.qrCodeUrl), \"&code=\").concat(referralCode, \"&t=\").concat(Date.now());\n        console.log('下载URL:', downloadUrl); // 使用隐藏iframe下载，避免页面跳动\n\n        var iframe = document.createElement('iframe');\n        iframe.style.display = 'none';\n        iframe.style.position = 'absolute';\n        iframe.style.left = '-9999px';\n        iframe.src = downloadUrl;\n        document.body.appendChild(iframe); // 3秒后移除iframe\n\n        setTimeout(function () {\n          if (iframe.parentNode) {\n            document.body.removeChild(iframe);\n          }\n        }, 3000);\n        this.$notification.success({\n          message: '下载开始',\n          description: \"\\u9080\\u8BF7\\u4E8C\\u7EF4\\u7801_\".concat(referralCode, \".png \\u6B63\\u5728\\u4E0B\\u8F7D\"),\n          placement: 'topRight'\n        });\n      } catch (error) {\n        console.error('下载二维码失败:', error);\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        });\n      }\n    },\n    // 从邀请链接中提取邀请码\n    extractReferralCode: function extractReferralCode(url) {\n      if (!url) return 'UNKNOWN';\n\n      try {\n        var urlObj = new URL(url);\n        var refParam = urlObj.searchParams.get('ref');\n        return refParam || 'UNKNOWN';\n      } catch (error) {\n        console.error('提取邀请码失败:', error);\n        return 'UNKNOWN';\n      }\n    },\n    // 显示提现弹窗\n    openWithdrawModal: function openWithdrawModal() {\n      if (this.availableEarnings < 50) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为50元，请继续邀请获得更多收益',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      this.showWithdrawModal = true;\n    },\n    // 处理提现申请\n    handleWithdraw: function () {\n      var _handleWithdraw = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n          while (1) {\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                this.withdrawForm.validateFields( /*#__PURE__*/function () {\n                  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11(err, values) {\n                    var h;\n                    return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n                      while (1) {\n                        switch (_context11.prev = _context11.next) {\n                          case 0:\n                            if (!err) {\n                              _context11.next = 2;\n                              break;\n                            }\n\n                            return _context11.abrupt(\"return\");\n\n                          case 2:\n                            // 二次确认弹窗\n                            h = _this3.$createElement;\n\n                            _this3.$confirm({\n                              title: '确认提现申请',\n                              content: h('div', {\n                                style: {\n                                  margin: '16px 0'\n                                }\n                              }, [h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '提现金额：'), \"\\xA5\".concat(values.amount)]), h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '支付宝账号：'), values.alipayAccount]), h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '收款人姓名：'), values.realName]), h('p', {\n                                style: {\n                                  color: '#ff4d4f',\n                                  marginTop: '12px',\n                                  marginBottom: '0'\n                                }\n                              }, [h('strong', '注意：'), '请再次核实一遍提现信息，请确认信息无误！'])]),\n                              okText: '确认提现',\n                              cancelText: '取消',\n                              centered: true,\n                              width: 400,\n                              onOk: function () {\n                                var _onOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n                                  return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n                                    while (1) {\n                                      switch (_context10.prev = _context10.next) {\n                                        case 0:\n                                          _context10.next = 2;\n                                          return _this3.submitWithdrawRequest(values);\n\n                                        case 2:\n                                        case \"end\":\n                                          return _context10.stop();\n                                      }\n                                    }\n                                  }, _callee10);\n                                }));\n\n                                function onOk() {\n                                  return _onOk.apply(this, arguments);\n                                }\n\n                                return onOk;\n                              }()\n                            });\n\n                          case 4:\n                          case \"end\":\n                            return _context11.stop();\n                        }\n                      }\n                    }, _callee11);\n                  }));\n\n                  return function (_x, _x2) {\n                    return _ref.apply(this, arguments);\n                  };\n                }());\n\n              case 1:\n              case \"end\":\n                return _context12.stop();\n            }\n          }\n        }, _callee12, this);\n      }));\n\n      function handleWithdraw() {\n        return _handleWithdraw.apply(this, arguments);\n      }\n\n      return handleWithdraw;\n    }(),\n    // 提交提现申请\n    submitWithdrawRequest: function () {\n      var _submitWithdrawRequest = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13(values) {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n          while (1) {\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                this.withdrawLoading = true;\n                _context13.prev = 1;\n                params = {\n                  withdrawalAmount: values.amount,\n                  realName: values.realName,\n                  alipayAccount: values.alipayAccount\n                };\n                _context13.next = 5;\n                return this.$http.post('/api/usercenter/applyWithdrawal', params);\n\n              case 5:\n                response = _context13.sent;\n\n                if (!response.success) {\n                  _context13.next = 15;\n                  break;\n                }\n\n                this.withdrawLoading = false;\n                this.showWithdrawModal = false;\n                this.withdrawForm.resetFields();\n                this.$notification.success({\n                  message: '提现申请成功',\n                  description: '您的提现申请已提交，预计1-3个工作日到账',\n                  placement: 'topRight'\n                }); // 刷新数据\n\n                _context13.next = 13;\n                return Promise.all([this.loadReferralData(), this.loadWithdrawRecords()]);\n\n              case 13:\n                _context13.next = 17;\n                break;\n\n              case 15:\n                this.withdrawLoading = false;\n                this.$notification.error({\n                  message: '提现申请失败',\n                  description: response.message || '申请失败，请重试',\n                  placement: 'topRight'\n                });\n\n              case 17:\n                _context13.next = 24;\n                break;\n\n              case 19:\n                _context13.prev = 19;\n                _context13.t0 = _context13[\"catch\"](1);\n                this.withdrawLoading = false;\n                console.error('提现申请失败:', _context13.t0); // 检查是否是HTTP响应错误，如果是则显示后端返回的错误信息\n\n                if (_context13.t0.response && _context13.t0.response.data && _context13.t0.response.data.message) {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: _context13.t0.response.data.message,\n                    placement: 'topRight'\n                  });\n                } else if (_context13.t0.message) {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: _context13.t0.message,\n                    placement: 'topRight'\n                  });\n                } else {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: '网络错误，请稍后重试',\n                    placement: 'topRight'\n                  });\n                }\n\n              case 24:\n              case \"end\":\n                return _context13.stop();\n            }\n          }\n        }, _callee13, this, [[1, 19]]);\n      }));\n\n      function submitWithdrawRequest(_x3) {\n        return _submitWithdrawRequest.apply(this, arguments);\n      }\n\n      return submitWithdrawRequest;\n    }(),\n    // 加载邀请用户列表\n    loadReferralUsers: function () {\n      var _loadReferralUsers = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {\n        var params, response, result, records;\n        return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n          while (1) {\n            switch (_context14.prev = _context14.next) {\n              case 0:\n                _context14.prev = 0;\n                this.usersLoading = true;\n                params = {\n                  current: this.usersPagination && this.usersPagination.current || 1,\n                  size: this.usersPagination && this.usersPagination.pageSize || 10,\n                  orderBy: this.usersSort && this.usersSort.orderBy || 'total_reward',\n                  order: this.usersSort && this.usersSort.order || 'desc'\n                };\n                console.log('加载邀请用户参数:', params);\n                _context14.next = 6;\n                return this.$http.get('/api/usercenter/referralList', {\n                  params: params\n                });\n\n              case 6:\n                response = _context14.sent;\n\n                if (response && response.success) {\n                  result = response.result || {};\n                  records = result.records || []; // 更新分页信息\n\n                  if (this.usersPagination) {\n                    this.usersPagination.total = result.total || 0;\n                  } // 转换数据格式\n\n\n                  this.referralUsers = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      nickname: item.referee_nickname || \"\\u7528\\u6237***\".concat(index + 1),\n                      avatar: item.referee_avatar || '',\n                      registerTime: item.register_time || '',\n                      reward: item.total_reward || '0.00'\n                    };\n                  });\n                } else {\n                  this.referralUsers = [];\n\n                  if (this.usersPagination) {\n                    this.usersPagination.total = 0;\n                  }\n                }\n\n                _context14.next = 15;\n                break;\n\n              case 10:\n                _context14.prev = 10;\n                _context14.t0 = _context14[\"catch\"](0);\n                console.error('获取邀请用户列表失败:', _context14.t0);\n                this.referralUsers = []; // 如果是网络错误或其他错误，显示友好提示\n\n                if (_context14.t0.response && _context14.t0.response.status === 401) {\n                  this.$message.warning('登录已过期，请重新登录');\n                }\n\n              case 15:\n                _context14.prev = 15;\n                this.usersLoading = false;\n                return _context14.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context14.stop();\n            }\n          }\n        }, _callee14, this, [[0, 10, 15, 18]]);\n      }));\n\n      function loadReferralUsers() {\n        return _loadReferralUsers.apply(this, arguments);\n      }\n\n      return loadReferralUsers;\n    }(),\n    // 加载提现记录\n    loadWithdrawRecords: function () {\n      var _loadWithdrawRecords = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15() {\n        var _this4 = this;\n\n        var params, response, result, records;\n        return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n          while (1) {\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                _context15.prev = 0;\n                this.recordsLoading = true;\n                params = {\n                  current: this.withdrawPagination && this.withdrawPagination.current || 1,\n                  size: this.withdrawPagination && this.withdrawPagination.pageSize || 10,\n                  orderBy: this.withdrawSort && this.withdrawSort.orderBy || 'apply_time',\n                  order: this.withdrawSort && this.withdrawSort.order || 'desc'\n                }; // 添加筛选参数\n\n                if (this.withdrawFilter) {\n                  if (this.withdrawFilter.minAmount !== null && this.withdrawFilter.minAmount !== '') {\n                    params.minAmount = this.withdrawFilter.minAmount;\n                  }\n\n                  if (this.withdrawFilter.maxAmount !== null && this.withdrawFilter.maxAmount !== '') {\n                    params.maxAmount = this.withdrawFilter.maxAmount;\n                  }\n\n                  if (this.withdrawFilter.status !== null) {\n                    params.status = this.withdrawFilter.status;\n                  }\n\n                  if (this.withdrawFilter.dateRange && this.withdrawFilter.dateRange.length === 2) {\n                    // 处理moment对象，转换为YYYY-MM-DD格式\n                    params.startDate = this.withdrawFilter.dateRange[0].format ? this.withdrawFilter.dateRange[0].format('YYYY-MM-DD') : this.withdrawFilter.dateRange[0];\n                    params.endDate = this.withdrawFilter.dateRange[1].format ? this.withdrawFilter.dateRange[1].format('YYYY-MM-DD') : this.withdrawFilter.dateRange[1];\n                  }\n\n                  if (this.withdrawFilter.completeDateRange && this.withdrawFilter.completeDateRange.length === 2) {\n                    // 处理moment对象，转换为YYYY-MM-DD格式\n                    params.completeStartDate = this.withdrawFilter.completeDateRange[0].format ? this.withdrawFilter.completeDateRange[0].format('YYYY-MM-DD') : this.withdrawFilter.completeDateRange[0];\n                    params.completeEndDate = this.withdrawFilter.completeDateRange[1].format ? this.withdrawFilter.completeDateRange[1].format('YYYY-MM-DD') : this.withdrawFilter.completeDateRange[1];\n                  }\n                }\n\n                console.log('加载提现记录参数:', params);\n                console.log('原始筛选条件:', {\n                  dateRange: this.withdrawFilter.dateRange,\n                  completeDateRange: this.withdrawFilter.completeDateRange\n                });\n                _context15.next = 8;\n                return this.$http.get('/api/usercenter/withdrawalHistory', {\n                  params: params\n                });\n\n              case 8:\n                response = _context15.sent;\n\n                if (response && response.success) {\n                  result = response.result || {};\n                  records = result.records || []; // 更新分页信息\n\n                  if (this.withdrawPagination) {\n                    this.withdrawPagination.total = result.total || 0;\n                  } // 转换数据格式\n\n\n                  this.withdrawRecords = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      id: item.id,\n                      amount: item.withdrawal_amount || '0.00',\n                      method: item.withdrawalMethod || '支付宝',\n                      applyTime: item.apply_time || '',\n                      status: _this4.getWithdrawStatusText(item.status, item.review_remark),\n                      rawStatus: item.status,\n                      completeTime: item.review_time || '-'\n                    };\n                  });\n                } else {\n                  this.withdrawRecords = [];\n\n                  if (this.withdrawPagination) {\n                    this.withdrawPagination.total = 0;\n                  }\n                }\n\n                _context15.next = 17;\n                break;\n\n              case 12:\n                _context15.prev = 12;\n                _context15.t0 = _context15[\"catch\"](0);\n                console.error('获取提现记录失败:', _context15.t0);\n                this.withdrawRecords = []; // 如果是网络错误或其他错误，显示友好提示\n\n                if (_context15.t0.response && _context15.t0.response.status === 401) {\n                  this.$message.warning('登录已过期，请重新登录');\n                }\n\n              case 17:\n                _context15.prev = 17;\n                this.recordsLoading = false;\n                return _context15.finish(17);\n\n              case 20:\n              case \"end\":\n                return _context15.stop();\n            }\n          }\n        }, _callee15, this, [[0, 12, 17, 20]]);\n      }));\n\n      function loadWithdrawRecords() {\n        return _loadWithdrawRecords.apply(this, arguments);\n      }\n\n      return loadWithdrawRecords;\n    }(),\n    // 获取提现状态文本\n    getWithdrawStatusText: function getWithdrawStatusText(status, reviewRemark) {\n      var statusMap = {\n        0: '待审核',\n        1: '待审核',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已取消'\n      };\n      var statusText = statusMap[status] || '未知状态'; // 如果是已拒绝状态且有拒绝原因，则添加原因\n\n      if (status === 3 && reviewRemark) {\n        statusText += \"\\uFF08\".concat(reviewRemark, \"\\uFF09\");\n      }\n\n      return statusText;\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(statusText) {\n      // 处理带拒绝原因的状态文本\n      if (statusText.includes('已拒绝')) {\n        return 'red';\n      }\n\n      var colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '待审核': 'orange',\n        '已取消': 'gray'\n      };\n      return colorMap[statusText] || 'default';\n    },\n    // 获取排序状态（暂时保留，可能后续需要）\n    getSortOrder: function getSortOrder(field) {\n      if (this.usersSort && this.usersSort.orderBy === field) {\n        return this.usersSort.order === 'asc' ? 'ascend' : 'descend';\n      }\n\n      return null;\n    },\n    // 处理邀请用户表格分页变化\n    handleUsersTableChange: function handleUsersTableChange(pagination, _filters, sorter) {\n      console.log('表格变化:', {\n        pagination: pagination,\n        sorter: sorter\n      });\n\n      if (this.usersPagination && pagination) {\n        this.usersPagination.current = pagination.current || 1;\n        this.usersPagination.pageSize = pagination.pageSize || 10;\n      } // 处理排序\n\n\n      if (sorter && sorter.field && this.usersSort) {\n        var fieldMap = {\n          'nickname': 'nickname',\n          'registerTime': 'register_time',\n          'reward': 'total_reward'\n        };\n        var newOrderBy = fieldMap[sorter.field] || 'total_reward'; // 如果点击的是同一个字段，切换排序方向\n\n        if (this.usersSort.orderBy === newOrderBy) {\n          this.usersSort.order = this.usersSort.order === 'asc' ? 'desc' : 'asc';\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.usersSort.orderBy = newOrderBy;\n\n          if (newOrderBy === 'total_reward') {\n            this.usersSort.order = 'desc'; // 奖励金额默认降序\n          } else {\n            this.usersSort.order = 'asc'; // 其他字段默认升序\n          }\n        }\n\n        console.log('排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.usersSort.orderBy,\n          finalOrder: this.usersSort.order,\n          clickedSameField: this.usersSort.orderBy === newOrderBy\n        }); // 排序时回到第一页\n\n        if (this.usersPagination) {\n          this.usersPagination.current = 1;\n        }\n      }\n\n      this.loadReferralUsers();\n    },\n    // 处理提现记录表格分页变化\n    handleWithdrawTableChange: function handleWithdrawTableChange(pagination, _filters, sorter) {\n      console.log('提现记录表格变化:', {\n        pagination: pagination,\n        sorter: sorter\n      });\n\n      if (this.withdrawPagination && pagination) {\n        this.withdrawPagination.current = pagination.current || 1;\n        this.withdrawPagination.pageSize = pagination.pageSize || 10;\n      } // 处理排序\n\n\n      if (sorter && sorter.field && this.withdrawSort) {\n        var fieldMap = {\n          'amount': 'withdrawal_amount',\n          'applyTime': 'apply_time',\n          'status': 'status',\n          'completeTime': 'review_time'\n        };\n        var newOrderBy = fieldMap[sorter.field] || 'apply_time'; // 如果点击的是同一个字段，切换排序方向\n\n        if (this.withdrawSort.orderBy === newOrderBy) {\n          this.withdrawSort.order = this.withdrawSort.order === 'asc' ? 'desc' : 'asc';\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.withdrawSort.orderBy = newOrderBy;\n\n          if (newOrderBy === 'apply_time') {\n            this.withdrawSort.order = 'desc'; // 申请时间默认降序\n          } else if (newOrderBy === 'withdrawal_amount') {\n            this.withdrawSort.order = 'desc'; // 金额默认降序\n          } else {\n            this.withdrawSort.order = 'asc'; // 其他字段默认升序\n          }\n        }\n\n        console.log('提现记录排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.withdrawSort.orderBy,\n          finalOrder: this.withdrawSort.order\n        }); // 排序时回到第一页\n\n        if (this.withdrawPagination) {\n          this.withdrawPagination.current = 1;\n        }\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 处理提现记录筛选\n    handleWithdrawFilter: function handleWithdrawFilter() {\n      // 筛选时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1;\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 重置提现记录筛选\n    handleWithdrawReset: function handleWithdrawReset() {\n      this.withdrawFilter = {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        dateRange: [],\n        completeDateRange: []\n      }; // 重置时回到第一页\n\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1;\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 处理取消提现\n    handleCancelWithdraw: function handleCancelWithdraw(record) {\n      var _this5 = this;\n\n      var h = this.$createElement;\n      this.$confirm({\n        title: '确认取消提现',\n        content: h('div', {\n          style: {\n            margin: '16px 0'\n          }\n        }, [h('p', {\n          style: {\n            marginBottom: '8px'\n          }\n        }, [h('strong', '提现金额：'), \"\\xA5\".concat(record.amount)]), h('p', {\n          style: {\n            marginBottom: '8px'\n          }\n        }, [h('strong', '申请时间：'), record.applyTime]), h('p', {\n          style: {\n            color: '#ff4d4f',\n            marginTop: '12px',\n            marginBottom: '0'\n          }\n        }, [h('strong', '注意：'), '取消后金额将返还到可提现余额，此操作不可撤销！'])]),\n        okText: '确认取消',\n        okType: 'danger',\n        cancelText: '返回',\n        centered: true,\n        width: 400,\n        onOk: function () {\n          var _onOk2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee16() {\n            return _regeneratorRuntime.wrap(function _callee16$(_context16) {\n              while (1) {\n                switch (_context16.prev = _context16.next) {\n                  case 0:\n                    _context16.next = 2;\n                    return _this5.confirmCancelWithdraw(record);\n\n                  case 2:\n                  case \"end\":\n                    return _context16.stop();\n                }\n              }\n            }, _callee16);\n          }));\n\n          function onOk() {\n            return _onOk2.apply(this, arguments);\n          }\n\n          return onOk;\n        }()\n      });\n    },\n    // 确认取消提现\n    confirmCancelWithdraw: function () {\n      var _confirmCancelWithdraw = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee17(record) {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee17$(_context17) {\n          while (1) {\n            switch (_context17.prev = _context17.next) {\n              case 0:\n                this.cancelLoading = true;\n                _context17.prev = 1;\n                params = {\n                  withdrawalId: record.id\n                };\n                _context17.next = 5;\n                return this.$http.post('/api/usercenter/cancelWithdrawal', params);\n\n              case 5:\n                response = _context17.sent;\n\n                if (!response.success) {\n                  _context17.next = 12;\n                  break;\n                }\n\n                this.$notification.success({\n                  message: '取消成功',\n                  description: '提现申请已取消，金额已返还到可提现余额',\n                  placement: 'topRight'\n                }); // 刷新数据\n\n                _context17.next = 10;\n                return Promise.all([this.loadReferralData(), this.loadWithdrawRecords()]);\n\n              case 10:\n                _context17.next = 13;\n                break;\n\n              case 12:\n                this.$notification.error({\n                  message: '取消失败',\n                  description: response.message || '取消提现失败，请重试',\n                  placement: 'topRight'\n                });\n\n              case 13:\n                _context17.next = 19;\n                break;\n\n              case 15:\n                _context17.prev = 15;\n                _context17.t0 = _context17[\"catch\"](1);\n                console.error('取消提现失败:', _context17.t0);\n\n                if (_context17.t0.response && _context17.t0.response.data && _context17.t0.response.data.message) {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: _context17.t0.response.data.message,\n                    placement: 'topRight'\n                  });\n                } else if (_context17.t0.message) {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: _context17.t0.message,\n                    placement: 'topRight'\n                  });\n                } else {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: '网络错误，请稍后重试',\n                    placement: 'topRight'\n                  });\n                }\n\n              case 19:\n                _context17.prev = 19;\n                this.cancelLoading = false;\n                return _context17.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context17.stop();\n            }\n          }\n        }, _callee17, this, [[1, 15, 19, 22]]);\n      }));\n\n      function confirmCancelWithdraw(_x4) {\n        return _confirmCancelWithdraw.apply(this, arguments);\n      }\n\n      return confirmCancelWithdraw;\n    }(),\n    // 格式化数字显示\n    formatNumber: function formatNumber(num) {\n      if (num === null || num === undefined) return '0';\n      var number = parseFloat(num);\n      if (isNaN(number)) return '0'; // 如果是金额，保留两位小数\n\n      if (num === this.totalEarnings) {\n        return number.toLocaleString('zh-CN', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        });\n      } // 其他数字不保留小数\n\n\n      return number.toLocaleString('zh-CN');\n    },\n    // 获取角色显示名称\n    getRoleDisplayName: function getRoleDisplayName(roleCode) {\n      switch (roleCode) {\n        case 'VIP':\n          return 'VIP用户';\n\n        case 'SVIP':\n          return 'SVIP用户';\n\n        case 'user':\n        default:\n          return '普通用户';\n      }\n    },\n    // 获取邀请人数要求文本\n    getRequirementText: function getRequirementText(config) {\n      var minReferrals = config.min_referrals;\n      var roleCode = config.role_code; // 查找同角色的下一个等级\n\n      var sameRoleConfigs = this.allLevelConfigs.filter(function (c) {\n        return c.role_code === roleCode;\n      });\n      var currentIndex = sameRoleConfigs.findIndex(function (c) {\n        return c.id === config.id;\n      });\n      var nextConfig = sameRoleConfigs[currentIndex + 1];\n\n      if (roleCode === 'SVIP') {\n        return '无要求';\n      }\n\n      if (nextConfig) {\n        if (minReferrals === 0) {\n          return \"0-\".concat(nextConfig.min_referrals - 1, \"\\u4EBA\");\n        } else {\n          return \"\".concat(minReferrals, \"-\").concat(nextConfig.min_referrals - 1, \"\\u4EBA\");\n        }\n      } else {\n        return \"\".concat(minReferrals, \"\\u4EBA\\u4EE5\\u4E0A\");\n      }\n    }\n  }\n};", null]}