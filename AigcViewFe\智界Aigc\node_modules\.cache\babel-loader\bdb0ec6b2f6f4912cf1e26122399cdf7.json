{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\affiliate\\Affiliate.vue", "mtime": 1753720109386}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport { getReferralStats, generateReferralLink, getUserRole, getLevelConfig } from '@/api/usercenter';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport Vue from 'vue';\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage: WebsitePage\n  },\n  data: function data() {\n    return {\n      loading: true,\n      qrLoading: false,\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n      // 邀请链接\n      affiliateLink: '',\n      // 佣金等级\n      userRole: 'user',\n      // user, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手邀请员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级邀请员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n      // 佣金等级配置\n      commissionLevels: [],\n      allLevelConfigs: [],\n      // 从数据库获取的完整等级配置\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false,\n      // 是否已预生成二维码\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n      // 邀请用户列表\n      referralUsers: [],\n      usersLoading: false,\n      usersPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total, range) {\n          return \"\\u7B2C \".concat(range[0], \"-\").concat(range[1], \" \\u6761\\uFF0C\\u5171 \").concat(total, \" \\u6761\");\n        }\n      },\n      // 邀请用户排序\n      usersSort: {\n        orderBy: 'total_reward',\n        order: 'desc'\n      },\n      defaultAvatar: '/default-avatar.png',\n      // 本地降级头像\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      cancelLoading: false,\n      withdrawPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: function showTotal(total, range) {\n          return \"\\u7B2C \".concat(range[0], \"-\").concat(range[1], \" \\u6761\\uFF0C\\u5171 \").concat(total, \" \\u6761\");\n        }\n      },\n      // 提现记录排序\n      withdrawSort: {\n        orderBy: 'apply_time',\n        order: 'desc'\n      },\n      // 提现记录筛选\n      withdrawFilter: {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        // null表示\"全部\"\n        dateRange: [],\n        completeDateRange: []\n      },\n      // 用户信息\n      userInfo: null\n    };\n  },\n  computed: {\n    // 动态计算表格列配置，确保排序状态响应式更新\n    userColumns: function userColumns() {\n      return [{\n        title: '头像',\n        dataIndex: 'avatar',\n        key: 'avatar',\n        scopedSlots: {\n          customRender: 'avatar'\n        },\n        width: 80\n      }, {\n        title: '用户昵称',\n        dataIndex: 'nickname',\n        key: 'nickname',\n        sorter: true\n      }, {\n        title: '注册时间',\n        dataIndex: 'registerTime',\n        key: 'registerTime',\n        sorter: true\n      }, {\n        title: '获得奖励',\n        dataIndex: 'reward',\n        key: 'reward',\n        scopedSlots: {\n          customRender: 'reward'\n        },\n        sorter: true\n      }];\n    },\n    // 动态计算提现记录表格列配置，确保排序状态响应式更新\n    withdrawColumns: function withdrawColumns() {\n      return [{\n        title: '提现金额',\n        dataIndex: 'amount',\n        key: 'amount',\n        scopedSlots: {\n          customRender: 'amount'\n        },\n        sorter: true\n      }, {\n        title: '提现方式',\n        dataIndex: 'method',\n        key: 'method'\n      }, {\n        title: '申请时间',\n        dataIndex: 'applyTime',\n        key: 'applyTime',\n        sorter: true\n      }, {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        scopedSlots: {\n          customRender: 'status'\n        },\n        sorter: true\n      }, {\n        title: '完成时间',\n        dataIndex: 'completeTime',\n        key: 'completeTime',\n        sorter: true\n      }, {\n        title: '操作',\n        key: 'action',\n        width: 100,\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }];\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.checkLoginAndLoadData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  methods: {\n    // 获取头像URL（处理CDN路径和默认头像）\n    getAvatarUrl: function getAvatarUrl(avatar) {\n      if (!avatar) {\n        return this.defaultAvatar;\n      } // 如果是完整的URL，直接返回\n\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是相对路径，使用getFileAccessHttpUrl转换\n\n\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar;\n    },\n    // 处理文件访问URL（和其他组件保持一致）\n    getFileAccessHttpUrl: function getFileAccessHttpUrl(avatar) {\n      if (!avatar) return this.defaultAvatar; // 如果已经是完整URL，直接返回\n\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar;\n      } // 如果是TOS文件，使用全局方法\n\n\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar;\n      } // 本地文件，使用静态域名\n\n\n      var staticDomain = this.$store.state.app.staticDomainURL;\n      return staticDomain ? \"\".concat(staticDomain, \"/\").concat(avatar) : avatar;\n    },\n    // 加载TOS默认头像URL\n    loadDefaultAvatar: function () {\n      var _loadDefaultAvatar = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return this.$http.get('/sys/common/default-avatar-url');\n\n              case 3:\n                response = _context2.sent;\n\n                if (response && response.success && response.result) {\n                  this.defaultAvatar = response.result;\n                  console.log('🎯 Affiliate: 已加载TOS默认头像:', this.defaultAvatar);\n                }\n\n                _context2.next = 10;\n                break;\n\n              case 7:\n                _context2.prev = 7;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.warn('⚠️ Affiliate: 获取TOS默认头像失败，使用本地降级:', _context2.t0); // 保持本地默认头像作为降级方案\n\n              case 10:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 7]]);\n      }));\n\n      function loadDefaultAvatar() {\n        return _loadDefaultAvatar.apply(this, arguments);\n      }\n\n      return loadDefaultAvatar;\n    }(),\n    // 检查登录状态并加载数据\n    checkLoginAndLoadData: function () {\n      var _checkLoginAndLoadData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var token;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context3.next = 4;\n                  break;\n                }\n\n                this.$router.push({\n                  path: '/login',\n                  query: {\n                    redirect: this.$route.fullPath\n                  }\n                });\n                return _context3.abrupt(\"return\");\n\n              case 4:\n                _context3.prev = 4;\n                _context3.next = 7;\n                return Promise.all([this.loadReferralData(), this.loadReferralLink(), this.loadUserRole(), this.loadLevelConfig(), this.loadReferralUsers(), this.loadWithdrawRecords(), this.loadDefaultAvatar()]);\n\n              case 7:\n                // 计算佣金等级\n                this.calculateCommissionLevel(); // 自动预生成邀请二维码\n\n                this.preGenerateQRCode();\n                _context3.next = 15;\n                break;\n\n              case 11:\n                _context3.prev = 11;\n                _context3.t0 = _context3[\"catch\"](4);\n                console.error('加载分销数据失败:', _context3.t0);\n                this.$notification.error({\n                  message: '加载失败',\n                  description: '获取分销数据失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 15:\n                _context3.prev = 15;\n                this.loading = false;\n                return _context3.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[4, 11, 15, 18]]);\n      }));\n\n      function checkLoginAndLoadData() {\n        return _checkLoginAndLoadData.apply(this, arguments);\n      }\n\n      return checkLoginAndLoadData;\n    }(),\n    // 加载推荐统计数据\n    loadReferralData: function () {\n      var _loadReferralData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var response, data;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return getReferralStats();\n\n              case 3:\n                response = _context4.sent;\n\n                if (response.success) {\n                  data = response.result;\n                  this.totalEarnings = data.total_reward_amount || 0;\n                  this.availableEarnings = data.available_rewards || 0;\n                  this.totalReferrals = data.total_referrals || 0;\n                  this.memberReferrals = data.member_referrals || 0;\n                }\n\n                _context4.next = 11;\n                break;\n\n              case 7:\n                _context4.prev = 7;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('获取推荐统计失败:', _context4.t0);\n                throw _context4.t0;\n\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 7]]);\n      }));\n\n      function loadReferralData() {\n        return _loadReferralData.apply(this, arguments);\n      }\n\n      return loadReferralData;\n    }(),\n    // 加载推荐链接\n    loadReferralLink: function () {\n      var _loadReferralLink = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                _context5.next = 3;\n                return generateReferralLink({});\n\n              case 3:\n                response = _context5.sent;\n\n                if (response.success) {\n                  this.affiliateLink = response.result || '';\n                }\n\n                _context5.next = 11;\n                break;\n\n              case 7:\n                _context5.prev = 7;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('获取推荐链接失败:', _context5.t0); // 如果获取失败，使用默认链接格式\n\n                this.affiliateLink = \"\".concat(window.location.origin, \"?ref=loading...\");\n\n              case 11:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 7]]);\n      }));\n\n      function loadReferralLink() {\n        return _loadReferralLink.apply(this, arguments);\n      }\n\n      return loadReferralLink;\n    }(),\n    // 加载用户角色信息\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                _context6.next = 3;\n                return getUserRole();\n\n              case 3:\n                response = _context6.sent;\n\n                if (response.success) {\n                  this.userRole = response.result.role_code || 'user';\n                }\n\n                _context6.next = 11;\n                break;\n\n              case 7:\n                _context6.prev = 7;\n                _context6.t0 = _context6[\"catch\"](0);\n                console.error('获取用户角色失败:', _context6.t0);\n                this.userRole = 'user';\n\n              case 11:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 7]]);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    // 加载等级配置信息\n    loadLevelConfig: function () {\n      var _loadLevelConfig = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                _context7.prev = 0;\n                _context7.next = 3;\n                return getLevelConfig();\n\n              case 3:\n                response = _context7.sent;\n\n                if (response.success) {\n                  this.allLevelConfigs = response.result || [];\n                }\n\n                _context7.next = 11;\n                break;\n\n              case 7:\n                _context7.prev = 7;\n                _context7.t0 = _context7[\"catch\"](0);\n                console.error('获取等级配置失败:', _context7.t0);\n                this.allLevelConfigs = [];\n\n              case 11:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[0, 7]]);\n      }));\n\n      function loadLevelConfig() {\n        return _loadLevelConfig.apply(this, arguments);\n      }\n\n      return loadLevelConfig;\n    }(),\n    // 计算佣金等级和进度\n    calculateCommissionLevel: function calculateCommissionLevel() {\n      var _this = this;\n\n      var memberCount = this.memberReferrals; // 从数据库配置中获取当前用户角色的等级配置\n\n      var userLevelConfigs = this.allLevelConfigs.filter(function (config) {\n        return config.role_code === _this.userRole;\n      });\n\n      if (userLevelConfigs.length === 0) {\n        console.warn('未找到用户角色的等级配置:', this.userRole);\n        return;\n      } // 根据邀请人数确定当前等级\n\n\n      var currentLevel = null;\n\n      for (var i = userLevelConfigs.length - 1; i >= 0; i--) {\n        if (memberCount >= userLevelConfigs[i].min_referrals) {\n          currentLevel = userLevelConfigs[i];\n          break;\n        }\n      }\n\n      if (!currentLevel) {\n        currentLevel = userLevelConfigs[0]; // 默认最低等级\n      } // 设置当前等级信息\n\n\n      this.currentCommissionRate = parseFloat(currentLevel.commission_rate);\n      this.commissionLevelText = currentLevel.level_name; // 查找下一个等级\n\n      var nextLevel = userLevelConfigs.find(function (config) {\n        return config.min_referrals > memberCount;\n      });\n\n      if (nextLevel) {\n        this.nextLevelRequirement = nextLevel.min_referrals;\n        this.nextLevelText = nextLevel.level_name;\n        this.nextLevelRate = parseFloat(nextLevel.commission_rate);\n        this.levelProgress = memberCount / nextLevel.min_referrals * 100;\n        this.progressColor = '#1890ff';\n      } else {\n        // 已达最高等级\n        this.nextLevelRequirement = 0;\n        this.nextLevelText = '已达最高等级';\n        this.nextLevelRate = this.currentCommissionRate;\n        this.levelProgress = 100;\n        this.progressColor = '#722ed1';\n      } // 生成等级进度显示数据\n\n\n      this.commissionLevels = userLevelConfigs.map(function (config, index) {\n        var isCompleted = memberCount >= config.min_referrals; // 判断当前等级：如果不是已完成，且满足前一个等级的要求，则为当前等级\n\n        var isCurrent = false;\n\n        if (!isCompleted) {\n          if (index === 0) {\n            // 第一个等级，如果没完成就是当前等级\n            isCurrent = true;\n          } else {\n            // 其他等级，如果满足前一个等级要求但不满足当前等级要求，则为当前等级\n            var prevRequirement = userLevelConfigs[index - 1].min_referrals;\n            isCurrent = memberCount >= prevRequirement;\n          }\n        }\n\n        var isUpcoming = !isCompleted && !isCurrent;\n        var remaining = 0;\n\n        if (!isCompleted) {\n          remaining = config.min_referrals - memberCount;\n        }\n\n        return {\n          name: config.level_name,\n          rate: parseFloat(config.commission_rate),\n          requirement: config.min_referrals,\n          isCompleted: isCompleted,\n          isCurrent: isCurrent,\n          isUpcoming: isUpcoming,\n          remaining: remaining > 0 ? remaining : 0\n        };\n      });\n    },\n    // 复制邀请链接\n    copyLink: function copyLink() {\n      var _this2 = this;\n\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '邀请链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(function () {\n        _this2.$notification.success({\n          message: '邀请链接已复制',\n          description: '邀请链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        });\n      }).catch(function () {\n        _this2.$notification.error({\n          message: '复制失败',\n          description: '复制邀请链接失败，请手动复制',\n          placement: 'topRight'\n        });\n      });\n    },\n    // 预生成邀请二维码（后台静默生成）\n    preGenerateQRCode: function () {\n      var _preGenerateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                if (!(!this.affiliateLink || this.qrPreGenerated)) {\n                  _context8.next = 2;\n                  break;\n                }\n\n                return _context8.abrupt(\"return\");\n\n              case 2:\n                _context8.prev = 2;\n                console.log('开始预生成邀请二维码...'); // 调用后端API生成二维码并上传到TOS\n\n                _context8.next = 6;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 6:\n                response = _context8.sent;\n\n                if (response && response.success) {\n                  // 静默保存二维码URL\n                  this.qrCodeUrl = response.result;\n                  this.qrPreGenerated = true;\n                  console.log('邀请二维码预生成成功:', this.qrCodeUrl);\n                }\n\n                _context8.next = 13;\n                break;\n\n              case 10:\n                _context8.prev = 10;\n                _context8.t0 = _context8[\"catch\"](2);\n                console.error('预生成二维码失败:', _context8.t0); // 预生成失败不显示错误提示，用户点击时再重试\n\n              case 13:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[2, 10]]);\n      }));\n\n      function preGenerateQRCode() {\n        return _preGenerateQRCode.apply(this, arguments);\n      }\n\n      return preGenerateQRCode;\n    }(),\n    // 生成邀请二维码（用户主动点击）\n    generateQRCode: function () {\n      var _generateQRCode = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        var response, errorMsg;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                if (this.affiliateLink) {\n                  _context9.next = 3;\n                  break;\n                }\n\n                this.$notification.warning({\n                  message: '邀请链接未生成',\n                  description: '请等待邀请链接生成完成后再生成二维码',\n                  placement: 'topRight'\n                });\n                return _context9.abrupt(\"return\");\n\n              case 3:\n                if (!(this.qrPreGenerated && this.qrCodeUrl)) {\n                  _context9.next = 6;\n                  break;\n                }\n\n                this.showQRModal = true;\n                return _context9.abrupt(\"return\");\n\n              case 6:\n                _context9.prev = 6;\n                this.qrLoading = true; // 调用后端API生成二维码并上传到TOS\n\n                _context9.next = 10;\n                return this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n                  params: {\n                    url: this.affiliateLink\n                  }\n                });\n\n              case 10:\n                response = _context9.sent;\n                console.log('二维码生成响应:', response);\n\n                if (!(response && response.success)) {\n                  _context9.next = 19;\n                  break;\n                }\n\n                // 使用CDN地址\n                this.qrCodeUrl = response.result;\n                this.qrPreGenerated = true;\n                this.showQRModal = true;\n                this.$notification.success({\n                  message: '二维码生成成功',\n                  description: '邀请二维码已生成并存储到CDN，可以下载保存',\n                  placement: 'topRight'\n                });\n                _context9.next = 21;\n                break;\n\n              case 19:\n                errorMsg = response && response.message || '生成失败';\n                throw new Error(errorMsg);\n\n              case 21:\n                _context9.next = 27;\n                break;\n\n              case 23:\n                _context9.prev = 23;\n                _context9.t0 = _context9[\"catch\"](6);\n                console.error('生成二维码失败:', _context9.t0);\n                this.$notification.error({\n                  message: '生成失败',\n                  description: _context9.t0.message || '二维码生成失败，请稍后重试',\n                  placement: 'topRight'\n                });\n\n              case 27:\n                _context9.prev = 27;\n                this.qrLoading = false;\n                return _context9.finish(27);\n\n              case 30:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this, [[6, 23, 27, 30]]);\n      }));\n\n      function generateQRCode() {\n        return _generateQRCode.apply(this, arguments);\n      }\n\n      return generateQRCode;\n    }(),\n    // 下载二维码\n    downloadQRCode: function downloadQRCode() {\n      if (!this.qrCodeUrl) return;\n\n      try {\n        // 从邀请链接中提取邀请码\n        var referralCode = this.extractReferralCode(this.affiliateLink); // 通过后端代理下载，避免CORS问题\n\n        var backendUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/jeecg-boot';\n        var downloadUrl = \"\".concat(backendUrl, \"/api/usercenter/downloadReferralQRCode?url=\").concat(encodeURIComponent(this.qrCodeUrl), \"&code=\").concat(referralCode, \"&t=\").concat(Date.now());\n        console.log('下载URL:', downloadUrl); // 使用隐藏iframe下载，避免页面跳动\n\n        var iframe = document.createElement('iframe');\n        iframe.style.display = 'none';\n        iframe.style.position = 'absolute';\n        iframe.style.left = '-9999px';\n        iframe.src = downloadUrl;\n        document.body.appendChild(iframe); // 3秒后移除iframe\n\n        setTimeout(function () {\n          if (iframe.parentNode) {\n            document.body.removeChild(iframe);\n          }\n        }, 3000);\n        this.$notification.success({\n          message: '下载开始',\n          description: \"\\u9080\\u8BF7\\u4E8C\\u7EF4\\u7801_\".concat(referralCode, \".png \\u6B63\\u5728\\u4E0B\\u8F7D\"),\n          placement: 'topRight'\n        });\n      } catch (error) {\n        console.error('下载二维码失败:', error);\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        });\n      }\n    },\n    // 从邀请链接中提取邀请码\n    extractReferralCode: function extractReferralCode(url) {\n      if (!url) return 'UNKNOWN';\n\n      try {\n        var urlObj = new URL(url);\n        var refParam = urlObj.searchParams.get('ref');\n        return refParam || 'UNKNOWN';\n      } catch (error) {\n        console.error('提取邀请码失败:', error);\n        return 'UNKNOWN';\n      }\n    },\n    // 显示提现弹窗\n    openWithdrawModal: function openWithdrawModal() {\n      if (this.availableEarnings < 50) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为50元，请继续邀请获得更多收益',\n          placement: 'topRight'\n        });\n        return;\n      }\n\n      this.showWithdrawModal = true;\n    },\n    // 处理提现申请\n    handleWithdraw: function () {\n      var _handleWithdraw = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n          while (1) {\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                this.withdrawForm.validateFields( /*#__PURE__*/function () {\n                  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11(err, values) {\n                    var h;\n                    return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n                      while (1) {\n                        switch (_context11.prev = _context11.next) {\n                          case 0:\n                            if (!err) {\n                              _context11.next = 2;\n                              break;\n                            }\n\n                            return _context11.abrupt(\"return\");\n\n                          case 2:\n                            // 二次确认弹窗\n                            h = _this3.$createElement;\n\n                            _this3.$confirm({\n                              title: '确认提现申请',\n                              content: h('div', {\n                                style: {\n                                  margin: '16px 0'\n                                }\n                              }, [h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '提现金额：'), \"\\xA5\".concat(values.amount)]), h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '支付宝账号：'), values.alipayAccount]), h('p', {\n                                style: {\n                                  marginBottom: '8px'\n                                }\n                              }, [h('strong', '收款人姓名：'), values.realName]), h('p', {\n                                style: {\n                                  color: '#ff4d4f',\n                                  marginTop: '12px',\n                                  marginBottom: '0'\n                                }\n                              }, [h('strong', '注意：'), '请再次核实一遍提现信息，请确认信息无误！'])]),\n                              okText: '确认提现',\n                              cancelText: '取消',\n                              centered: true,\n                              width: 400,\n                              onOk: function () {\n                                var _onOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n                                  return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n                                    while (1) {\n                                      switch (_context10.prev = _context10.next) {\n                                        case 0:\n                                          _context10.next = 2;\n                                          return _this3.submitWithdrawRequest(values);\n\n                                        case 2:\n                                        case \"end\":\n                                          return _context10.stop();\n                                      }\n                                    }\n                                  }, _callee10);\n                                }));\n\n                                function onOk() {\n                                  return _onOk.apply(this, arguments);\n                                }\n\n                                return onOk;\n                              }()\n                            });\n\n                          case 4:\n                          case \"end\":\n                            return _context11.stop();\n                        }\n                      }\n                    }, _callee11);\n                  }));\n\n                  return function (_x, _x2) {\n                    return _ref.apply(this, arguments);\n                  };\n                }());\n\n              case 1:\n              case \"end\":\n                return _context12.stop();\n            }\n          }\n        }, _callee12, this);\n      }));\n\n      function handleWithdraw() {\n        return _handleWithdraw.apply(this, arguments);\n      }\n\n      return handleWithdraw;\n    }(),\n    // 提交提现申请\n    submitWithdrawRequest: function () {\n      var _submitWithdrawRequest = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee13(values) {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee13$(_context13) {\n          while (1) {\n            switch (_context13.prev = _context13.next) {\n              case 0:\n                this.withdrawLoading = true;\n                _context13.prev = 1;\n                params = {\n                  withdrawalAmount: values.amount,\n                  realName: values.realName,\n                  alipayAccount: values.alipayAccount\n                };\n                _context13.next = 5;\n                return this.$http.post('/api/usercenter/applyWithdrawal', params);\n\n              case 5:\n                response = _context13.sent;\n\n                if (!response.success) {\n                  _context13.next = 15;\n                  break;\n                }\n\n                this.withdrawLoading = false;\n                this.showWithdrawModal = false;\n                this.withdrawForm.resetFields();\n                this.$notification.success({\n                  message: '提现申请成功',\n                  description: '您的提现申请已提交，预计1-3个工作日到账',\n                  placement: 'topRight'\n                }); // 刷新数据\n\n                _context13.next = 13;\n                return Promise.all([this.loadReferralData(), this.loadWithdrawRecords()]);\n\n              case 13:\n                _context13.next = 17;\n                break;\n\n              case 15:\n                this.withdrawLoading = false;\n                this.$notification.error({\n                  message: '提现申请失败',\n                  description: response.message || '申请失败，请重试',\n                  placement: 'topRight'\n                });\n\n              case 17:\n                _context13.next = 24;\n                break;\n\n              case 19:\n                _context13.prev = 19;\n                _context13.t0 = _context13[\"catch\"](1);\n                this.withdrawLoading = false;\n                console.error('提现申请失败:', _context13.t0); // 检查是否是HTTP响应错误，如果是则显示后端返回的错误信息\n\n                if (_context13.t0.response && _context13.t0.response.data && _context13.t0.response.data.message) {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: _context13.t0.response.data.message,\n                    placement: 'topRight'\n                  });\n                } else if (_context13.t0.message) {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: _context13.t0.message,\n                    placement: 'topRight'\n                  });\n                } else {\n                  this.$notification.error({\n                    message: '提现申请失败',\n                    description: '网络错误，请稍后重试',\n                    placement: 'topRight'\n                  });\n                }\n\n              case 24:\n              case \"end\":\n                return _context13.stop();\n            }\n          }\n        }, _callee13, this, [[1, 19]]);\n      }));\n\n      function submitWithdrawRequest(_x3) {\n        return _submitWithdrawRequest.apply(this, arguments);\n      }\n\n      return submitWithdrawRequest;\n    }(),\n    // 加载邀请用户列表\n    loadReferralUsers: function () {\n      var _loadReferralUsers = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee14() {\n        var params, response, result, records;\n        return _regeneratorRuntime.wrap(function _callee14$(_context14) {\n          while (1) {\n            switch (_context14.prev = _context14.next) {\n              case 0:\n                _context14.prev = 0;\n                this.usersLoading = true;\n                params = {\n                  current: this.usersPagination && this.usersPagination.current || 1,\n                  size: this.usersPagination && this.usersPagination.pageSize || 10,\n                  orderBy: this.usersSort && this.usersSort.orderBy || 'total_reward',\n                  order: this.usersSort && this.usersSort.order || 'desc'\n                };\n                console.log('加载邀请用户参数:', params);\n                _context14.next = 6;\n                return this.$http.get('/api/usercenter/referralList', {\n                  params: params\n                });\n\n              case 6:\n                response = _context14.sent;\n\n                if (response && response.success) {\n                  result = response.result || {};\n                  records = result.records || []; // 更新分页信息\n\n                  if (this.usersPagination) {\n                    this.usersPagination.total = result.total || 0;\n                  } // 转换数据格式\n\n\n                  this.referralUsers = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      nickname: item.referee_nickname || \"\\u7528\\u6237***\".concat(index + 1),\n                      avatar: item.referee_avatar || '',\n                      registerTime: item.register_time || '',\n                      reward: item.total_reward || '0.00'\n                    };\n                  });\n                } else {\n                  this.referralUsers = [];\n\n                  if (this.usersPagination) {\n                    this.usersPagination.total = 0;\n                  }\n                }\n\n                _context14.next = 15;\n                break;\n\n              case 10:\n                _context14.prev = 10;\n                _context14.t0 = _context14[\"catch\"](0);\n                console.error('获取邀请用户列表失败:', _context14.t0);\n                this.referralUsers = []; // 如果是网络错误或其他错误，显示友好提示\n\n                if (_context14.t0.response && _context14.t0.response.status === 401) {\n                  this.$message.warning('登录已过期，请重新登录');\n                }\n\n              case 15:\n                _context14.prev = 15;\n                this.usersLoading = false;\n                return _context14.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context14.stop();\n            }\n          }\n        }, _callee14, this, [[0, 10, 15, 18]]);\n      }));\n\n      function loadReferralUsers() {\n        return _loadReferralUsers.apply(this, arguments);\n      }\n\n      return loadReferralUsers;\n    }(),\n    // 加载提现记录\n    loadWithdrawRecords: function () {\n      var _loadWithdrawRecords = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee15() {\n        var _this4 = this;\n\n        var params, response, result, records;\n        return _regeneratorRuntime.wrap(function _callee15$(_context15) {\n          while (1) {\n            switch (_context15.prev = _context15.next) {\n              case 0:\n                _context15.prev = 0;\n                this.recordsLoading = true;\n                params = {\n                  current: this.withdrawPagination && this.withdrawPagination.current || 1,\n                  size: this.withdrawPagination && this.withdrawPagination.pageSize || 10,\n                  orderBy: this.withdrawSort && this.withdrawSort.orderBy || 'apply_time',\n                  order: this.withdrawSort && this.withdrawSort.order || 'desc'\n                }; // 添加筛选参数\n\n                if (this.withdrawFilter) {\n                  if (this.withdrawFilter.minAmount !== null && this.withdrawFilter.minAmount !== '') {\n                    params.minAmount = this.withdrawFilter.minAmount;\n                  }\n\n                  if (this.withdrawFilter.maxAmount !== null && this.withdrawFilter.maxAmount !== '') {\n                    params.maxAmount = this.withdrawFilter.maxAmount;\n                  }\n\n                  if (this.withdrawFilter.status !== null) {\n                    params.status = this.withdrawFilter.status;\n                  }\n\n                  if (this.withdrawFilter.dateRange && this.withdrawFilter.dateRange.length === 2) {\n                    // 处理moment对象，转换为YYYY-MM-DD格式\n                    params.startDate = this.withdrawFilter.dateRange[0].format ? this.withdrawFilter.dateRange[0].format('YYYY-MM-DD') : this.withdrawFilter.dateRange[0];\n                    params.endDate = this.withdrawFilter.dateRange[1].format ? this.withdrawFilter.dateRange[1].format('YYYY-MM-DD') : this.withdrawFilter.dateRange[1];\n                  }\n\n                  if (this.withdrawFilter.completeDateRange && this.withdrawFilter.completeDateRange.length === 2) {\n                    // 处理moment对象，转换为YYYY-MM-DD格式\n                    params.completeStartDate = this.withdrawFilter.completeDateRange[0].format ? this.withdrawFilter.completeDateRange[0].format('YYYY-MM-DD') : this.withdrawFilter.completeDateRange[0];\n                    params.completeEndDate = this.withdrawFilter.completeDateRange[1].format ? this.withdrawFilter.completeDateRange[1].format('YYYY-MM-DD') : this.withdrawFilter.completeDateRange[1];\n                  }\n                }\n\n                console.log('加载提现记录参数:', params);\n                console.log('原始筛选条件:', {\n                  dateRange: this.withdrawFilter.dateRange,\n                  completeDateRange: this.withdrawFilter.completeDateRange\n                });\n                _context15.next = 8;\n                return this.$http.get('/api/usercenter/withdrawalHistory', {\n                  params: params\n                });\n\n              case 8:\n                response = _context15.sent;\n\n                if (response && response.success) {\n                  result = response.result || {};\n                  records = result.records || []; // 更新分页信息\n\n                  if (this.withdrawPagination) {\n                    this.withdrawPagination.total = result.total || 0;\n                  } // 转换数据格式\n\n\n                  this.withdrawRecords = records.map(function (item, index) {\n                    return {\n                      key: item.id || index,\n                      id: item.id,\n                      amount: item.withdrawal_amount || '0.00',\n                      method: item.withdrawalMethod || '支付宝',\n                      applyTime: item.apply_time || '',\n                      status: _this4.getWithdrawStatusText(item.status, item.review_remark),\n                      rawStatus: item.status,\n                      completeTime: item.review_time || '-'\n                    };\n                  });\n                } else {\n                  this.withdrawRecords = [];\n\n                  if (this.withdrawPagination) {\n                    this.withdrawPagination.total = 0;\n                  }\n                }\n\n                _context15.next = 17;\n                break;\n\n              case 12:\n                _context15.prev = 12;\n                _context15.t0 = _context15[\"catch\"](0);\n                console.error('获取提现记录失败:', _context15.t0);\n                this.withdrawRecords = []; // 如果是网络错误或其他错误，显示友好提示\n\n                if (_context15.t0.response && _context15.t0.response.status === 401) {\n                  this.$message.warning('登录已过期，请重新登录');\n                }\n\n              case 17:\n                _context15.prev = 17;\n                this.recordsLoading = false;\n                return _context15.finish(17);\n\n              case 20:\n              case \"end\":\n                return _context15.stop();\n            }\n          }\n        }, _callee15, this, [[0, 12, 17, 20]]);\n      }));\n\n      function loadWithdrawRecords() {\n        return _loadWithdrawRecords.apply(this, arguments);\n      }\n\n      return loadWithdrawRecords;\n    }(),\n    // 获取提现状态文本\n    getWithdrawStatusText: function getWithdrawStatusText(status, reviewRemark) {\n      var statusMap = {\n        0: '待审核',\n        1: '待审核',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已取消'\n      };\n      var statusText = statusMap[status] || '未知状态'; // 如果是已拒绝状态且有拒绝原因，则添加原因\n\n      if (status === 3 && reviewRemark) {\n        statusText += \"\\uFF08\".concat(reviewRemark, \"\\uFF09\");\n      }\n\n      return statusText;\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(statusText) {\n      // 处理带拒绝原因的状态文本\n      if (statusText.includes('已拒绝')) {\n        return 'red';\n      }\n\n      var colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '待审核': 'orange',\n        '已取消': 'gray'\n      };\n      return colorMap[statusText] || 'default';\n    },\n    // 获取排序状态（暂时保留，可能后续需要）\n    getSortOrder: function getSortOrder(field) {\n      if (this.usersSort && this.usersSort.orderBy === field) {\n        return this.usersSort.order === 'asc' ? 'ascend' : 'descend';\n      }\n\n      return null;\n    },\n    // 处理邀请用户表格分页变化\n    handleUsersTableChange: function handleUsersTableChange(pagination, _filters, sorter) {\n      console.log('表格变化:', {\n        pagination: pagination,\n        sorter: sorter\n      });\n\n      if (this.usersPagination && pagination) {\n        this.usersPagination.current = pagination.current || 1;\n        this.usersPagination.pageSize = pagination.pageSize || 10;\n      } // 处理排序\n\n\n      if (sorter && sorter.field && this.usersSort) {\n        var fieldMap = {\n          'nickname': 'nickname',\n          'registerTime': 'register_time',\n          'reward': 'total_reward'\n        };\n        var newOrderBy = fieldMap[sorter.field] || 'total_reward'; // 如果点击的是同一个字段，切换排序方向\n\n        if (this.usersSort.orderBy === newOrderBy) {\n          this.usersSort.order = this.usersSort.order === 'asc' ? 'desc' : 'asc';\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.usersSort.orderBy = newOrderBy;\n\n          if (newOrderBy === 'total_reward') {\n            this.usersSort.order = 'desc'; // 奖励金额默认降序\n          } else {\n            this.usersSort.order = 'asc'; // 其他字段默认升序\n          }\n        }\n\n        console.log('排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.usersSort.orderBy,\n          finalOrder: this.usersSort.order,\n          clickedSameField: this.usersSort.orderBy === newOrderBy\n        }); // 排序时回到第一页\n\n        if (this.usersPagination) {\n          this.usersPagination.current = 1;\n        }\n      }\n\n      this.loadReferralUsers();\n    },\n    // 处理提现记录表格分页变化\n    handleWithdrawTableChange: function handleWithdrawTableChange(pagination, _filters, sorter) {\n      console.log('提现记录表格变化:', {\n        pagination: pagination,\n        sorter: sorter\n      });\n\n      if (this.withdrawPagination && pagination) {\n        this.withdrawPagination.current = pagination.current || 1;\n        this.withdrawPagination.pageSize = pagination.pageSize || 10;\n      } // 处理排序\n\n\n      if (sorter && sorter.field && this.withdrawSort) {\n        var fieldMap = {\n          'amount': 'withdrawal_amount',\n          'applyTime': 'apply_time',\n          'status': 'status',\n          'completeTime': 'review_time'\n        };\n        var newOrderBy = fieldMap[sorter.field] || 'apply_time'; // 如果点击的是同一个字段，切换排序方向\n\n        if (this.withdrawSort.orderBy === newOrderBy) {\n          this.withdrawSort.order = this.withdrawSort.order === 'asc' ? 'desc' : 'asc';\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.withdrawSort.orderBy = newOrderBy;\n\n          if (newOrderBy === 'apply_time') {\n            this.withdrawSort.order = 'desc'; // 申请时间默认降序\n          } else if (newOrderBy === 'withdrawal_amount') {\n            this.withdrawSort.order = 'desc'; // 金额默认降序\n          } else {\n            this.withdrawSort.order = 'asc'; // 其他字段默认升序\n          }\n        }\n\n        console.log('提现记录排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.withdrawSort.orderBy,\n          finalOrder: this.withdrawSort.order\n        }); // 排序时回到第一页\n\n        if (this.withdrawPagination) {\n          this.withdrawPagination.current = 1;\n        }\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 处理提现记录筛选\n    handleWithdrawFilter: function handleWithdrawFilter() {\n      // 筛选时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1;\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 重置提现记录筛选\n    handleWithdrawReset: function handleWithdrawReset() {\n      this.withdrawFilter = {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        dateRange: [],\n        completeDateRange: []\n      }; // 重置时回到第一页\n\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1;\n      }\n\n      this.loadWithdrawRecords();\n    },\n    // 处理取消提现\n    handleCancelWithdraw: function handleCancelWithdraw(record) {\n      var _this5 = this;\n\n      var h = this.$createElement;\n      this.$confirm({\n        title: '确认取消提现',\n        content: h('div', {\n          style: {\n            margin: '16px 0'\n          }\n        }, [h('p', {\n          style: {\n            marginBottom: '8px'\n          }\n        }, [h('strong', '提现金额：'), \"\\xA5\".concat(record.amount)]), h('p', {\n          style: {\n            marginBottom: '8px'\n          }\n        }, [h('strong', '申请时间：'), record.applyTime]), h('p', {\n          style: {\n            color: '#ff4d4f',\n            marginTop: '12px',\n            marginBottom: '0'\n          }\n        }, [h('strong', '注意：'), '取消后金额将返还到可提现余额，此操作不可撤销！'])]),\n        okText: '确认取消',\n        okType: 'danger',\n        cancelText: '返回',\n        centered: true,\n        width: 400,\n        onOk: function () {\n          var _onOk2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee16() {\n            return _regeneratorRuntime.wrap(function _callee16$(_context16) {\n              while (1) {\n                switch (_context16.prev = _context16.next) {\n                  case 0:\n                    _context16.next = 2;\n                    return _this5.confirmCancelWithdraw(record);\n\n                  case 2:\n                  case \"end\":\n                    return _context16.stop();\n                }\n              }\n            }, _callee16);\n          }));\n\n          function onOk() {\n            return _onOk2.apply(this, arguments);\n          }\n\n          return onOk;\n        }()\n      });\n    },\n    // 确认取消提现\n    confirmCancelWithdraw: function () {\n      var _confirmCancelWithdraw = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee17(record) {\n        var params, response;\n        return _regeneratorRuntime.wrap(function _callee17$(_context17) {\n          while (1) {\n            switch (_context17.prev = _context17.next) {\n              case 0:\n                this.cancelLoading = true;\n                _context17.prev = 1;\n                params = {\n                  withdrawalId: record.id\n                };\n                _context17.next = 5;\n                return this.$http.post('/api/usercenter/cancelWithdrawal', params);\n\n              case 5:\n                response = _context17.sent;\n\n                if (!response.success) {\n                  _context17.next = 12;\n                  break;\n                }\n\n                this.$notification.success({\n                  message: '取消成功',\n                  description: '提现申请已取消，金额已返还到可提现余额',\n                  placement: 'topRight'\n                }); // 刷新数据\n\n                _context17.next = 10;\n                return Promise.all([this.loadReferralData(), this.loadWithdrawRecords()]);\n\n              case 10:\n                _context17.next = 13;\n                break;\n\n              case 12:\n                this.$notification.error({\n                  message: '取消失败',\n                  description: response.message || '取消提现失败，请重试',\n                  placement: 'topRight'\n                });\n\n              case 13:\n                _context17.next = 19;\n                break;\n\n              case 15:\n                _context17.prev = 15;\n                _context17.t0 = _context17[\"catch\"](1);\n                console.error('取消提现失败:', _context17.t0);\n\n                if (_context17.t0.response && _context17.t0.response.data && _context17.t0.response.data.message) {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: _context17.t0.response.data.message,\n                    placement: 'topRight'\n                  });\n                } else if (_context17.t0.message) {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: _context17.t0.message,\n                    placement: 'topRight'\n                  });\n                } else {\n                  this.$notification.error({\n                    message: '取消失败',\n                    description: '网络错误，请稍后重试',\n                    placement: 'topRight'\n                  });\n                }\n\n              case 19:\n                _context17.prev = 19;\n                this.cancelLoading = false;\n                return _context17.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context17.stop();\n            }\n          }\n        }, _callee17, this, [[1, 15, 19, 22]]);\n      }));\n\n      function confirmCancelWithdraw(_x4) {\n        return _confirmCancelWithdraw.apply(this, arguments);\n      }\n\n      return confirmCancelWithdraw;\n    }(),\n    // 格式化数字显示\n    formatNumber: function formatNumber(num) {\n      if (num === null || num === undefined) return '0';\n      var number = parseFloat(num);\n      if (isNaN(number)) return '0'; // 如果是金额，保留两位小数\n\n      if (num === this.totalEarnings) {\n        return number.toLocaleString('zh-CN', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        });\n      } // 其他数字不保留小数\n\n\n      return number.toLocaleString('zh-CN');\n    },\n    // 获取角色显示名称\n    getRoleDisplayName: function getRoleDisplayName(roleCode) {\n      switch (roleCode) {\n        case 'VIP':\n          return 'VIP用户';\n\n        case 'SVIP':\n          return 'SVIP用户';\n\n        case 'user':\n        default:\n          return '普通用户';\n      }\n    },\n    // 获取邀请人数要求文本\n    getRequirementText: function getRequirementText(config) {\n      var minReferrals = config.min_referrals;\n      var roleCode = config.role_code; // 查找同角色的下一个等级\n\n      var sameRoleConfigs = this.allLevelConfigs.filter(function (c) {\n        return c.role_code === roleCode;\n      });\n      var currentIndex = sameRoleConfigs.findIndex(function (c) {\n        return c.id === config.id;\n      });\n      var nextConfig = sameRoleConfigs[currentIndex + 1];\n\n      if (roleCode === 'SVIP') {\n        return '无要求';\n      }\n\n      if (nextConfig) {\n        if (minReferrals === 0) {\n          return \"0-\".concat(nextConfig.min_referrals - 1, \"\\u4EBA\");\n        } else {\n          return \"\".concat(minReferrals, \"-\").concat(nextConfig.min_referrals - 1, \"\\u4EBA\");\n        }\n      } else {\n        return \"\".concat(minReferrals, \"\\u4EBA\\u4EE5\\u4E0A\");\n      }\n    }\n  }\n};", {"version": 3, "sources": ["Affiliate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2bA,OAAA,WAAA,MAAA,sCAAA;AACA,SAAA,gBAAA,EAAA,oBAAA,EAAA,WAAA,EAAA,cAAA,QAAA,kBAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,OAAA,GAAA,MAAA,KAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,KAFA;AAIA;AACA,MAAA,aAAA,EAAA,CALA;AAMA,MAAA,iBAAA,EAAA,CANA;AAOA,MAAA,cAAA,EAAA,CAPA;AAQA,MAAA,eAAA,EAAA,CARA;AAUA;AACA,MAAA,aAAA,EAAA,EAXA;AAaA;AACA,MAAA,QAAA,EAAA,MAdA;AAcA;AACA,MAAA,qBAAA,EAAA,EAfA;AAgBA,MAAA,mBAAA,EAAA,OAhBA;AAiBA,MAAA,aAAA,EAAA,CAjBA;AAkBA,MAAA,oBAAA,EAAA,EAlBA;AAmBA,MAAA,aAAA,EAAA,OAnBA;AAoBA,MAAA,aAAA,EAAA,EApBA;AAqBA,MAAA,aAAA,EAAA,SArBA;AAuBA;AACA,MAAA,gBAAA,EAAA,EAxBA;AAyBA,MAAA,eAAA,EAAA,EAzBA;AAyBA;AAEA;AACA,MAAA,WAAA,EAAA,KA5BA;AA6BA,MAAA,SAAA,EAAA,EA7BA;AA8BA,MAAA,cAAA,EAAA,KA9BA;AA8BA;AAEA;AACA,MAAA,iBAAA,EAAA,KAjCA;AAkCA,MAAA,eAAA,EAAA,KAlCA;AAmCA,MAAA,YAAA,EAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAnCA;AAqCA;AACA,MAAA,aAAA,EAAA,EAtCA;AAuCA,MAAA,YAAA,EAAA,KAvCA;AAwCA,MAAA,eAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,eAAA,EAAA,IAJA;AAKA,QAAA,eAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA,mBAAA,KAAA,EAAA,KAAA;AAAA,kCAAA,KAAA,CAAA,CAAA,CAAA,cAAA,KAAA,CAAA,CAAA,CAAA,iCAAA,KAAA;AAAA;AANA,OAxCA;AAgDA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,cADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjDA;AAqDA,MAAA,aAAA,EAAA,qBArDA;AAqDA;AAEA;AACA,MAAA,eAAA,EAAA,EAxDA;AAyDA,MAAA,cAAA,EAAA,KAzDA;AA0DA,MAAA,aAAA,EAAA,KA1DA;AA2DA,MAAA,kBAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,eAAA,EAAA,IAJA;AAKA,QAAA,eAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA,mBAAA,KAAA,EAAA,KAAA;AAAA,kCAAA,KAAA,CAAA,CAAA,CAAA,cAAA,KAAA,CAAA,CAAA,CAAA,iCAAA,KAAA;AAAA;AANA,OA3DA;AAmEA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,OAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA;AAFA,OApEA;AAwEA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,IAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAGA;AACA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,iBAAA,EAAA;AALA,OAzEA;AAiFA;AACA,MAAA,QAAA,EAAA;AAlFA,KAAA;AAoFA,GA1FA;AA2FA,EAAA,QAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AACA,aAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAJA;AAKA,QAAA,KAAA,EAAA;AALA,OADA,EAQA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,GAAA,EAAA,UAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OARA,EAcA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,cAFA;AAGA,QAAA,GAAA,EAAA,cAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAdA,EAoBA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAJA;AAKA,QAAA,MAAA,EAAA;AALA,OApBA,CAAA;AA4BA,KA/BA;AAiCA;AACA,IAAA,eAlCA,6BAkCA;AACA,aAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAJA;AAKA,QAAA,MAAA,EAAA;AALA,OADA,EAQA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OARA,EAaA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,WAFA;AAGA,QAAA,GAAA,EAAA,WAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAbA,EAmBA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,GAAA,EAAA,QAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAnBA,EA0BA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,SAAA,EAAA,cAFA;AAGA,QAAA,GAAA,EAAA,cAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OA1BA,EAgCA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OAhCA,CAAA;AAuCA;AA1EA,GA3FA;AAuKA,EAAA,OAvKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAwKA,KAAA,qBAAA,EAxKA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA0KA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,YAFA,wBAEA,MAFA,EAEA;AACA,UAAA,CAAA,MAAA,EAAA;AACA,eAAA,KAAA,aAAA;AACA,OAHA,CAKA;;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,KAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OARA,CAUA;;;AACA,aAAA,KAAA,oBAAA,CAAA,MAAA,KAAA,KAAA,aAAA;AACA,KAdA;AAgBA;AACA,IAAA,oBAjBA,gCAiBA,MAjBA,EAiBA;AACA,UAAA,CAAA,MAAA,EAAA,OAAA,KAAA,aAAA,CADA,CAGA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,KAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA;AACA,OANA,CAQA;;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,oBAAA,GAAA,MAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAA;AACA,OAXA,CAaA;;;AACA,UAAA,YAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,eAAA;AACA,aAAA,YAAA,aAAA,YAAA,cAAA,MAAA,IAAA,MAAA;AACA,KAjCA;AAmCA;AACA,IAAA,iBApCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAsCA,KAAA,KAAA,CAAA,GAAA,CAAA,gCAAA,CAtCA;;AAAA;AAsCA,gBAAA,QAtCA;;AAuCA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,IAAA,QAAA,CAAA,MAAA,EAAA;AACA,uBAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,KAAA,aAAA;AACA;;AA1CA;AAAA;;AAAA;AAAA;AAAA;AA4CA,gBAAA,OAAA,CAAA,IAAA,CAAA,mCAAA,gBA5CA,CA6CA;;AA7CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiDA;AACA,IAAA,qBAlDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmDA,gBAAA,KAnDA,GAmDA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CAnDA;;AAAA,oBAoDA,KApDA;AAAA;AAAA;AAAA;;AAqDA,qBAAA,OAAA,CAAA,IAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,QAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,oBAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAAA,iBAAA;AArDA;;AAAA;AAAA;AAAA;AAAA,uBA0DA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,gBAAA,EAFA,EAGA,KAAA,YAAA,EAHA,EAIA,KAAA,eAAA,EAJA,EAKA,KAAA,iBAAA,EALA,EAMA,KAAA,mBAAA,EANA,EAOA,KAAA,iBAAA,EAPA,CAAA,CA1DA;;AAAA;AAoEA;AACA,qBAAA,wBAAA,GArEA,CAuEA;;AACA,qBAAA,iBAAA;AAxEA;AAAA;;AAAA;AAAA;AAAA;AA0EA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,gBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AA3EA;AAAA;AAiFA,qBAAA,OAAA,GAAA,KAAA;AAjFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAqFA;AACA,IAAA,gBAtFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAwFA,gBAAA,EAxFA;;AAAA;AAwFA,gBAAA,QAxFA;;AAyFA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,IADA,GACA,QAAA,CAAA,MADA;AAEA,uBAAA,aAAA,GAAA,IAAA,CAAA,mBAAA,IAAA,CAAA;AACA,uBAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA,IAAA,CAAA;AACA,uBAAA,cAAA,GAAA,IAAA,CAAA,eAAA,IAAA,CAAA;AACA,uBAAA,eAAA,GAAA,IAAA,CAAA,gBAAA,IAAA,CAAA;AACA;;AA/FA;AAAA;;AAAA;AAAA;AAAA;AAiGA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AAjGA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsGA;AACA,IAAA,gBAvGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAyGA,oBAAA,CAAA,EAAA,CAzGA;;AAAA;AAyGA,gBAAA,QAzGA;;AA0GA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,aAAA,GAAA,QAAA,CAAA,MAAA,IAAA,EAAA;AACA;;AA5GA;AAAA;;AAAA;AAAA;AAAA;AA8GA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBA9GA,CA+GA;;AACA,qBAAA,aAAA,aAAA,MAAA,CAAA,QAAA,CAAA,MAAA;;AAhHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAoHA;AACA,IAAA,YArHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAuHA,WAAA,EAvHA;;AAAA;AAuHA,gBAAA,QAvHA;;AAwHA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,SAAA,IAAA,MAAA;AACA;;AA1HA;AAAA;;AAAA;AAAA;AAAA;AA4HA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,GAAA,MAAA;;AA7HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiIA;AACA,IAAA,eAlIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAoIA,cAAA,EApIA;;AAAA;AAoIA,gBAAA,QApIA;;AAqIA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,eAAA,GAAA,QAAA,CAAA,MAAA,IAAA,EAAA;AACA;;AAvIA;AAAA;;AAAA;AAAA;AAAA;AAyIA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,eAAA,GAAA,EAAA;;AA1IA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8IA;AACA,IAAA,wBA/IA,sCA+IA;AAAA;;AACA,UAAA,WAAA,GAAA,KAAA,eAAA,CADA,CAGA;;AACA,UAAA,gBAAA,GAAA,KAAA,eAAA,CAAA,MAAA,CAAA,UAAA,MAAA;AAAA,eAAA,MAAA,CAAA,SAAA,KAAA,KAAA,CAAA,QAAA;AAAA,OAAA,CAAA;;AAEA,UAAA,gBAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,IAAA,CAAA,eAAA,EAAA,KAAA,QAAA;AACA;AACA,OATA,CAWA;;;AACA,UAAA,YAAA,GAAA,IAAA;;AACA,WAAA,IAAA,CAAA,GAAA,gBAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,WAAA,IAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,aAAA,EAAA;AACA,UAAA,YAAA,GAAA,gBAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA;;AAEA,UAAA,CAAA,YAAA,EAAA;AACA,QAAA,YAAA,GAAA,gBAAA,CAAA,CAAA,CAAA,CADA,CACA;AACA,OAtBA,CAwBA;;;AACA,WAAA,qBAAA,GAAA,UAAA,CAAA,YAAA,CAAA,eAAA,CAAA;AACA,WAAA,mBAAA,GAAA,YAAA,CAAA,UAAA,CA1BA,CA4BA;;AACA,UAAA,SAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,UAAA,MAAA;AAAA,eAAA,MAAA,CAAA,aAAA,GAAA,WAAA;AAAA,OAAA,CAAA;;AAEA,UAAA,SAAA,EAAA;AACA,aAAA,oBAAA,GAAA,SAAA,CAAA,aAAA;AACA,aAAA,aAAA,GAAA,SAAA,CAAA,UAAA;AACA,aAAA,aAAA,GAAA,UAAA,CAAA,SAAA,CAAA,eAAA,CAAA;AACA,aAAA,aAAA,GAAA,WAAA,GAAA,SAAA,CAAA,aAAA,GAAA,GAAA;AACA,aAAA,aAAA,GAAA,SAAA;AACA,OANA,MAMA;AACA;AACA,aAAA,oBAAA,GAAA,CAAA;AACA,aAAA,aAAA,GAAA,QAAA;AACA,aAAA,aAAA,GAAA,KAAA,qBAAA;AACA,aAAA,aAAA,GAAA,GAAA;AACA,aAAA,aAAA,GAAA,SAAA;AACA,OA5CA,CA8CA;;;AACA,WAAA,gBAAA,GAAA,gBAAA,CAAA,GAAA,CAAA,UAAA,MAAA,EAAA,KAAA,EAAA;AACA,YAAA,WAAA,GAAA,WAAA,IAAA,MAAA,CAAA,aAAA,CADA,CAGA;;AACA,YAAA,SAAA,GAAA,KAAA;;AACA,YAAA,CAAA,WAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,SAAA,GAAA,IAAA;AACA,WAHA,MAGA;AACA;AACA,gBAAA,eAAA,GAAA,gBAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,aAAA;AACA,YAAA,SAAA,GAAA,WAAA,IAAA,eAAA;AACA;AACA;;AAEA,YAAA,UAAA,GAAA,CAAA,WAAA,IAAA,CAAA,SAAA;AAEA,YAAA,SAAA,GAAA,CAAA;;AACA,YAAA,CAAA,WAAA,EAAA;AACA,UAAA,SAAA,GAAA,MAAA,CAAA,aAAA,GAAA,WAAA;AACA;;AAEA,eAAA;AACA,UAAA,IAAA,EAAA,MAAA,CAAA,UADA;AAEA,UAAA,IAAA,EAAA,UAAA,CAAA,MAAA,CAAA,eAAA,CAFA;AAGA,UAAA,WAAA,EAAA,MAAA,CAAA,aAHA;AAIA,UAAA,WAAA,EAAA,WAJA;AAKA,UAAA,SAAA,EAAA,SALA;AAMA,UAAA,UAAA,EAAA,UANA;AAOA,UAAA,SAAA,EAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA;AAPA,SAAA;AASA,OAhCA,CAAA;AAiCA,KA/NA;AAiOA;AACA,IAAA,QAlOA,sBAkOA;AAAA;;AACA,UAAA,CAAA,KAAA,aAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,iBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AAEA,MAAA,SAAA,CAAA,SAAA,CAAA,SAAA,CAAA,KAAA,aAAA,EAAA,IAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,EAAA,yBAFA;AAGA,UAAA,SAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,CAJA;AAKA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA,OADA;AAEA,YAAA,SAAA,EAAA,OAFA;AAGA,YAAA,YAAA,EAAA,KAHA;AAIA,YAAA,SAAA,EAAA;AAJA;AALA,SAAA;AAYA,OAbA,EAaA,KAbA,CAaA,YAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,gBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA,OAnBA;AAoBA,KAhQA;AAkQA;AACA,IAAA,iBAnQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAoQA,CAAA,KAAA,aAAA,IAAA,KAAA,cApQA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAyQA,gBAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAzQA,CA2QA;;AA3QA;AAAA,uBA4QA,KAAA,KAAA,CAAA,IAAA,CAAA,wCAAA,EAAA,IAAA,EAAA;AACA,kBAAA,MAAA,EAAA;AACA,oBAAA,GAAA,EAAA,KAAA;AADA;AADA,iBAAA,CA5QA;;AAAA;AA4QA,gBAAA,QA5QA;;AAkRA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,EAAA;AACA;AACA,uBAAA,SAAA,GAAA,QAAA,CAAA,MAAA;AACA,uBAAA,cAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,KAAA,SAAA;AACA;;AAvRA;AAAA;;AAAA;AAAA;AAAA;AAyRA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,gBAzRA,CA0RA;;AA1RA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8RA;AACA,IAAA,cA/RA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAgSA,KAAA,aAhSA;AAAA;AAAA;AAAA;;AAiSA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,WAAA,EAAA,oBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AAjSA;;AAAA;AAAA,sBA0SA,KAAA,cAAA,IAAA,KAAA,SA1SA;AAAA;AAAA;AAAA;;AA2SA,qBAAA,WAAA,GAAA,IAAA;AA3SA;;AAAA;AAAA;AAgTA,qBAAA,SAAA,GAAA,IAAA,CAhTA,CAkTA;;AAlTA;AAAA,uBAmTA,KAAA,KAAA,CAAA,IAAA,CAAA,wCAAA,EAAA,IAAA,EAAA;AACA,kBAAA,MAAA,EAAA;AACA,oBAAA,GAAA,EAAA,KAAA;AADA;AADA,iBAAA,CAnTA;;AAAA;AAmTA,gBAAA,QAnTA;AAyTA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;;AAzTA,sBA2TA,QAAA,IAAA,QAAA,CAAA,OA3TA;AAAA;AAAA;AAAA;;AA4TA;AACA,qBAAA,SAAA,GAAA,QAAA,CAAA,MAAA;AACA,qBAAA,cAAA,GAAA,IAAA;AACA,qBAAA,WAAA,GAAA,IAAA;AAEA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,WAAA,EAAA,wBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AAjUA;AAAA;;AAAA;AAuUA,gBAAA,QAvUA,GAuUA,QAAA,IAAA,QAAA,CAAA,OAAA,IAAA,MAvUA;AAAA,sBAwUA,IAAA,KAAA,CAAA,QAAA,CAxUA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA2UA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,aAAA,OAAA,IAAA,eAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AA5UA;AAAA;AAkVA,qBAAA,SAAA,GAAA,KAAA;AAlVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsVA;AACA,IAAA,cAvVA,4BAuVA;AACA,UAAA,CAAA,KAAA,SAAA,EAAA;;AAEA,UAAA;AACA;AACA,YAAA,YAAA,GAAA,KAAA,mBAAA,CAAA,KAAA,aAAA,CAAA,CAFA,CAIA;;AACA,YAAA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,oBAAA,IAAA,kCAAA;AACA,YAAA,WAAA,aAAA,UAAA,wDAAA,kBAAA,CAAA,KAAA,SAAA,CAAA,mBAAA,YAAA,gBAAA,IAAA,CAAA,GAAA,EAAA,CAAA;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,WAAA,EARA,CAUA;;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,IAAA,GAAA,SAAA;AACA,QAAA,MAAA,CAAA,GAAA,GAAA,WAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,EAhBA,CAkBA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,cAAA,MAAA,CAAA,UAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA;AACA;AACA,SAJA,EAIA,IAJA,CAAA;AAMA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,2CAAA,YAAA,kCAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA,OA9BA,CA8BA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA;AACA,aAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA,eAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,KAhYA;AAkYA;AACA,IAAA,mBAnYA,+BAmYA,GAnYA,EAmYA;AACA,UAAA,CAAA,GAAA,EAAA,OAAA,SAAA;;AAEA,UAAA;AACA,YAAA,MAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA;AACA,YAAA,QAAA,GAAA,MAAA,CAAA,YAAA,CAAA,GAAA,CAAA,KAAA,CAAA;AACA,eAAA,QAAA,IAAA,SAAA;AACA,OAJA,CAIA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA;AACA,eAAA,SAAA;AACA;AACA,KA9YA;AAgZA;AACA,IAAA,iBAjZA,+BAiZA;AACA,UAAA,KAAA,iBAAA,GAAA,EAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,QADA;AAEA,UAAA,WAAA,EAAA,wBAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KA3ZA;AA6ZA;AACA,IAAA,cA9ZA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AA+ZA,qBAAA,YAAA,CAAA,cAAA;AAAA,sFAAA,mBAAA,GAAA,EAAA,MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,GADA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAGA;AACA,4BAAA,CAJA,GAIA,MAAA,CAAA,cAJA;;AAKA,4BAAA,MAAA,CAAA,QAAA,CAAA;AACA,8BAAA,KAAA,EAAA,QADA;AAEA,8BAAA,OAAA,EAAA,CAAA,CAAA,KAAA,EAAA;AAAA,gCAAA,KAAA,EAAA;AAAA,kCAAA,MAAA,EAAA;AAAA;AAAA,+BAAA,EAAA,CACA,CAAA,CAAA,GAAA,EAAA;AAAA,gCAAA,KAAA,EAAA;AAAA,kCAAA,YAAA,EAAA;AAAA;AAAA,+BAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,OAAA,CADA,gBAEA,MAAA,CAAA,MAFA,EAAA,CADA,EAKA,CAAA,CAAA,GAAA,EAAA;AAAA,gCAAA,KAAA,EAAA;AAAA,kCAAA,YAAA,EAAA;AAAA;AAAA,+BAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,QAAA,CADA,EAEA,MAAA,CAAA,aAFA,CAAA,CALA,EASA,CAAA,CAAA,GAAA,EAAA;AAAA,gCAAA,KAAA,EAAA;AAAA,kCAAA,YAAA,EAAA;AAAA;AAAA,+BAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,QAAA,CADA,EAEA,MAAA,CAAA,QAFA,CAAA,CATA,EAaA,CAAA,CAAA,GAAA,EAAA;AAAA,gCAAA,KAAA,EAAA;AAAA,kCAAA,KAAA,EAAA,SAAA;AAAA,kCAAA,SAAA,EAAA,MAAA;AAAA,kCAAA,YAAA,EAAA;AAAA;AAAA,+BAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,KAAA,CADA,EAEA,sBAFA,CAAA,CAbA,CAAA,CAFA;AAoBA,8BAAA,MAAA,EAAA,MApBA;AAqBA,8BAAA,UAAA,EAAA,IArBA;AAsBA,8BAAA,QAAA,EAAA,IAtBA;AAuBA,8BAAA,KAAA,EAAA,GAvBA;AAwBA,8BAAA,IAAA;AAAA,qGAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDACA,MAAA,CAAA,qBAAA,CAAA,MAAA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAxBA,6BAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA;;AA/ZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmcA;AACA,IAAA,qBApcA;AAAA,+GAocA,MApcA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqcA,qBAAA,eAAA,GAAA,IAAA;AArcA;AAwcA,gBAAA,MAxcA,GAwcA;AACA,kBAAA,gBAAA,EAAA,MAAA,CAAA,MADA;AAEA,kBAAA,QAAA,EAAA,MAAA,CAAA,QAFA;AAGA,kBAAA,aAAA,EAAA,MAAA,CAAA;AAHA,iBAxcA;AAAA;AAAA,uBA8cA,KAAA,KAAA,CAAA,IAAA,CAAA,iCAAA,EAAA,MAAA,CA9cA;;AAAA;AA8cA,gBAAA,QA9cA;;AAAA,qBAgdA,QAAA,CAAA,OAhdA;AAAA;AAAA;AAAA;;AAidA,qBAAA,eAAA,GAAA,KAAA;AACA,qBAAA,iBAAA,GAAA,KAAA;AACA,qBAAA,YAAA,CAAA,WAAA;AAEA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,QADA;AAEA,kBAAA,WAAA,EAAA,uBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA,EArdA,CA2dA;;AA3dA;AAAA,uBA4dA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,mBAAA,EAFA,CAAA,CA5dA;;AAAA;AAAA;AAAA;;AAAA;AAieA,qBAAA,eAAA,GAAA,KAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,QADA;AAEA,kBAAA,WAAA,EAAA,QAAA,CAAA,OAAA,IAAA,UAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AAleA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAyeA,qBAAA,eAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA,iBA1eA,CA4eA;;AACA,oBAAA,cAAA,QAAA,IAAA,cAAA,QAAA,CAAA,IAAA,IAAA,cAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,QADA;AAEA,oBAAA,WAAA,EAAA,cAAA,QAAA,CAAA,IAAA,CAAA,OAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,cAAA,OAAA,EAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,QADA;AAEA,oBAAA,WAAA,EAAA,cAAA,OAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,QADA;AAEA,oBAAA,WAAA,EAAA,YAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA;;AA/fA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmgBA;AACA,IAAA,iBApgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsgBA,qBAAA,YAAA,GAAA,IAAA;AAEA,gBAAA,MAxgBA,GAwgBA;AACA,kBAAA,OAAA,EAAA,KAAA,eAAA,IAAA,KAAA,eAAA,CAAA,OAAA,IAAA,CADA;AAEA,kBAAA,IAAA,EAAA,KAAA,eAAA,IAAA,KAAA,eAAA,CAAA,QAAA,IAAA,EAFA;AAGA,kBAAA,OAAA,EAAA,KAAA,SAAA,IAAA,KAAA,SAAA,CAAA,OAAA,IAAA,cAHA;AAIA,kBAAA,KAAA,EAAA,KAAA,SAAA,IAAA,KAAA,SAAA,CAAA,KAAA,IAAA;AAJA,iBAxgBA;AA+gBA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,MAAA;AA/gBA;AAAA,uBAihBA,KAAA,KAAA,CAAA,GAAA,CAAA,8BAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CAjhBA;;AAAA;AAihBA,gBAAA,QAjhBA;;AAmhBA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MADA,GACA,QAAA,CAAA,MAAA,IAAA,EADA;AAEA,kBAAA,OAFA,GAEA,MAAA,CAAA,OAAA,IAAA,EAFA,EAIA;;AACA,sBAAA,KAAA,eAAA,EAAA;AACA,yBAAA,eAAA,CAAA,KAAA,GAAA,MAAA,CAAA,KAAA,IAAA,CAAA;AACA,mBAPA,CASA;;;AACA,uBAAA,aAAA,GAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;AAAA,2BAAA;AACA,sBAAA,GAAA,EAAA,IAAA,CAAA,EAAA,IAAA,KADA;AAEA,sBAAA,QAAA,EAAA,IAAA,CAAA,gBAAA,6BAAA,KAAA,GAAA,CAAA,CAFA;AAGA,sBAAA,MAAA,EAAA,IAAA,CAAA,cAAA,IAAA,EAHA;AAIA,sBAAA,YAAA,EAAA,IAAA,CAAA,aAAA,IAAA,EAJA;AAKA,sBAAA,MAAA,EAAA,IAAA,CAAA,YAAA,IAAA;AALA,qBAAA;AAAA,mBAAA,CAAA;AAOA,iBAjBA,MAiBA;AACA,uBAAA,aAAA,GAAA,EAAA;;AACA,sBAAA,KAAA,eAAA,EAAA;AACA,yBAAA,eAAA,CAAA,KAAA,GAAA,CAAA;AACA;AACA;;AAziBA;AAAA;;AAAA;AAAA;AAAA;AA2iBA,gBAAA,OAAA,CAAA,KAAA,CAAA,aAAA;AACA,qBAAA,aAAA,GAAA,EAAA,CA5iBA,CA6iBA;;AACA,oBAAA,cAAA,QAAA,IAAA,cAAA,QAAA,CAAA,MAAA,KAAA,GAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;;AAhjBA;AAAA;AAkjBA,qBAAA,YAAA,GAAA,KAAA;AAljBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsjBA;AACA,IAAA,mBAvjBA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyjBA,qBAAA,cAAA,GAAA,IAAA;AAEA,gBAAA,MA3jBA,GA2jBA;AACA,kBAAA,OAAA,EAAA,KAAA,kBAAA,IAAA,KAAA,kBAAA,CAAA,OAAA,IAAA,CADA;AAEA,kBAAA,IAAA,EAAA,KAAA,kBAAA,IAAA,KAAA,kBAAA,CAAA,QAAA,IAAA,EAFA;AAGA,kBAAA,OAAA,EAAA,KAAA,YAAA,IAAA,KAAA,YAAA,CAAA,OAAA,IAAA,YAHA;AAIA,kBAAA,KAAA,EAAA,KAAA,YAAA,IAAA,KAAA,YAAA,CAAA,KAAA,IAAA;AAJA,iBA3jBA,EAkkBA;;AACA,oBAAA,KAAA,cAAA,EAAA;AACA,sBAAA,KAAA,cAAA,CAAA,SAAA,KAAA,IAAA,IAAA,KAAA,cAAA,CAAA,SAAA,KAAA,EAAA,EAAA;AACA,oBAAA,MAAA,CAAA,SAAA,GAAA,KAAA,cAAA,CAAA,SAAA;AACA;;AACA,sBAAA,KAAA,cAAA,CAAA,SAAA,KAAA,IAAA,IAAA,KAAA,cAAA,CAAA,SAAA,KAAA,EAAA,EAAA;AACA,oBAAA,MAAA,CAAA,SAAA,GAAA,KAAA,cAAA,CAAA,SAAA;AACA;;AACA,sBAAA,KAAA,cAAA,CAAA,MAAA,KAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,GAAA,KAAA,cAAA,CAAA,MAAA;AACA;;AACA,sBAAA,KAAA,cAAA,CAAA,SAAA,IAAA,KAAA,cAAA,CAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,SAAA,GAAA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,GACA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CADA,GAEA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,CAFA;AAGA,oBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,GACA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CADA,GAEA,KAAA,cAAA,CAAA,SAAA,CAAA,CAAA,CAFA;AAGA;;AACA,sBAAA,KAAA,cAAA,CAAA,iBAAA,IAAA,KAAA,cAAA,CAAA,iBAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,iBAAA,GAAA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,GACA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CADA,GAEA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,CAFA;AAGA,oBAAA,MAAA,CAAA,eAAA,GAAA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,GACA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,CAAA,YAAA,CADA,GAEA,KAAA,cAAA,CAAA,iBAAA,CAAA,CAAA,CAFA;AAGA;AACA;;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,MAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AACA,kBAAA,SAAA,EAAA,KAAA,cAAA,CAAA,SADA;AAEA,kBAAA,iBAAA,EAAA,KAAA,cAAA,CAAA;AAFA,iBAAA;AAlmBA;AAAA,uBAumBA,KAAA,KAAA,CAAA,GAAA,CAAA,mCAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CAvmBA;;AAAA;AAumBA,gBAAA,QAvmBA;;AAymBA,oBAAA,QAAA,IAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MADA,GACA,QAAA,CAAA,MAAA,IAAA,EADA;AAEA,kBAAA,OAFA,GAEA,MAAA,CAAA,OAAA,IAAA,EAFA,EAIA;;AACA,sBAAA,KAAA,kBAAA,EAAA;AACA,yBAAA,kBAAA,CAAA,KAAA,GAAA,MAAA,CAAA,KAAA,IAAA,CAAA;AACA,mBAPA,CASA;;;AACA,uBAAA,eAAA,GAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;AAAA,2BAAA;AACA,sBAAA,GAAA,EAAA,IAAA,CAAA,EAAA,IAAA,KADA;AAEA,sBAAA,EAAA,EAAA,IAAA,CAAA,EAFA;AAGA,sBAAA,MAAA,EAAA,IAAA,CAAA,iBAAA,IAAA,MAHA;AAIA,sBAAA,MAAA,EAAA,IAAA,CAAA,gBAAA,IAAA,KAJA;AAKA,sBAAA,SAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EALA;AAMA,sBAAA,MAAA,EAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,aAAA,CANA;AAOA,sBAAA,SAAA,EAAA,IAAA,CAAA,MAPA;AAQA,sBAAA,YAAA,EAAA,IAAA,CAAA,WAAA,IAAA;AARA,qBAAA;AAAA,mBAAA,CAAA;AAUA,iBApBA,MAoBA;AACA,uBAAA,eAAA,GAAA,EAAA;;AACA,sBAAA,KAAA,kBAAA,EAAA;AACA,yBAAA,kBAAA,CAAA,KAAA,GAAA,CAAA;AACA;AACA;;AAloBA;AAAA;;AAAA;AAAA;AAAA;AAooBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,eAAA,GAAA,EAAA,CAroBA,CAsoBA;;AACA,oBAAA,cAAA,QAAA,IAAA,cAAA,QAAA,CAAA,MAAA,KAAA,GAAA,EAAA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;;AAzoBA;AAAA;AA2oBA,qBAAA,cAAA,GAAA,KAAA;AA3oBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+oBA;AACA,IAAA,qBAhpBA,iCAgpBA,MAhpBA,EAgpBA,YAhpBA,EAgpBA;AACA,UAAA,SAAA,GAAA;AACA,WAAA,KADA;AAEA,WAAA,KAFA;AAGA,WAAA,KAHA;AAIA,WAAA,KAJA;AAKA,WAAA;AALA,OAAA;AAOA,UAAA,UAAA,GAAA,SAAA,CAAA,MAAA,CAAA,IAAA,MAAA,CARA,CAUA;;AACA,UAAA,MAAA,KAAA,CAAA,IAAA,YAAA,EAAA;AACA,QAAA,UAAA,oBAAA,YAAA,WAAA;AACA;;AAEA,aAAA,UAAA;AACA,KAhqBA;AAkqBA;AACA,IAAA,cAnqBA,0BAmqBA,UAnqBA,EAmqBA;AACA;AACA,UAAA,UAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA;;AAEA,UAAA,QAAA,GAAA;AACA,eAAA,OADA;AAEA,eAAA,MAFA;AAGA,eAAA,QAHA;AAIA,eAAA;AAJA,OAAA;AAMA,aAAA,QAAA,CAAA,UAAA,CAAA,IAAA,SAAA;AACA,KAhrBA;AAkrBA;AACA,IAAA,YAnrBA,wBAmrBA,KAnrBA,EAmrBA;AACA,UAAA,KAAA,SAAA,IAAA,KAAA,SAAA,CAAA,OAAA,KAAA,KAAA,EAAA;AACA,eAAA,KAAA,SAAA,CAAA,KAAA,KAAA,KAAA,GAAA,QAAA,GAAA,SAAA;AACA;;AACA,aAAA,IAAA;AACA,KAxrBA;AA0rBA;AACA,IAAA,sBA3rBA,kCA2rBA,UA3rBA,EA2rBA,QA3rBA,EA2rBA,MA3rBA,EA2rBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AAAA,QAAA,UAAA,EAAA,UAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA;;AAEA,UAAA,KAAA,eAAA,IAAA,UAAA,EAAA;AACA,aAAA,eAAA,CAAA,OAAA,GAAA,UAAA,CAAA,OAAA,IAAA,CAAA;AACA,aAAA,eAAA,CAAA,QAAA,GAAA,UAAA,CAAA,QAAA,IAAA,EAAA;AACA,OANA,CAQA;;;AACA,UAAA,MAAA,IAAA,MAAA,CAAA,KAAA,IAAA,KAAA,SAAA,EAAA;AACA,YAAA,QAAA,GAAA;AACA,sBAAA,UADA;AAEA,0BAAA,eAFA;AAGA,oBAAA;AAHA,SAAA;AAMA,YAAA,UAAA,GAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,cAAA,CAPA,CASA;;AACA,YAAA,KAAA,SAAA,CAAA,OAAA,KAAA,UAAA,EAAA;AACA,eAAA,SAAA,CAAA,KAAA,GAAA,KAAA,SAAA,CAAA,KAAA,KAAA,KAAA,GAAA,MAAA,GAAA,KAAA;AACA,SAFA,MAEA;AACA;AACA,eAAA,SAAA,CAAA,OAAA,GAAA,UAAA;;AACA,cAAA,UAAA,KAAA,cAAA,EAAA;AACA,iBAAA,SAAA,CAAA,KAAA,GAAA,MAAA,CADA,CACA;AACA,WAFA,MAEA;AACA,iBAAA,SAAA,CAAA,KAAA,GAAA,KAAA,CADA,CACA;AACA;AACA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AACA,UAAA,KAAA,EAAA,MAAA,CAAA,KADA;AAEA,UAAA,KAAA,EAAA,MAAA,CAAA,KAFA;AAGA,UAAA,OAAA,EAAA,KAAA,SAAA,CAAA,OAHA;AAIA,UAAA,UAAA,EAAA,KAAA,SAAA,CAAA,KAJA;AAKA,UAAA,gBAAA,EAAA,KAAA,SAAA,CAAA,OAAA,KAAA;AALA,SAAA,EAtBA,CA8BA;;AACA,YAAA,KAAA,eAAA,EAAA;AACA,eAAA,eAAA,CAAA,OAAA,GAAA,CAAA;AACA;AACA;;AAEA,WAAA,iBAAA;AACA,KAzuBA;AA2uBA;AACA,IAAA,yBA5uBA,qCA4uBA,UA5uBA,EA4uBA,QA5uBA,EA4uBA,MA5uBA,EA4uBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA;AAAA,QAAA,UAAA,EAAA,UAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA;;AAEA,UAAA,KAAA,kBAAA,IAAA,UAAA,EAAA;AACA,aAAA,kBAAA,CAAA,OAAA,GAAA,UAAA,CAAA,OAAA,IAAA,CAAA;AACA,aAAA,kBAAA,CAAA,QAAA,GAAA,UAAA,CAAA,QAAA,IAAA,EAAA;AACA,OANA,CAQA;;;AACA,UAAA,MAAA,IAAA,MAAA,CAAA,KAAA,IAAA,KAAA,YAAA,EAAA;AACA,YAAA,QAAA,GAAA;AACA,oBAAA,mBADA;AAEA,uBAAA,YAFA;AAGA,oBAAA,QAHA;AAIA,0BAAA;AAJA,SAAA;AAOA,YAAA,UAAA,GAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,YAAA,CARA,CAUA;;AACA,YAAA,KAAA,YAAA,CAAA,OAAA,KAAA,UAAA,EAAA;AACA,eAAA,YAAA,CAAA,KAAA,GAAA,KAAA,YAAA,CAAA,KAAA,KAAA,KAAA,GAAA,MAAA,GAAA,KAAA;AACA,SAFA,MAEA;AACA;AACA,eAAA,YAAA,CAAA,OAAA,GAAA,UAAA;;AACA,cAAA,UAAA,KAAA,YAAA,EAAA;AACA,iBAAA,YAAA,CAAA,KAAA,GAAA,MAAA,CADA,CACA;AACA,WAFA,MAEA,IAAA,UAAA,KAAA,mBAAA,EAAA;AACA,iBAAA,YAAA,CAAA,KAAA,GAAA,MAAA,CADA,CACA;AACA,WAFA,MAEA;AACA,iBAAA,YAAA,CAAA,KAAA,GAAA,KAAA,CADA,CACA;AACA;AACA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,EAAA,MAAA,CAAA,KADA;AAEA,UAAA,KAAA,EAAA,MAAA,CAAA,KAFA;AAGA,UAAA,OAAA,EAAA,KAAA,YAAA,CAAA,OAHA;AAIA,UAAA,UAAA,EAAA,KAAA,YAAA,CAAA;AAJA,SAAA,EAzBA,CAgCA;;AACA,YAAA,KAAA,kBAAA,EAAA;AACA,eAAA,kBAAA,CAAA,OAAA,GAAA,CAAA;AACA;AACA;;AAEA,WAAA,mBAAA;AACA,KA5xBA;AA8xBA;AACA,IAAA,oBA/xBA,kCA+xBA;AACA;AACA,UAAA,KAAA,kBAAA,EAAA;AACA,aAAA,kBAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AACA,WAAA,mBAAA;AACA,KAryBA;AAuyBA;AACA,IAAA,mBAxyBA,iCAwyBA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,IAFA;AAGA,QAAA,MAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,iBAAA,EAAA;AALA,OAAA,CADA,CASA;;AACA,UAAA,KAAA,kBAAA,EAAA;AACA,aAAA,kBAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AAEA,WAAA,mBAAA;AACA,KAvzBA;AAyzBA;AACA,IAAA,oBA1zBA,gCA0zBA,MA1zBA,EA0zBA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA,cAAA;AACA,WAAA,QAAA,CAAA;AACA,QAAA,KAAA,EAAA,QADA;AAEA,QAAA,OAAA,EAAA,CAAA,CAAA,KAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,YAAA,MAAA,EAAA;AAAA;AAAA,SAAA,EAAA,CACA,CAAA,CAAA,GAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,YAAA,YAAA,EAAA;AAAA;AAAA,SAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,OAAA,CADA,gBAEA,MAAA,CAAA,MAFA,EAAA,CADA,EAKA,CAAA,CAAA,GAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,YAAA,YAAA,EAAA;AAAA;AAAA,SAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,OAAA,CADA,EAEA,MAAA,CAAA,SAFA,CAAA,CALA,EASA,CAAA,CAAA,GAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,YAAA,KAAA,EAAA,SAAA;AAAA,YAAA,SAAA,EAAA,MAAA;AAAA,YAAA,YAAA,EAAA;AAAA;AAAA,SAAA,EAAA,CACA,CAAA,CAAA,QAAA,EAAA,KAAA,CADA,EAEA,yBAFA,CAAA,CATA,CAAA,CAFA;AAgBA,QAAA,MAAA,EAAA,MAhBA;AAiBA,QAAA,MAAA,EAAA,QAjBA;AAkBA,QAAA,UAAA,EAAA,IAlBA;AAmBA,QAAA,QAAA,EAAA,IAnBA;AAoBA,QAAA,KAAA,EAAA,GApBA;AAqBA,QAAA,IAAA;AAAA,gFAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BACA,MAAA,CAAA,qBAAA,CAAA,MAAA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AArBA,OAAA;AAyBA,KAr1BA;AAu1BA;AACA,IAAA,qBAx1BA;AAAA,+GAw1BA,MAx1BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAy1BA,qBAAA,aAAA,GAAA,IAAA;AAz1BA;AA41BA,gBAAA,MA51BA,GA41BA;AACA,kBAAA,YAAA,EAAA,MAAA,CAAA;AADA,iBA51BA;AAAA;AAAA,uBAg2BA,KAAA,KAAA,CAAA,IAAA,CAAA,kCAAA,EAAA,MAAA,CAh2BA;;AAAA;AAg2BA,gBAAA,QAh2BA;;AAAA,qBAk2BA,QAAA,CAAA,OAl2BA;AAAA;AAAA;AAAA;;AAm2BA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,qBAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA,EAn2BA,CAy2BA;;AAz2BA;AAAA,uBA02BA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,mBAAA,EAFA,CAAA,CA12BA;;AAAA;AAAA;AAAA;;AAAA;AA+2BA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,QAAA,CAAA,OAAA,IAAA,YAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;;AA/2BA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAs3BA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA;;AAEA,oBAAA,cAAA,QAAA,IAAA,cAAA,QAAA,CAAA,IAAA,IAAA,cAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,cAAA,QAAA,CAAA,IAAA,CAAA,OAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,cAAA,OAAA,EAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,cAAA,OAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,YAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA;;AA14BA;AAAA;AA44BA,qBAAA,aAAA,GAAA,KAAA;AA54BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAg5BA;AACA,IAAA,YAj5BA,wBAi5BA,GAj5BA,EAi5BA;AACA,UAAA,GAAA,KAAA,IAAA,IAAA,GAAA,KAAA,SAAA,EAAA,OAAA,GAAA;AACA,UAAA,MAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,EAAA,OAAA,GAAA,CAHA,CAKA;;AACA,UAAA,GAAA,KAAA,KAAA,aAAA,EAAA;AACA,eAAA,MAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,qBAAA,EAAA,CADA;AAEA,UAAA,qBAAA,EAAA;AAFA,SAAA,CAAA;AAIA,OAXA,CAaA;;;AACA,aAAA,MAAA,CAAA,cAAA,CAAA,OAAA,CAAA;AACA,KAh6BA;AAk6BA;AACA,IAAA,kBAn6BA,8BAm6BA,QAn6BA,EAm6BA;AACA,cAAA,QAAA;AACA,aAAA,KAAA;AACA,iBAAA,OAAA;;AACA,aAAA,MAAA;AACA,iBAAA,QAAA;;AACA,aAAA,MAAA;AACA;AACA,iBAAA,MAAA;AAPA;AASA,KA76BA;AA+6BA;AACA,IAAA,kBAh7BA,8BAg7BA,MAh7BA,EAg7BA;AACA,UAAA,YAAA,GAAA,MAAA,CAAA,aAAA;AACA,UAAA,QAAA,GAAA,MAAA,CAAA,SAAA,CAFA,CAIA;;AACA,UAAA,eAAA,GAAA,KAAA,eAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,SAAA,KAAA,QAAA;AAAA,OAAA,CAAA;AACA,UAAA,YAAA,GAAA,eAAA,CAAA,SAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,EAAA,KAAA,MAAA,CAAA,EAAA;AAAA,OAAA,CAAA;AACA,UAAA,UAAA,GAAA,eAAA,CAAA,YAAA,GAAA,CAAA,CAAA;;AAEA,UAAA,QAAA,KAAA,MAAA,EAAA;AACA,eAAA,KAAA;AACA;;AAEA,UAAA,UAAA,EAAA;AACA,YAAA,YAAA,KAAA,CAAA,EAAA;AACA,6BAAA,UAAA,CAAA,aAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,2BAAA,YAAA,cAAA,UAAA,CAAA,aAAA,GAAA,CAAA;AACA;AACA,OANA,MAMA;AACA,yBAAA,YAAA;AACA;AACA;AAt8BA;AA1KA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"affiliate-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">邀请奖励</h1>\n        <p class=\"simple-subtitle\">邀请好友注册智界AIGC，获得丰厚奖励</p>\n        <div class=\"commission-badge\">\n          <span class=\"badge-text\">当前奖励比例：{{ currentCommissionRate }}%</span>\n          <span class=\"badge-level\">{{ commissionLevelText }}</span>\n        </div>\n      </div>\n\n      <!-- 分销内容区域 -->\n      <section class=\"affiliate-section\">\n        <div class=\"container\">\n          <!-- 邀请链接区域 - 最显眼位置 -->\n          <div class=\"promotion-link-section\">\n            <h2 class=\"section-title\">您的专属邀请链接</h2>\n            <div class=\"link-main-container\">\n              <div class=\"link-input-large\">\n                <a-input\n                  :value=\"affiliateLink || '正在生成邀请链接...'\"\n                  readonly\n                  :loading=\"loading\"\n                  size=\"large\"\n                  placeholder=\"邀请链接生成中...\"\n                />\n              </div>\n              <div class=\"link-actions\">\n                <a-button\n                  type=\"primary\"\n                  size=\"large\"\n                  :disabled=\"!affiliateLink || loading\"\n                  @click=\"copyLink\"\n                  class=\"copy-btn\"\n                >\n                  <a-icon type=\"copy\" />\n                  复制链接\n                </a-button>\n                <a-button\n                  size=\"large\"\n                  :loading=\"qrLoading\"\n                  @click=\"generateQRCode\"\n                  class=\"qr-btn\"\n                >\n                  <a-icon type=\"qrcode\" />\n                  邀请二维码\n                </a-button>\n              </div>\n            </div>\n            <div class=\"link-tips\">\n              <a-icon type=\"info-circle\" />\n              分享此链接，您将获得好友付费的 <strong>{{ currentCommissionRate }}%</strong> 奖励\n            </div>\n          </div>\n\n          <!-- 收益展示 -->\n          <div class=\"earnings-dashboard\">\n            <h2 class=\"section-title\">收益概览</h2>\n            <div class=\"earnings-grid\">\n              <div class=\"earning-card primary\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"dollar\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(totalEarnings) }}</div>\n                    <div class=\"earning-label\">累计收益</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card success\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"wallet\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">¥{{ formatNumber(availableEarnings) }}</div>\n                    <div class=\"earning-label\">可提现金额</div>\n                  </a-spin>\n                  <div class=\"card-action\">\n                    <a-button\n                      type=\"primary\"\n                      size=\"small\"\n                      :disabled=\"availableEarnings <= 0 || loading\"\n                      @click=\"openWithdrawModal\"\n                    >\n                      立即提现\n                    </a-button>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"earning-card info\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"team\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ Math.floor(totalReferrals) }}</div>\n                    <div class=\"earning-label\">邀请注册人数</div>\n                  </a-spin>\n                </div>\n              </div>\n\n              <div class=\"earning-card warning\">\n                <div class=\"card-icon\">\n                  <a-icon type=\"crown\" />\n                </div>\n                <div class=\"card-content\">\n                  <a-spin :spinning=\"loading\" size=\"small\">\n                    <div class=\"earning-number\">{{ Math.floor(memberReferrals) }}</div>\n                    <div class=\"earning-label\">转化人数</div>\n                  </a-spin>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 奖励等级进度 -->\n          <div class=\"commission-progress\">\n            <h2 class=\"section-title\">奖励等级进度</h2>\n            <div class=\"progress-card\">\n              <!-- 一行显示所有等级 -->\n              <div class=\"level-timeline-horizontal\">\n                <div\n                  v-for=\"(level, index) in commissionLevels\"\n                  :key=\"index\"\n                  class=\"level-step-horizontal\"\n                  :class=\"{\n                    'current': level.isCurrent,\n                    'completed': level.isCompleted,\n                    'upcoming': level.isUpcoming\n                  }\"\n                >\n                  <div class=\"step-circle-horizontal\">\n                    <a-icon v-if=\"level.isCompleted\" type=\"check\" />\n                    <span v-else-if=\"level.isCurrent\" class=\"current-dot\"></span>\n                    <span v-else class=\"step-number\">{{ index + 1 }}</span>\n                  </div>\n                  <div class=\"step-content-horizontal\">\n                    <div class=\"step-title\">{{ level.name }}</div>\n                    <div class=\"step-rate\">{{ level.rate }}%</div>\n                    <div class=\"step-requirement\">{{ level.requirement }}人</div>\n                    <div v-if=\"level.remaining > 0\" class=\"step-remaining\">\n                      还需{{ level.remaining }}个\n                    </div>\n                    <div v-else-if=\"level.isCompleted\" class=\"step-completed\">\n                      已达成\n                    </div>\n                  </div>\n                  <!-- 连接线 -->\n                  <div v-if=\"index < commissionLevels.length - 1\" class=\"step-line-horizontal\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 奖励规则说明 -->\n          <div class=\"commission-rules\">\n            <h2 class=\"section-title\">奖励规则</h2>\n            <div class=\"rules-table\">\n              <div class=\"rule-row header\">\n                <div class=\"rule-cell\">用户等级</div>\n                <div class=\"rule-cell\">邀请人数要求</div>\n                <div class=\"rule-cell\">奖励比例</div>\n                <div class=\"rule-cell\">说明</div>\n              </div>\n              <a-spin :spinning=\"loading\" size=\"small\">\n                <div\n                  v-for=\"config in allLevelConfigs\"\n                  :key=\"config.id\"\n                  class=\"rule-row\"\n                  :class=\"{\n                    'vip': config.role_code === 'VIP',\n                    'svip': config.role_code === 'SVIP'\n                  }\"\n                >\n                  <div class=\"rule-cell\">{{ getRoleDisplayName(config.role_code) }}</div>\n                  <div class=\"rule-cell\">{{ getRequirementText(config) }}</div>\n                  <div class=\"rule-cell highlight\">{{ config.commission_rate }}%</div>\n                  <div class=\"rule-cell\">{{ config.level_name }}</div>\n                </div>\n              </a-spin>\n            </div>\n          </div>\n\n          <!-- 邀请用户列表 -->\n          <div class=\"referral-users\">\n            <h2 class=\"section-title\">我的邀请用户</h2>\n            <div class=\"users-table-container\">\n              <a-table\n                :columns=\"userColumns\"\n                :data-source=\"referralUsers\"\n                :loading=\"usersLoading\"\n                :pagination=\"usersPagination\"\n                size=\"middle\"\n                @change=\"handleUsersTableChange\"\n              >\n                <template slot=\"avatar\" slot-scope=\"text, record\">\n                  <a-avatar :src=\"getAvatarUrl(record.avatar)\" :style=\"{ backgroundColor: '#87d068' }\">\n                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}\n                  </a-avatar>\n                </template>\n                <template slot=\"reward\" slot-scope=\"text\">\n                  <span class=\"reward-amount\">¥{{ text || '0.00' }}</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n\n          <!-- 提现记录 -->\n          <div class=\"withdraw-records\">\n            <h2 class=\"section-title\">提现记录</h2>\n\n            <!-- 筛选区域 -->\n            <div class=\"filter-section\" style=\"margin-bottom: 16px; padding: 16px; background: #fafafa; border-radius: 6px;\">\n              <a-row :gutter=\"16\">\n                <a-col :span=\"5\">\n                  <a-form-item label=\"提现金额\">\n                    <a-input-group compact>\n                      <a-input-number\n                        v-model=\"withdrawFilter.minAmount\"\n                        placeholder=\"最小金额\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        style=\"width: 50%\"\n                      />\n                      <a-input-number\n                        v-model=\"withdrawFilter.maxAmount\"\n                        placeholder=\"最大金额\"\n                        :min=\"0\"\n                        :precision=\"2\"\n                        style=\"width: 50%\"\n                      />\n                    </a-input-group>\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\"申请时间\">\n                    <a-range-picker\n                      v-model=\"withdrawFilter.dateRange\"\n                      format=\"YYYY-MM-DD\"\n                      placeholder=\"['开始日期', '结束日期']\"\n                      style=\"width: 100%\"\n                    />\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"4\">\n                  <a-form-item label=\"状态\">\n                    <a-select\n                      v-model=\"withdrawFilter.status\"\n                      placeholder=\"选择状态\"\n                      style=\"width: 100%\"\n                    >\n                      <a-select-option :value=\"null\">全部</a-select-option>\n                      <a-select-option :value=\"1\">待审核</a-select-option>\n                      <a-select-option :value=\"2\">已发放</a-select-option>\n                      <a-select-option :value=\"3\">审核拒绝</a-select-option>\n                      <a-select-option :value=\"4\">已取消</a-select-option>\n                    </a-select>\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\"完成时间\">\n                    <a-range-picker\n                      v-model=\"withdrawFilter.completeDateRange\"\n                      format=\"YYYY-MM-DD\"\n                      placeholder=\"['开始日期', '结束日期']\"\n                      style=\"width: 100%\"\n                    />\n                  </a-form-item>\n                </a-col>\n\n                <a-col :span=\"5\">\n                  <a-form-item label=\" \">\n                    <a-button type=\"primary\" @click=\"handleWithdrawFilter\" :loading=\"recordsLoading\" style=\"margin-right: 8px;\">\n                      搜索\n                    </a-button>\n                    <a-button @click=\"handleWithdrawReset\">重置</a-button>\n                  </a-form-item>\n                </a-col>\n              </a-row>\n            </div>\n\n            <div class=\"records-table-container\">\n              <a-table\n                :columns=\"withdrawColumns\"\n                :data-source=\"withdrawRecords\"\n                :loading=\"recordsLoading\"\n                :pagination=\"withdrawPagination\"\n                size=\"middle\"\n                @change=\"handleWithdrawTableChange\"\n              >\n                <template slot=\"status\" slot-scope=\"text\">\n                  <a-tag :color=\"getStatusColor(text)\">\n                    {{ text }}\n                  </a-tag>\n                </template>\n                <template slot=\"amount\" slot-scope=\"text\">\n                  <span class=\"withdraw-amount\">¥{{ text }}</span>\n                </template>\n                <template slot=\"action\" slot-scope=\"text, record\">\n                  <a-button\n                    v-if=\"record.rawStatus === 1\"\n                    type=\"danger\"\n                    size=\"small\"\n                    :loading=\"cancelLoading\"\n                    @click=\"handleCancelWithdraw(record)\"\n                  >\n                    取消提现\n                  </a-button>\n                  <span v-else>-</span>\n                </template>\n              </a-table>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 二维码弹窗 -->\n      <a-modal\n        v-model=\"showQRModal\"\n        title=\"邀请二维码\"\n        :footer=\"null\"\n        width=\"400px\"\n        centered\n      >\n        <div class=\"qr-modal-content\">\n          <div class=\"qr-code-container\" v-if=\"qrCodeUrl\">\n            <img :src=\"qrCodeUrl\" alt=\"邀请二维码\" class=\"qr-code-image\" />\n          </div>\n          <div class=\"qr-actions\">\n            <a-button type=\"primary\" block @click=\"downloadQRCode\" v-if=\"qrCodeUrl\">\n              <a-icon type=\"download\" />\n              下载二维码\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n\n      <!-- 提现弹窗 -->\n      <a-modal\n        v-model=\"showWithdrawModal\"\n        title=\"申请提现\"\n        :footer=\"null\"\n        width=\"500px\"\n        centered\n      >\n        <div class=\"withdraw-modal-content\">\n          <div class=\"withdraw-info\">\n            <div class=\"info-item\">\n              <span class=\"info-label\">可提现金额：</span>\n              <span class=\"info-value\">¥{{ formatNumber(availableEarnings) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"info-label\">最低提现金额：</span>\n              <span class=\"info-value\">¥50.00</span>\n            </div>\n          </div>\n\n          <a-form :form=\"withdrawForm\" @submit=\"handleWithdraw\">\n            <a-form-item label=\"提现金额\">\n              <a-input-number\n                v-decorator=\"['amount', {\n                  rules: [\n                    { required: true, message: '请输入提现金额' },\n                    { type: 'number', min: 50, message: '最低提现金额为50元' },\n                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                  ]\n                }]\"\n                :min=\"50\"\n                :max=\"availableEarnings\"\n                :precision=\"2\"\n                style=\"width: 100%\"\n                placeholder=\"请输入提现金额\"\n              >\n                <template slot=\"addonAfter\">元</template>\n              </a-input-number>\n            </a-form-item>\n\n            <a-form-item label=\"提现方式\">\n              <a-select\n                v-decorator=\"['method', {\n                  rules: [{ required: true, message: '请选择提现方式' }],\n                  initialValue: 'alipay'\n                }]\"\n                placeholder=\"请选择提现方式\"\n                disabled\n              >\n                <a-select-option value=\"alipay\">支付宝</a-select-option>\n              </a-select>\n            </a-form-item>\n\n            <a-form-item label=\"支付宝手机号\">\n              <a-input\n                v-decorator=\"['alipayAccount', {\n                  rules: [\n                    { required: true, message: '请输入支付宝手机号' },\n                    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                  ]\n                }]\"\n                placeholder=\"请输入支付宝手机号\"\n              />\n            </a-form-item>\n\n            <a-form-item label=\"收款人真实姓名\">\n              <a-input\n                v-decorator=\"['realName', {\n                  rules: [\n                    { required: true, message: '请输入收款人真实姓名' },\n                    { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                  ]\n                }]\"\n                placeholder=\"请输入收款人真实姓名\"\n              />\n            </a-form-item>\n          </a-form>\n\n          <div class=\"withdraw-actions\">\n            <a-button @click=\"showWithdrawModal = false\" style=\"margin-right: 8px\">\n              取消\n            </a-button>\n            <a-button\n              type=\"primary\"\n              :loading=\"withdrawLoading\"\n              @click=\"handleWithdraw\"\n            >\n              申请提现\n            </a-button>\n          </div>\n        </div>\n      </a-modal>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport { getReferralStats, generateReferralLink, getUserRole, getLevelConfig } from '@/api/usercenter'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport Vue from 'vue'\n\nexport default {\n  name: 'Affiliate',\n  components: {\n    WebsitePage\n  },\n  data() {\n    return {\n      loading: true,\n      qrLoading: false,\n\n      // 收益数据\n      totalEarnings: 0,\n      availableEarnings: 0,\n      totalReferrals: 0,\n      memberReferrals: 0,\n\n      // 邀请链接\n      affiliateLink: '',\n\n      // 佣金等级\n      userRole: 'user', // user, VIP, SVIP\n      currentCommissionRate: 30,\n      commissionLevelText: '新手邀请员',\n      levelProgress: 0,\n      nextLevelRequirement: 10,\n      nextLevelText: '高级邀请员',\n      nextLevelRate: 40,\n      progressColor: '#1890ff',\n\n      // 佣金等级配置\n      commissionLevels: [],\n      allLevelConfigs: [], // 从数据库获取的完整等级配置\n\n      // 二维码\n      showQRModal: false,\n      qrCodeUrl: '',\n      qrPreGenerated: false, // 是否已预生成二维码\n\n      // 提现\n      showWithdrawModal: false,\n      withdrawLoading: false,\n      withdrawForm: this.$form.createForm(this),\n\n      // 邀请用户列表\n      referralUsers: [],\n      usersLoading: false,\n      usersPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n      },\n      // 邀请用户排序\n      usersSort: {\n        orderBy: 'total_reward',\n        order: 'desc'\n      },\n      defaultAvatar: '/default-avatar.png', // 本地降级头像\n\n      // 提现记录\n      withdrawRecords: [],\n      recordsLoading: false,\n      cancelLoading: false,\n      withdrawPagination: {\n        current: 1,\n        pageSize: 10,\n        total: 0,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\n      },\n      // 提现记录排序\n      withdrawSort: {\n        orderBy: 'apply_time',\n        order: 'desc'\n      },\n      // 提现记录筛选\n      withdrawFilter: {\n        minAmount: null,\n        maxAmount: null,\n        status: null, // null表示\"全部\"\n        dateRange: [],\n        completeDateRange: []\n      },\n\n      // 用户信息\n      userInfo: null\n    }\n  },\n  computed: {\n    // 动态计算表格列配置，确保排序状态响应式更新\n    userColumns() {\n      return [\n        {\n          title: '头像',\n          dataIndex: 'avatar',\n          key: 'avatar',\n          scopedSlots: { customRender: 'avatar' },\n          width: 80\n        },\n        {\n          title: '用户昵称',\n          dataIndex: 'nickname',\n          key: 'nickname',\n          sorter: true\n        },\n        {\n          title: '注册时间',\n          dataIndex: 'registerTime',\n          key: 'registerTime',\n          sorter: true\n        },\n        {\n          title: '获得奖励',\n          dataIndex: 'reward',\n          key: 'reward',\n          scopedSlots: { customRender: 'reward' },\n          sorter: true\n        }\n      ]\n    },\n\n    // 动态计算提现记录表格列配置，确保排序状态响应式更新\n    withdrawColumns() {\n      return [\n        {\n          title: '提现金额',\n          dataIndex: 'amount',\n          key: 'amount',\n          scopedSlots: { customRender: 'amount' },\n          sorter: true\n        },\n        {\n          title: '提现方式',\n          dataIndex: 'method',\n          key: 'method'\n        },\n        {\n          title: '申请时间',\n          dataIndex: 'applyTime',\n          key: 'applyTime',\n          sorter: true\n        },\n        {\n          title: '状态',\n          dataIndex: 'status',\n          key: 'status',\n          scopedSlots: { customRender: 'status' },\n          sorter: true\n        },\n        {\n          title: '完成时间',\n          dataIndex: 'completeTime',\n          key: 'completeTime',\n          sorter: true\n        },\n        {\n          title: '操作',\n          key: 'action',\n          width: 100,\n          scopedSlots: { customRender: 'action' }\n        }\n      ]\n    }\n  },\n  async mounted() {\n    await this.checkLoginAndLoadData()\n  },\n  methods: {\n    // 获取头像URL（处理CDN路径和默认头像）\n    getAvatarUrl(avatar) {\n      if (!avatar) {\n        return this.defaultAvatar\n      }\n\n      // 如果是完整的URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是相对路径，使用getFileAccessHttpUrl转换\n      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar\n    },\n\n    // 处理文件访问URL（和其他组件保持一致）\n    getFileAccessHttpUrl(avatar) {\n      if (!avatar) return this.defaultAvatar\n\n      // 如果已经是完整URL，直接返回\n      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {\n        return avatar\n      }\n\n      // 如果是TOS文件，使用全局方法\n      if (avatar.startsWith('uploads/')) {\n        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar\n      }\n\n      // 本地文件，使用静态域名\n      const staticDomain = this.$store.state.app.staticDomainURL\n      return staticDomain ? `${staticDomain}/${avatar}` : avatar\n    },\n\n    // 加载TOS默认头像URL\n    async loadDefaultAvatar() {\n      try {\n        const response = await this.$http.get('/sys/common/default-avatar-url')\n        if (response && response.success && response.result) {\n          this.defaultAvatar = response.result\n          console.log('🎯 Affiliate: 已加载TOS默认头像:', this.defaultAvatar)\n        }\n      } catch (error) {\n        console.warn('⚠️ Affiliate: 获取TOS默认头像失败，使用本地降级:', error)\n        // 保持本地默认头像作为降级方案\n      }\n    },\n\n    // 检查登录状态并加载数据\n    async checkLoginAndLoadData() {\n      const token = Vue.ls.get(ACCESS_TOKEN)\n      if (!token) {\n        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })\n        return\n      }\n\n      try {\n        await Promise.all([\n          this.loadReferralData(),\n          this.loadReferralLink(),\n          this.loadUserRole(),\n          this.loadLevelConfig(),\n          this.loadReferralUsers(),\n          this.loadWithdrawRecords(),\n          this.loadDefaultAvatar()\n        ])\n\n        // 计算佣金等级\n        this.calculateCommissionLevel()\n\n        // 自动预生成邀请二维码\n        this.preGenerateQRCode()\n      } catch (error) {\n        console.error('加载分销数据失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取分销数据失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载推荐统计数据\n    async loadReferralData() {\n      try {\n        const response = await getReferralStats()\n        if (response.success) {\n          const data = response.result\n          this.totalEarnings = data.total_reward_amount || 0\n          this.availableEarnings = data.available_rewards || 0\n          this.totalReferrals = data.total_referrals || 0\n          this.memberReferrals = data.member_referrals || 0\n        }\n      } catch (error) {\n        console.error('获取推荐统计失败:', error)\n        throw error\n      }\n    },\n\n    // 加载推荐链接\n    async loadReferralLink() {\n      try {\n        const response = await generateReferralLink({})\n        if (response.success) {\n          this.affiliateLink = response.result || ''\n        }\n      } catch (error) {\n        console.error('获取推荐链接失败:', error)\n        // 如果获取失败，使用默认链接格式\n        this.affiliateLink = `${window.location.origin}?ref=loading...`\n      }\n    },\n\n    // 加载用户角色信息\n    async loadUserRole() {\n      try {\n        const response = await getUserRole()\n        if (response.success) {\n          this.userRole = response.result.role_code || 'user'\n        }\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = 'user'\n      }\n    },\n\n    // 加载等级配置信息\n    async loadLevelConfig() {\n      try {\n        const response = await getLevelConfig()\n        if (response.success) {\n          this.allLevelConfigs = response.result || []\n        }\n      } catch (error) {\n        console.error('获取等级配置失败:', error)\n        this.allLevelConfigs = []\n      }\n    },\n\n    // 计算佣金等级和进度\n    calculateCommissionLevel() {\n      const memberCount = this.memberReferrals\n\n      // 从数据库配置中获取当前用户角色的等级配置\n      const userLevelConfigs = this.allLevelConfigs.filter(config => config.role_code === this.userRole)\n\n      if (userLevelConfigs.length === 0) {\n        console.warn('未找到用户角色的等级配置:', this.userRole)\n        return\n      }\n\n      // 根据邀请人数确定当前等级\n      let currentLevel = null\n      for (let i = userLevelConfigs.length - 1; i >= 0; i--) {\n        if (memberCount >= userLevelConfigs[i].min_referrals) {\n          currentLevel = userLevelConfigs[i]\n          break\n        }\n      }\n\n      if (!currentLevel) {\n        currentLevel = userLevelConfigs[0] // 默认最低等级\n      }\n\n      // 设置当前等级信息\n      this.currentCommissionRate = parseFloat(currentLevel.commission_rate)\n      this.commissionLevelText = currentLevel.level_name\n\n      // 查找下一个等级\n      const nextLevel = userLevelConfigs.find(config => config.min_referrals > memberCount)\n\n      if (nextLevel) {\n        this.nextLevelRequirement = nextLevel.min_referrals\n        this.nextLevelText = nextLevel.level_name\n        this.nextLevelRate = parseFloat(nextLevel.commission_rate)\n        this.levelProgress = (memberCount / nextLevel.min_referrals) * 100\n        this.progressColor = '#1890ff'\n      } else {\n        // 已达最高等级\n        this.nextLevelRequirement = 0\n        this.nextLevelText = '已达最高等级'\n        this.nextLevelRate = this.currentCommissionRate\n        this.levelProgress = 100\n        this.progressColor = '#722ed1'\n      }\n\n      // 生成等级进度显示数据\n      this.commissionLevels = userLevelConfigs.map((config, index) => {\n        const isCompleted = memberCount >= config.min_referrals\n\n        // 判断当前等级：如果不是已完成，且满足前一个等级的要求，则为当前等级\n        let isCurrent = false\n        if (!isCompleted) {\n          if (index === 0) {\n            // 第一个等级，如果没完成就是当前等级\n            isCurrent = true\n          } else {\n            // 其他等级，如果满足前一个等级要求但不满足当前等级要求，则为当前等级\n            const prevRequirement = userLevelConfigs[index - 1].min_referrals\n            isCurrent = memberCount >= prevRequirement\n          }\n        }\n\n        const isUpcoming = !isCompleted && !isCurrent\n\n        let remaining = 0\n        if (!isCompleted) {\n          remaining = config.min_referrals - memberCount\n        }\n\n        return {\n          name: config.level_name,\n          rate: parseFloat(config.commission_rate),\n          requirement: config.min_referrals,\n          isCompleted,\n          isCurrent,\n          isUpcoming,\n          remaining: remaining > 0 ? remaining : 0\n        }\n      })\n    },\n\n     // 复制邀请链接\n     copyLink() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '邀请链接正在生成中，请稍后再试',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      navigator.clipboard.writeText(this.affiliateLink).then(() => {\n        this.$notification.success({\n          message: '邀请链接已复制',\n          description: '邀请链接已成功复制到剪贴板，快去分享给好友吧！',\n          placement: 'topRight',\n          duration: 3,\n          style: {\n            width: '380px',\n            marginTop: '101px',\n            borderRadius: '8px',\n            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'\n          }\n        })\n      }).catch(() => {\n        this.$notification.error({\n          message: '复制失败',\n          description: '复制邀请链接失败，请手动复制',\n          placement: 'topRight'\n        })\n      })\n    },\n\n    // 预生成邀请二维码（后台静默生成）\n    async preGenerateQRCode() {\n      if (!this.affiliateLink || this.qrPreGenerated) {\n        return\n      }\n\n      try {\n        console.log('开始预生成邀请二维码...')\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        if (response && response.success) {\n          // 静默保存二维码URL\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          console.log('邀请二维码预生成成功:', this.qrCodeUrl)\n        }\n      } catch (error) {\n        console.error('预生成二维码失败:', error)\n        // 预生成失败不显示错误提示，用户点击时再重试\n      }\n    },\n\n    // 生成邀请二维码（用户主动点击）\n    async generateQRCode() {\n      if (!this.affiliateLink) {\n        this.$notification.warning({\n          message: '邀请链接未生成',\n          description: '请等待邀请链接生成完成后再生成二维码',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 如果已经预生成，直接显示\n      if (this.qrPreGenerated && this.qrCodeUrl) {\n        this.showQRModal = true\n        return\n      }\n\n      try {\n        this.qrLoading = true\n\n        // 调用后端API生成二维码并上传到TOS\n        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {\n          params: {\n            url: this.affiliateLink\n          }\n        })\n\n        console.log('二维码生成响应:', response)\n\n        if (response && response.success) {\n          // 使用CDN地址\n          this.qrCodeUrl = response.result\n          this.qrPreGenerated = true\n          this.showQRModal = true\n\n          this.$notification.success({\n            message: '二维码生成成功',\n            description: '邀请二维码已生成并存储到CDN，可以下载保存',\n            placement: 'topRight'\n          })\n        } else {\n          const errorMsg = (response && response.message) || '生成失败'\n          throw new Error(errorMsg)\n        }\n      } catch (error) {\n        console.error('生成二维码失败:', error)\n        this.$notification.error({\n          message: '生成失败',\n          description: error.message || '二维码生成失败，请稍后重试',\n          placement: 'topRight'\n        })\n      } finally {\n        this.qrLoading = false\n      }\n    },\n\n    // 下载二维码\n    downloadQRCode() {\n      if (!this.qrCodeUrl) return\n\n      try {\n        // 从邀请链接中提取邀请码\n        const referralCode = this.extractReferralCode(this.affiliateLink)\n\n        // 通过后端代理下载，避免CORS问题\n        const backendUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/jeecg-boot'\n        const downloadUrl = `${backendUrl}/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}&code=${referralCode}&t=${Date.now()}`\n\n        console.log('下载URL:', downloadUrl)\n\n        // 使用隐藏iframe下载，避免页面跳动\n        const iframe = document.createElement('iframe')\n        iframe.style.display = 'none'\n        iframe.style.position = 'absolute'\n        iframe.style.left = '-9999px'\n        iframe.src = downloadUrl\n        document.body.appendChild(iframe)\n\n        // 3秒后移除iframe\n        setTimeout(() => {\n          if (iframe.parentNode) {\n            document.body.removeChild(iframe)\n          }\n        }, 3000)\n\n        this.$notification.success({\n          message: '下载开始',\n          description: `邀请二维码_${referralCode}.png 正在下载`,\n          placement: 'topRight'\n        })\n      } catch (error) {\n        console.error('下载二维码失败:', error)\n        this.$notification.error({\n          message: '下载失败',\n          description: '二维码下载失败，请稍后重试',\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 从邀请链接中提取邀请码\n    extractReferralCode(url) {\n      if (!url) return 'UNKNOWN'\n\n      try {\n        const urlObj = new URL(url)\n        const refParam = urlObj.searchParams.get('ref')\n        return refParam || 'UNKNOWN'\n      } catch (error) {\n        console.error('提取邀请码失败:', error)\n        return 'UNKNOWN'\n      }\n    },\n\n    // 显示提现弹窗\n    openWithdrawModal() {\n      if (this.availableEarnings < 50) {\n        this.$notification.warning({\n          message: '提现金额不足',\n          description: '最低提现金额为50元，请继续邀请获得更多收益',\n          placement: 'topRight'\n        })\n        return\n      }\n      this.showWithdrawModal = true\n    },\n\n    // 处理提现申请\n    async handleWithdraw() {\n      this.withdrawForm.validateFields(async (err, values) => {\n        if (err) return\n\n        // 二次确认弹窗\n        const h = this.$createElement\n        this.$confirm({\n          title: '确认提现申请',\n          content: h('div', { style: { margin: '16px 0' } }, [\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '提现金额：'),\n              `¥${values.amount}`\n            ]),\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '支付宝账号：'),\n              values.alipayAccount\n            ]),\n            h('p', { style: { marginBottom: '8px' } }, [\n              h('strong', '收款人姓名：'),\n              values.realName\n            ]),\n            h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [\n              h('strong', '注意：'),\n              '请再次核实一遍提现信息，请确认信息无误！'\n            ])\n          ]),\n          okText: '确认提现',\n          cancelText: '取消',\n          centered: true,\n          width: 400,\n          onOk: async () => {\n            await this.submitWithdrawRequest(values)\n          }\n        })\n      })\n    },\n\n    // 提交提现申请\n    async submitWithdrawRequest(values) {\n      this.withdrawLoading = true\n\n      try {\n        const params = {\n          withdrawalAmount: values.amount,\n          realName: values.realName,\n          alipayAccount: values.alipayAccount\n        }\n\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n\n        if (response.success) {\n          this.withdrawLoading = false\n          this.showWithdrawModal = false\n          this.withdrawForm.resetFields()\n\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账',\n            placement: 'topRight'\n          })\n\n          // 刷新数据\n          await Promise.all([\n            this.loadReferralData(),\n            this.loadWithdrawRecords()\n          ])\n        } else {\n          this.withdrawLoading = false\n          this.$notification.error({\n            message: '提现申请失败',\n            description: response.message || '申请失败，请重试',\n            placement: 'topRight'\n          })\n        }\n      } catch (error) {\n        this.withdrawLoading = false\n        console.error('提现申请失败:', error)\n\n        // 检查是否是HTTP响应错误，如果是则显示后端返回的错误信息\n        if (error.response && error.response.data && error.response.data.message) {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: error.response.data.message,\n            placement: 'topRight'\n          })\n        } else if (error.message) {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: error.message,\n            placement: 'topRight'\n          })\n        } else {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: '网络错误，请稍后重试',\n            placement: 'topRight'\n          })\n        }\n      }\n    },\n\n    // 加载邀请用户列表\n    async loadReferralUsers() {\n      try {\n        this.usersLoading = true\n\n        const params = {\n          current: (this.usersPagination && this.usersPagination.current) || 1,\n          size: (this.usersPagination && this.usersPagination.pageSize) || 10,\n          orderBy: (this.usersSort && this.usersSort.orderBy) || 'total_reward',\n          order: (this.usersSort && this.usersSort.order) || 'desc'\n        }\n\n        console.log('加载邀请用户参数:', params)\n\n        const response = await this.$http.get('/api/usercenter/referralList', { params })\n\n        if (response && response.success) {\n          const result = response.result || {}\n          const records = result.records || []\n\n          // 更新分页信息\n          if (this.usersPagination) {\n            this.usersPagination.total = result.total || 0\n          }\n\n          // 转换数据格式\n          this.referralUsers = records.map((item, index) => ({\n            key: item.id || index,\n            nickname: item.referee_nickname || `用户***${index + 1}`,\n            avatar: item.referee_avatar || '',\n            registerTime: item.register_time || '',\n            reward: item.total_reward || '0.00'\n          }))\n        } else {\n          this.referralUsers = []\n          if (this.usersPagination) {\n            this.usersPagination.total = 0\n          }\n        }\n      } catch (error) {\n        console.error('获取邀请用户列表失败:', error)\n        this.referralUsers = []\n        // 如果是网络错误或其他错误，显示友好提示\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n        }\n      } finally {\n        this.usersLoading = false\n      }\n    },\n\n    // 加载提现记录\n    async loadWithdrawRecords() {\n      try {\n        this.recordsLoading = true\n\n        const params = {\n          current: (this.withdrawPagination && this.withdrawPagination.current) || 1,\n          size: (this.withdrawPagination && this.withdrawPagination.pageSize) || 10,\n          orderBy: (this.withdrawSort && this.withdrawSort.orderBy) || 'apply_time',\n          order: (this.withdrawSort && this.withdrawSort.order) || 'desc'\n        }\n\n        // 添加筛选参数\n        if (this.withdrawFilter) {\n          if (this.withdrawFilter.minAmount !== null && this.withdrawFilter.minAmount !== '') {\n            params.minAmount = this.withdrawFilter.minAmount\n          }\n          if (this.withdrawFilter.maxAmount !== null && this.withdrawFilter.maxAmount !== '') {\n            params.maxAmount = this.withdrawFilter.maxAmount\n          }\n          if (this.withdrawFilter.status !== null) {\n            params.status = this.withdrawFilter.status\n          }\n          if (this.withdrawFilter.dateRange && this.withdrawFilter.dateRange.length === 2) {\n            // 处理moment对象，转换为YYYY-MM-DD格式\n            params.startDate = this.withdrawFilter.dateRange[0].format\n              ? this.withdrawFilter.dateRange[0].format('YYYY-MM-DD')\n              : this.withdrawFilter.dateRange[0]\n            params.endDate = this.withdrawFilter.dateRange[1].format\n              ? this.withdrawFilter.dateRange[1].format('YYYY-MM-DD')\n              : this.withdrawFilter.dateRange[1]\n          }\n          if (this.withdrawFilter.completeDateRange && this.withdrawFilter.completeDateRange.length === 2) {\n            // 处理moment对象，转换为YYYY-MM-DD格式\n            params.completeStartDate = this.withdrawFilter.completeDateRange[0].format\n              ? this.withdrawFilter.completeDateRange[0].format('YYYY-MM-DD')\n              : this.withdrawFilter.completeDateRange[0]\n            params.completeEndDate = this.withdrawFilter.completeDateRange[1].format\n              ? this.withdrawFilter.completeDateRange[1].format('YYYY-MM-DD')\n              : this.withdrawFilter.completeDateRange[1]\n          }\n        }\n\n        console.log('加载提现记录参数:', params)\n        console.log('原始筛选条件:', {\n          dateRange: this.withdrawFilter.dateRange,\n          completeDateRange: this.withdrawFilter.completeDateRange\n        })\n\n        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })\n\n        if (response && response.success) {\n          const result = response.result || {}\n          const records = result.records || []\n\n          // 更新分页信息\n          if (this.withdrawPagination) {\n            this.withdrawPagination.total = result.total || 0\n          }\n\n          // 转换数据格式\n          this.withdrawRecords = records.map((item, index) => ({\n            key: item.id || index,\n            id: item.id,\n            amount: item.withdrawal_amount || '0.00',\n            method: item.withdrawalMethod || '支付宝',\n            applyTime: item.apply_time || '',\n            status: this.getWithdrawStatusText(item.status, item.review_remark),\n            rawStatus: item.status,\n            completeTime: item.review_time || '-'\n          }))\n        } else {\n          this.withdrawRecords = []\n          if (this.withdrawPagination) {\n            this.withdrawPagination.total = 0\n          }\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.withdrawRecords = []\n        // 如果是网络错误或其他错误，显示友好提示\n        if (error.response && error.response.status === 401) {\n          this.$message.warning('登录已过期，请重新登录')\n        }\n      } finally {\n        this.recordsLoading = false\n      }\n    },\n\n    // 获取提现状态文本\n    getWithdrawStatusText(status, reviewRemark) {\n      const statusMap = {\n        0: '待审核',\n        1: '待审核',\n        2: '已完成',\n        3: '已拒绝',\n        4: '已取消'\n      }\n      let statusText = statusMap[status] || '未知状态'\n\n      // 如果是已拒绝状态且有拒绝原因，则添加原因\n      if (status === 3 && reviewRemark) {\n        statusText += `（${reviewRemark}）`\n      }\n\n      return statusText\n    },\n\n    // 获取状态颜色\n    getStatusColor(statusText) {\n      // 处理带拒绝原因的状态文本\n      if (statusText.includes('已拒绝')) {\n        return 'red'\n      }\n\n      const colorMap = {\n        '已完成': 'green',\n        '处理中': 'blue',\n        '待审核': 'orange',\n        '已取消': 'gray'\n      }\n      return colorMap[statusText] || 'default'\n    },\n\n    // 获取排序状态（暂时保留，可能后续需要）\n    getSortOrder(field) {\n      if (this.usersSort && this.usersSort.orderBy === field) {\n        return this.usersSort.order === 'asc' ? 'ascend' : 'descend'\n      }\n      return null\n    },\n\n    // 处理邀请用户表格分页变化\n    handleUsersTableChange(pagination, _filters, sorter) {\n      console.log('表格变化:', { pagination, sorter })\n\n      if (this.usersPagination && pagination) {\n        this.usersPagination.current = pagination.current || 1\n        this.usersPagination.pageSize = pagination.pageSize || 10\n      }\n\n      // 处理排序\n      if (sorter && sorter.field && this.usersSort) {\n        const fieldMap = {\n          'nickname': 'nickname',\n          'registerTime': 'register_time',\n          'reward': 'total_reward'\n        }\n\n        const newOrderBy = fieldMap[sorter.field] || 'total_reward'\n\n        // 如果点击的是同一个字段，切换排序方向\n        if (this.usersSort.orderBy === newOrderBy) {\n          this.usersSort.order = this.usersSort.order === 'asc' ? 'desc' : 'asc'\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.usersSort.orderBy = newOrderBy\n          if (newOrderBy === 'total_reward') {\n            this.usersSort.order = 'desc' // 奖励金额默认降序\n          } else {\n            this.usersSort.order = 'asc' // 其他字段默认升序\n          }\n        }\n\n        console.log('排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.usersSort.orderBy,\n          finalOrder: this.usersSort.order,\n          clickedSameField: this.usersSort.orderBy === newOrderBy\n        })\n\n        // 排序时回到第一页\n        if (this.usersPagination) {\n          this.usersPagination.current = 1\n        }\n      }\n\n      this.loadReferralUsers()\n    },\n\n    // 处理提现记录表格分页变化\n    handleWithdrawTableChange(pagination, _filters, sorter) {\n      console.log('提现记录表格变化:', { pagination, sorter })\n\n      if (this.withdrawPagination && pagination) {\n        this.withdrawPagination.current = pagination.current || 1\n        this.withdrawPagination.pageSize = pagination.pageSize || 10\n      }\n\n      // 处理排序\n      if (sorter && sorter.field && this.withdrawSort) {\n        const fieldMap = {\n          'amount': 'withdrawal_amount',\n          'applyTime': 'apply_time',\n          'status': 'status',\n          'completeTime': 'review_time'\n        }\n\n        const newOrderBy = fieldMap[sorter.field] || 'apply_time'\n\n        // 如果点击的是同一个字段，切换排序方向\n        if (this.withdrawSort.orderBy === newOrderBy) {\n          this.withdrawSort.order = this.withdrawSort.order === 'asc' ? 'desc' : 'asc'\n        } else {\n          // 如果是新字段，使用默认排序方向\n          this.withdrawSort.orderBy = newOrderBy\n          if (newOrderBy === 'apply_time') {\n            this.withdrawSort.order = 'desc' // 申请时间默认降序\n          } else if (newOrderBy === 'withdrawal_amount') {\n            this.withdrawSort.order = 'desc' // 金额默认降序\n          } else {\n            this.withdrawSort.order = 'asc' // 其他字段默认升序\n          }\n        }\n\n        console.log('提现记录排序更新:', {\n          field: sorter.field,\n          order: sorter.order,\n          orderBy: this.withdrawSort.orderBy,\n          finalOrder: this.withdrawSort.order\n        })\n\n        // 排序时回到第一页\n        if (this.withdrawPagination) {\n          this.withdrawPagination.current = 1\n        }\n      }\n\n      this.loadWithdrawRecords()\n    },\n\n    // 处理提现记录筛选\n    handleWithdrawFilter() {\n      // 筛选时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1\n      }\n      this.loadWithdrawRecords()\n    },\n\n    // 重置提现记录筛选\n    handleWithdrawReset() {\n      this.withdrawFilter = {\n        minAmount: null,\n        maxAmount: null,\n        status: null,\n        dateRange: [],\n        completeDateRange: []\n      }\n\n      // 重置时回到第一页\n      if (this.withdrawPagination) {\n        this.withdrawPagination.current = 1\n      }\n\n      this.loadWithdrawRecords()\n    },\n\n    // 处理取消提现\n    handleCancelWithdraw(record) {\n      const h = this.$createElement\n      this.$confirm({\n        title: '确认取消提现',\n        content: h('div', { style: { margin: '16px 0' } }, [\n          h('p', { style: { marginBottom: '8px' } }, [\n            h('strong', '提现金额：'),\n            `¥${record.amount}`\n          ]),\n          h('p', { style: { marginBottom: '8px' } }, [\n            h('strong', '申请时间：'),\n            record.applyTime\n          ]),\n          h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [\n            h('strong', '注意：'),\n            '取消后金额将返还到可提现余额，此操作不可撤销！'\n          ])\n        ]),\n        okText: '确认取消',\n        okType: 'danger',\n        cancelText: '返回',\n        centered: true,\n        width: 400,\n        onOk: async () => {\n          await this.confirmCancelWithdraw(record)\n        }\n      })\n    },\n\n    // 确认取消提现\n    async confirmCancelWithdraw(record) {\n      this.cancelLoading = true\n\n      try {\n        const params = {\n          withdrawalId: record.id\n        }\n\n        const response = await this.$http.post('/api/usercenter/cancelWithdrawal', params)\n\n        if (response.success) {\n          this.$notification.success({\n            message: '取消成功',\n            description: '提现申请已取消，金额已返还到可提现余额',\n            placement: 'topRight'\n          })\n\n          // 刷新数据\n          await Promise.all([\n            this.loadReferralData(),\n            this.loadWithdrawRecords()\n          ])\n        } else {\n          this.$notification.error({\n            message: '取消失败',\n            description: response.message || '取消提现失败，请重试',\n            placement: 'topRight'\n          })\n        }\n      } catch (error) {\n        console.error('取消提现失败:', error)\n\n        if (error.response && error.response.data && error.response.data.message) {\n          this.$notification.error({\n            message: '取消失败',\n            description: error.response.data.message,\n            placement: 'topRight'\n          })\n        } else if (error.message) {\n          this.$notification.error({\n            message: '取消失败',\n            description: error.message,\n            placement: 'topRight'\n          })\n        } else {\n          this.$notification.error({\n            message: '取消失败',\n            description: '网络错误，请稍后重试',\n            placement: 'topRight'\n          })\n        }\n      } finally {\n        this.cancelLoading = false\n      }\n    },\n\n     // 格式化数字显示\n     formatNumber(num) {\n       if (num === null || num === undefined) return '0'\n       const number = parseFloat(num)\n       if (isNaN(number)) return '0'\n\n       // 如果是金额，保留两位小数\n       if (num === this.totalEarnings) {\n         return number.toLocaleString('zh-CN', {\n           minimumFractionDigits: 2,\n           maximumFractionDigits: 2\n         })\n       }\n\n       // 其他数字不保留小数\n       return number.toLocaleString('zh-CN')\n     },\n\n     // 获取角色显示名称\n     getRoleDisplayName(roleCode) {\n       switch (roleCode) {\n         case 'VIP':\n           return 'VIP用户'\n         case 'SVIP':\n           return 'SVIP用户'\n         case 'user':\n         default:\n           return '普通用户'\n       }\n     },\n\n     // 获取邀请人数要求文本\n     getRequirementText(config) {\n       const minReferrals = config.min_referrals\n       const roleCode = config.role_code\n\n       // 查找同角色的下一个等级\n       const sameRoleConfigs = this.allLevelConfigs.filter(c => c.role_code === roleCode)\n       const currentIndex = sameRoleConfigs.findIndex(c => c.id === config.id)\n       const nextConfig = sameRoleConfigs[currentIndex + 1]\n\n       if (roleCode === 'SVIP') {\n         return '无要求'\n       }\n\n       if (nextConfig) {\n         if (minReferrals === 0) {\n           return `0-${nextConfig.min_referrals - 1}人`\n         } else {\n           return `${minReferrals}-${nextConfig.min_referrals - 1}人`\n         }\n       } else {\n         return `${minReferrals}人以上`\n       }\n     }\n   }\n }\n</script>\n\n<style scoped>\n.affiliate-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0 0 1.5rem 0;\n}\n\n.commission-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 12px;\n  background: white;\n  padding: 12px 24px;\n  border-radius: 50px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.badge-text {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.badge-level {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n\n/* 分销内容区域 */\n.affiliate-section {\n  padding: 0 0 4rem 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n  text-align: center;\n}\n\n/* 收益仪表板 */\n.earnings-dashboard {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.earnings-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n  gap: 24px;\n}\n\n.earning-card {\n  display: flex;\n  align-items: center;\n  padding: 24px;\n  border-radius: 16px;\n  background: white;\n  border: 2px solid #f1f5f9;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.earning-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: var(--card-color);\n}\n\n.earning-card.primary {\n  --card-color: #3b82f6;\n}\n\n.earning-card.success {\n  --card-color: #10b981;\n}\n\n.earning-card.warning {\n  --card-color: #f59e0b;\n}\n\n.earning-card.info {\n  --card-color: #8b5cf6;\n}\n\n.earning-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);\n  border-color: var(--card-color);\n}\n\n.card-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  background: var(--card-color);\n}\n\n.card-content {\n  flex: 1;\n}\n\n.earning-number {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n  line-height: 1;\n}\n\n.earning-label {\n  font-size: 0.9rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n/* 佣金等级进度 */\n.commission-progress {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  margin-bottom: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.progress-card {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  padding: 32px;\n  border: 2px solid #e2e8f0;\n}\n\n.current-level {\n  margin-bottom: 24px;\n}\n\n.level-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.level-name {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.level-rate {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 6px 16px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 600;\n}\n\n.level-progress {\n  margin-bottom: 8px;\n}\n\n.progress-text {\n  text-align: center;\n  font-size: 0.9rem;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n.next-level {\n  padding-top: 24px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.next-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.next-text {\n  font-size: 1rem;\n  color: #374151;\n  font-weight: 500;\n}\n\n.next-rate {\n  background: #f3f4f6;\n  color: #6b7280;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.remaining {\n  font-size: 0.9rem;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 邀请工具 */\n.tools-section {\n  background: white;\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.tools-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: 32px;\n}\n\n.tool-card {\n  background: #fafbfc;\n  border: 2px solid #f1f5f9;\n  border-radius: 16px;\n  padding: 32px;\n  transition: all 0.3s ease;\n}\n\n.tool-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);\n}\n\n.tool-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 24px;\n}\n\n.tool-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n  color: white;\n  font-size: 20px;\n  flex-shrink: 0;\n}\n\n.tool-info h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #1f2937;\n}\n\n.tool-info p {\n  color: #6b7280;\n  line-height: 1.5;\n  margin: 0;\n}\n\n.tool-content {\n  margin-top: 16px;\n}\n\n.link-input {\n  width: 100%;\n}\n\n/* 二维码弹窗 */\n.qr-modal-content {\n  text-align: center;\n}\n\n.qr-code-container {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 2px dashed #d1d5db;\n}\n\n.qr-code-image {\n  max-width: 100%;\n  height: auto;\n  border-radius: 8px;\n}\n\n.qr-actions {\n  margin-top: 16px;\n}\n\n/* 提现弹窗 */\n.withdraw-modal-content {\n  padding: 8px 0;\n}\n\n.withdraw-info {\n  background: #f8fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 24px;\n  border: 1px solid #e2e8f0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-label {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.info-value {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.withdraw-actions {\n  text-align: right;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #f1f5f9;\n}\n\n.card-action {\n  margin-top: 8px;\n}\n\n/* 邀请链接区域 */\n.promotion-link-section {\n  background: white;\n  border-radius: 20px;\n  padding: 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  border: 2px solid #e2e8f0;\n}\n\n.link-main-container {\n  margin-bottom: 1.5rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.link-input-large {\n  flex: 1;\n}\n\n.link-input-large .ant-input {\n  font-size: 1.1rem;\n  padding: 16px 20px;\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  background: #f8fafc;\n  transition: all 0.3s ease;\n}\n\n.link-input-large .ant-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.link-actions {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.copy-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border: none;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.copy-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);\n  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n}\n\n.copy-btn:active {\n  transform: translateY(0);\n}\n\n.qr-btn {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  font-weight: 600;\n  border-radius: 12px;\n  padding: 12px 32px;\n  height: auto;\n  font-size: 16px;\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n  transition: all 0.3s ease;\n  min-width: 140px;\n}\n\n.qr-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  color: white;\n}\n\n.qr-btn:active {\n  transform: translateY(0);\n}\n\n/* 按钮禁用状态 */\n.copy-btn:disabled,\n.qr-btn:disabled {\n  background: #e5e7eb !important;\n  color: #9ca3af !important;\n  box-shadow: none !important;\n  transform: none !important;\n  cursor: not-allowed !important;\n}\n\n.copy-btn:disabled:hover,\n.qr-btn:disabled:hover {\n  background: #e5e7eb !important;\n  transform: none !important;\n  box-shadow: none !important;\n}\n\n.link-tips {\n  background: #f0f9ff;\n  border: 1px solid #bae6fd;\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: #0369a1;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.link-tips strong {\n  color: #1e40af;\n  font-weight: 700;\n}\n\n/* 分成规则表格 */\n.commission-rules {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.rules-table {\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.rule-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.rule-row:last-child {\n  border-bottom: none;\n}\n\n.rule-row.header {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.rule-row.vip {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  color: #475569;\n  font-weight: 500;\n  border: 1px solid #94a3b8;\n  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.2);\n}\n\n.rule-row.svip {\n  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);\n  color: #92400e;\n  font-weight: 600;\n  border: 1px solid #d97706;\n  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);\n}\n\n.rule-cell {\n  padding: 16px;\n  text-align: center;\n  border-right: 1px solid #e2e8f0;\n}\n\n.rule-cell:last-child {\n  border-right: none;\n}\n\n.rule-cell.highlight {\n  font-weight: 700;\n  color: #dc2626;\n  font-size: 1.1rem;\n}\n\n/* 表格容器 */\n.users-table-container,\n.records-table-container {\n  background: white;\n  border-radius: 20px;\n  padding: 2rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.referral-users,\n.withdraw-records {\n  margin-bottom: 3rem;\n}\n\n.reward-amount,\n.withdraw-amount {\n  font-weight: 600;\n  color: #059669;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-title {\n    font-size: 2rem;\n  }\n\n  .page-subtitle {\n    font-size: 1rem;\n  }\n\n  .commission-badge {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .earnings-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tools-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .earnings-dashboard,\n  .commission-progress,\n  .tools-section {\n    padding: 24px;\n  }\n\n  .tool-card {\n    padding: 24px;\n  }\n\n  .progress-card {\n    padding: 24px;\n  }\n\n  .level-info,\n  .next-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .tool-header {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .tool-icon {\n    margin: 0 auto 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .affiliate-section {\n    padding: 0 16px 60px;\n  }\n\n  .page-header {\n    padding: 40px 16px 24px;\n  }\n\n  .earning-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .card-icon {\n    margin: 0 auto 12px;\n  }\n\n  .promotion-link-section {\n    padding: 1.5rem;\n    margin-bottom: 2rem;\n  }\n\n  .link-main-container {\n    gap: 1rem;\n  }\n\n  .link-input-large .ant-input {\n    font-size: 1rem;\n    padding: 14px 16px;\n  }\n\n  .link-actions {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .copy-btn,\n  .qr-btn {\n    width: 100%;\n    min-width: auto;\n    padding: 14px 24px;\n    font-size: 15px;\n  }\n\n  .rule-row {\n    grid-template-columns: 1fr;\n    text-align: left;\n  }\n\n  .rule-cell {\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    text-align: left;\n  }\n\n  .rule-cell:last-child {\n    border-bottom: none;\n  }\n\n  .promotion-link-section,\n  .commission-rules,\n  .users-table-container,\n  .records-table-container {\n    padding: 1.5rem;\n  }\n}\n\n/* 水平等级时间线样式 */\n.level-timeline-horizontal {\n  position: relative;\n  padding: 20px 0;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  margin-bottom: 20px;\n}\n\n.level-step-horizontal {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  flex: 1;\n  text-align: center;\n  min-width: 0;\n  padding: 0 10px;\n}\n\n.step-circle-horizontal {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background-color: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  z-index: 2;\n  border: 2px solid #e8e8e8;\n  position: relative;\n  flex-shrink: 0;\n}\n\n.level-step-horizontal.completed .step-circle-horizontal {\n  background-color: #52c41a;\n  border-color: #52c41a;\n  color: white;\n}\n\n.level-step-horizontal.current .step-circle-horizontal {\n  background-color: #1890ff;\n  border-color: #1890ff;\n  color: white;\n}\n\n.level-step-horizontal.upcoming .step-circle-horizontal {\n  border-color: #d9d9d9;\n}\n\n.step-circle-horizontal .step-number {\n  font-size: 14px;\n  font-weight: 600;\n  color: #666;\n}\n\n.step-content-horizontal {\n  padding: 0 5px;\n  max-width: 120px;\n}\n\n.step-content-horizontal .step-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n  white-space: nowrap;\n}\n\n.step-content-horizontal .step-rate {\n  color: #1890ff;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.level-step-horizontal.completed .step-content-horizontal .step-rate {\n  color: #52c41a;\n}\n\n.step-content-horizontal .step-requirement {\n  color: #666;\n  margin-bottom: 4px;\n  font-size: 13px;\n}\n\n.step-content-horizontal .step-remaining {\n  color: #ff4d4f;\n  font-size: 12px;\n}\n\n.step-content-horizontal .step-completed {\n  color: #52c41a;\n  font-size: 12px;\n}\n\n.step-line-horizontal {\n  position: absolute;\n  left: calc(50% + 18px);\n  width: calc(100% - 36px);\n  top: 18px;\n  height: 2px;\n  background-color: #e8e8e8;\n  z-index: 1;\n}\n\n.level-step-horizontal.completed .step-line-horizontal {\n  background-color: #52c41a;\n}\n\n.level-step-horizontal:last-child .step-line-horizontal {\n  display: none;\n}\n</style>\n"], "sourceRoot": "src/views/website/affiliate"}]}